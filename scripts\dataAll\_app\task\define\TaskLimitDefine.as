package dataAll._app.task.define
{
   import com.sounto.utils.ClassProperty;
   
   public class TaskLimitDefine
   {
      public static var pro_arr:Array = null;
      
      public var vehicleB:Boolean = true;
      
      public var skillB:Boolean = true;
      
      public var propsB:Boolean = true;
      
      public function TaskLimitDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
   }
}


package dataAll.equip.define
{
   import com.sounto.oldUtils.OldNiuBiCF;
   
   public class EquipSuitPropertyMul
   {
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var name:String = "";
      
      public var mul:Number = 0;
      
      public var index:int = 0;
      
      public function EquipSuitPropertyMul()
      {
         super();
         this.value = 0;
      }
      
      public function get value() : Number
      {
         return this.CF.getAttribute("value");
      }
      
      public function set value(v0:Number) : void
      {
         this.CF.setAttribute("value",v0);
      }
      
      public function inData_byStr(str0:String, index0:int) : void
      {
         var valueB0:Boolean = str0.indexOf(":") >= 0;
         var strArr0:Array = str0.split(valueB0 ? ":" : "_");
         this.name = strArr0[0];
         if(valueB0)
         {
            this.value = Number(strArr0[1]);
         }
         else
         {
            this.mul = Number(strArr0[1]);
         }
         this.index = index0;
      }
   }
}


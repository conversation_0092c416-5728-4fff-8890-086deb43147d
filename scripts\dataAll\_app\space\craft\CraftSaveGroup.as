package dataAll._app.space.craft
{
   import com.sounto.utils.ClassProperty;
   import dataAll._base.OneSave;
   import dataAll._base.OneSaveGroup;
   
   public class CraftSaveGroup extends OneSaveGroup
   {
      public static var pro_arr:Array = null;
      
      public static var mePro_arr:Array = null;
      
      public var now:String = "";
      
      public function CraftSaveGroup()
      {
         super();
         saveClass = CraftSave;
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObj(obj0);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
      }
      
      override public function addSave(s0:OneSave) : void
      {
         super.addSave(s0);
         var cs0:CraftSave = s0 as CraftSave;
      }
   }
}


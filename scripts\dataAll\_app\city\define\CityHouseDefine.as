package dataAll._app.city.define
{
   import com.sounto.utils.ClassProperty;
   
   public class CityHouseDefine extends CityBodyDefine
   {
      public static var pro_arr:Array = null;
      
      public static var mePro_arr:Array = null;
      
      public var link:String = "";
      
      public function CityHouseDefine()
      {
         super();
      }
      
      override public function inData_byXML(xml0:XML, father0:String = "") : void
      {
         super.inData_byXML(xml0,father0);
         ClassProperty.inData_byXMLAt(this,xml0,mePro_arr);
         if(imgUrl == "")
         {
            imgUrl = "CityUI/" + name;
         }
      }
      
      override protected function getFinalProArr() : Array
      {
         return pro_arr;
      }
   }
}


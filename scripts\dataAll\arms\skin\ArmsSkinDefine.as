package dataAll.arms.skin
{
   import com.sounto.cf.NiuBiCF;
   import dataAll._base.NormalDefine;
   import dataAll.arms.creator.ArmsEvoCtrl;
   import dataAll.arms.creator.GunImageCreator;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.gift.define.IO_GiftDefine;
   import dataAll.items.define.IO_ItemsDefine;
   
   public class ArmsSkinDefine extends NormalDefine implements IO_GiftDefine
   {
      public static const UID_ARR:Array = ["1723588218","596719805","1584967666","760597284","3990886350","411786252","1560415328","1512974212","3801078226","3223756609","2043501275","3439662462","1620650750","139888642","1853181226","3594229545","2607333339","247904824","511159787","551598532","3336554617","1676180080","107396537","189752643","1587464098","654612145","1192018625","2572411060","3789272881","881607617","1676180080","2734366043","498942159","1460760168","644937708","3246188122","3588120315","2296141867","3996715415","2579763438","896732812","1433974107","1423556554","2714641779"];
      
      public static var pro_arr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var gather:String = "";
      
      public var url:String = "";
      
      public var icon:String = "";
      
      public var color:String = "";
      
      public var items:String = "";
      
      public var lightColor:uint = 0;
      
      public var bulletColor:uint = 0;
      
      public var goods:String = "";
      
      public var info:String = "";
      
      public var author:String = "";
      
      public var au:String = "";
      
      public var other:String = "";
      
      public function ArmsSkinDefine()
      {
         super();
      }
      
      public function get evoLv() : Number
      {
         return this.CF.getAttribute("evoLv");
      }
      
      public function set evoLv(v0:Number) : void
      {
         this.CF.setAttribute("evoLv",v0);
      }
      
      public function get vip() : Number
      {
         return this.CF.getAttribute("vip");
      }
      
      public function set vip(v0:Number) : void
      {
         this.CF.setAttribute("vip",v0);
      }
      
      public function get p() : Number
      {
         return this.CF.getAttribute("p");
      }
      
      public function set p(v0:Number) : void
      {
         this.CF.setAttribute("p",v0);
      }
      
      public function get stren() : Number
      {
         return this.CF.getAttribute("stren");
      }
      
      public function set stren(v0:Number) : void
      {
         this.CF.setAttribute("stren",v0);
      }
      
      override public function inData_byXML(xml0:XML, father0:String = "") : void
      {
         super.inData_byXML(xml0,father0);
      }
      
      override protected function getBaseClassProArr() : Array
      {
         return pro_arr;
      }
      
      override protected function getFinalProArr() : Array
      {
         return pro_arr;
      }
      
      public function isArmsB() : Boolean
      {
         return this.gather == "arms";
      }
      
      public function isWeaponB() : Boolean
      {
         return this.gather == "weapon";
      }
      
      public function isPlayerOriginal() : Boolean
      {
         return this.au == "original";
      }
      
      public function getUrl() : String
      {
         if(this.url == "")
         {
            if(this.isArmsB())
            {
               this.url = "specialGun/" + name;
            }
            else if(this.isWeaponB())
            {
               this.url = "weapon/" + name;
            }
         }
         return this.url;
      }
      
      public function getIconUrl() : String
      {
         if(this.icon == "")
         {
            if(this.isArmsB())
            {
               this.icon = GunImageCreator.getArmsImgLabelByUrl(this.getUrl());
            }
            else if(this.isWeaponB())
            {
               this.icon = this.getUrl() + "_icon";
            }
            else
            {
               this.icon = this.getUrl();
            }
         }
         return this.icon;
      }
      
      public function getBigIconRotation() : Number
      {
         if(this.isWeaponB())
         {
            return -90;
         }
         return 0;
      }
      
      public function getBigIconUrl() : String
      {
         if(this.isArmsB())
         {
            return this.getIconUrl();
         }
         return this.getUrl();
      }
      
      public function getEvoCn() : String
      {
         var armsD0:ArmsDefine = null;
         var cn0:String = null;
         if(this.evoLv > 0)
         {
            if(this.isArmsB())
            {
               armsD0 = Gaming.defineGroup.bullet.getArmsDefineInRange(father);
               cn0 = ArmsEvoCtrl.getCnName("",this.evoLv,armsD0,true);
               return String(cn0).replace("·","");
            }
            return this.evoLv + "级";
         }
         return "";
      }
      
      public function getMustItemsDefine() : IO_ItemsDefine
      {
         if(this.items != "")
         {
            return Gaming.defineGroup.getItemsDefine(this.items);
         }
         return null;
      }
      
      public function getGiftCn() : String
      {
         return cnName;
      }
      
      public function getGiftTip() : String
      {
         return "";
      }
      
      public function getGiftIconUrl() : String
      {
         return this.getIconUrl();
      }
   }
}


package w_test.cheating
{
   import UI.pet.PetUI;
   import dataAll._app.space.craft.CraftData;
   import dataAll.pet.PetData;
   import dataAll.pet.gene.define.GeneDefine;
   
   public class PetCheating extends OneCheating
   {
      public function PetCheating()
      {
         super();
      }
      
      public function addNowCraftExp(str0:String, v0:int) : String
      {
         Gaming.PG.da.space.addNowCraftExp(v0);
         return "添加飞船经验值：" + v0;
      }
      
      public function setNowCraftLv(str0:String, v0:int) : String
      {
         var da0:CraftData = Gaming.PG.da.space.getNowCraft();
         if(<PERSON><PERSON>an(da0))
         {
            da0.setLevel(v0);
            return da0.getCnName() + " 设置飞船等级：" + v0;
         }
         return "";
      }
      
      public function clearAllPet(str0:String, v0:int) : String
      {
         Gaming.PG.da.pet.clearData();
         return "清除所有宠物";
      }
      
      public function setPetLv(str0:String, v0:int) : String
      {
         var da0:PetData = PetUI.getNowData();
         if(da0 is PetData)
         {
            da0.base.save.level = v0;
         }
         return "设置当前尸宠等级为：" + v0;
      }
      
      public function addNowGenePro(str0:String, v0:int) : String
      {
         var d0:GeneDefine = Gaming.uiGroup.petUI.bookBoard.nowGeneDefine;
         if(Boolean(d0))
         {
            Gaming.PG.da.pet.saveGroup.map.setDropPro(d0.name,v0 / 100,true);
         }
         return "添加" + d0.cnName + "基因体掉落概率：" + v0 / 100;
      }

      public function debugPetSystem(str0:String, v0:int) : String
      {
         var debugInfo:Array = [];

         try
         {
            debugInfo.push("=== 宠物系统调试信息 ===");

            // 检查基本组件
            debugInfo.push("\n1. 基本组件检查:");
            debugInfo.push("Gaming.defineGroup: " + (Gaming.defineGroup ? "✅" : "❌"));
            debugInfo.push("Gaming.defineGroup.gene: " + (Gaming.defineGroup && Gaming.defineGroup.gene ? "✅" : "❌"));
            debugInfo.push("Gaming.defineGroup.geneCreator: " + (Gaming.defineGroup && Gaming.defineGroup.geneCreator ? "✅" : "❌"));
            debugInfo.push("Gaming.PG.da.pet: " + (Gaming.PG && Gaming.PG.da && Gaming.PG.da.pet ? "✅" : "❌"));

            // 检查宠物背包状态
            if(Gaming.PG && Gaming.PG.da && Gaming.PG.da.pet)
            {
               debugInfo.push("\n2. 宠物背包状态:");
               debugInfo.push("当前宠物数量: " + Gaming.PG.da.pet.arr.length);
               debugInfo.push("背包容量: " + Gaming.PG.da.pet.saveGroup.lockLen);
               debugInfo.push("剩余空间: " + Gaming.PG.da.pet.getSpaceNum());
            }

            // 检查基因定义
            if(Gaming.defineGroup && Gaming.defineGroup.gene)
            {
               debugInfo.push("\n3. 基因定义检查:");
               try
               {
                  var normalNames:Array = Gaming.defineGroup.gene.getNormalNameArr();
                  debugInfo.push("正常宠物数量: " + (normalNames ? normalNames.length : 0));
                  if(normalNames && normalNames.length > 0)
                  {
                     debugInfo.push("前5个宠物: " + normalNames.slice(0, 5).join(", "));
                  }
               }
               catch(e1:Error)
               {
                  debugInfo.push("获取正常宠物失败: " + e1.message);
               }

               // 测试特定宠物
               var testPets:Array = ["BoomSkull", "IronChief", "Lake"];
               debugInfo.push("\n4. 测试宠物定义:");
               for each(var testPet:String in testPets)
               {
                  try
                  {
                     var testDefine:* = Gaming.defineGroup.gene.getDefine(testPet);
                     debugInfo.push(testPet + ": " + (testDefine ? "✅ " + testDefine.cnName : "❌ 未找到"));
                  }
                  catch(e2:Error)
                  {
                     debugInfo.push(testPet + ": ❌ 异常 - " + e2.message);
                  }
               }
            }

            // 检查基因创建器
            if(Gaming.defineGroup && Gaming.defineGroup.geneCreator)
            {
               debugInfo.push("\n5. 基因创建器测试:");
               try
               {
                  var testSave:* = Gaming.defineGroup.geneCreator.getSave("red", 1, "BoomSkull", true);
                  debugInfo.push("创建基因保存: " + (testSave ? "✅" : "❌"));

                  if(testSave)
                  {
                     var testData:* = Gaming.defineGroup.geneCreator.getTempData(testSave);
                     debugInfo.push("创建基因数据: " + (testData ? "✅" : "❌"));
                  }
               }
               catch(e3:Error)
               {
                  debugInfo.push("基因创建器测试失败: " + e3.message);
               }
            }

            return debugInfo.join("\n");
         }
         catch(e:Error)
         {
            return "调试失败: " + e.message + "\n堆栈: " + e.getStackTrace();
         }
      }

      public function addAllPet(str0:String, v0:int) : String
      {
         var addedGenes:int = 0;
         var failedGenes:int = 0;
         var debugInfo:Array = [];

         try
         {
            // 强制启用GM功能
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;

            // 扩展基因背包空间（基因体存放在基因背包中）
            if(Gaming.PG.da.geneBag)
            {
               Gaming.PG.da.geneBag.addBagNum(200);
               debugInfo.push("扩展基因背包到: " + Gaming.PG.da.geneBag.saveGroup.lockLen);
            }
            else
            {
               debugInfo.push("警告: 基因背包不存在，尝试扩展宠物背包");
               Gaming.PG.da.pet.addBagNum(200);
            }

            // 使用多种方法获取宠物名称
            var normalPetNames:Array = [];

            // 方法1: 尝试获取正常宠物名称
            try
            {
               var tempNames:Array = Gaming.defineGroup.gene.getNormalNameArr();
               if(tempNames && tempNames.length > 0)
               {
                  normalPetNames = tempNames;
                  debugInfo.push("方法1成功: 找到 " + normalPetNames.length + " 个宠物");
               }
            }
            catch(e1:Error)
            {
               debugInfo.push("方法1失败: " + e1.message);
            }

            // 方法2: 如果方法1失败，使用备用列表
            if(normalPetNames.length == 0)
            {
               normalPetNames = [
                  "BoomSkull", "IronChief", "Lake", "FightWolf", "ZombieWolf"
               ];
               debugInfo.push("方法2: 使用备用宠物列表 " + normalPetNames.length + " 个");
            }

            // 方法3: 如果还是没有，尝试从定义组直接获取
            if(normalPetNames.length == 0)
            {
               try
               {
                  if(Gaming.defineGroup && Gaming.defineGroup.gene && Gaming.defineGroup.gene.obj)
                  {
                     for(var id:String in Gaming.defineGroup.gene.obj)
                     {
                        var def:* = Gaming.defineGroup.gene.obj[id];
                        if(def && def.cnName)
                        {
                           normalPetNames.push(id);
                           if(normalPetNames.length >= 10) break; // 限制数量
                        }
                     }
                     debugInfo.push("方法3: 从定义组获取 " + normalPetNames.length + " 个宠物");
                  }
               }
               catch(e3:Error)
               {
                  debugInfo.push("方法3失败: " + e3.message);
               }
            }

            for each(var petName:String in normalPetNames)
            {
               try
               {
                  // 检查基因定义是否存在
                  var geneDefine:* = Gaming.defineGroup.gene.getDefine(petName);
                  if(!geneDefine)
                  {
                     debugInfo.push("跳过未定义宠物: " + petName);
                     failedGenes++;
                     continue;
                  }

                  // 方法1: 尝试直接添加基因体到基因背包
                  var success:Boolean = false;
                  try
                  {
                     if(Gaming.PG.da.geneBag)
                     {
                        // 确保基因背包有足够空间
                        if(Gaming.PG.da.geneBag.getSpaceNum() <= 0)
                        {
                           Gaming.PG.da.geneBag.addBagNum(10);
                        }

                        // 尝试添加基因体
                        var geneItemData:* = Gaming.PG.da.geneBag.addByName(petName, 1);
                        if(geneItemData)
                        {
                           addedGenes++;
                           debugInfo.push("✅ 成功添加基因体: " + petName);
                           success = true;
                        }
                     }
                  }
                  catch(e1:Error)
                  {
                     debugInfo.push("方法1失败 " + petName + ": " + e1.message);
                  }

                  // 方法2: 如果方法1失败，尝试通过GM命令添加
                  if(!success)
                  {
                     try
                     {
                        Gaming.testCtrl.cheating.doOrder("item", "addItem", petName, 1);
                        addedGenes++;
                        debugInfo.push("✅ 通过GM命令添加: " + petName);
                        success = true;
                     }
                     catch(e2:Error)
                     {
                        debugInfo.push("方法2失败 " + petName + ": " + e2.message);
                     }
                  }

                  // 方法3: 如果前面都失败，尝试添加到物品背包
                  if(!success)
                  {
                     try
                     {
                        if(Gaming.PG.da.itemBag)
                        {
                           Gaming.PG.da.itemBag.addBagNum(10);
                           var itemData:* = Gaming.PG.da.itemBag.addByName(petName, 1);
                           if(itemData)
                           {
                              addedGenes++;
                              debugInfo.push("✅ 添加到物品背包: " + petName);
                              success = true;
                           }
                        }
                     }
                     catch(e3:Error)
                     {
                        debugInfo.push("方法3失败 " + petName + ": " + e3.message);
                     }
                  }

                  if(!success)
                  {
                     failedGenes++;
                     debugInfo.push("❌ 所有方法都失败: " + petName);
                  }
               }
               catch(geneError:Error)
               {
                  failedGenes++;
                  debugInfo.push("❌ 基因异常 " + petName + ": " + geneError.message);
               }
            }

            // 刷新相关UI
            try
            {
               // 刷新基因背包UI
               if(Gaming.uiGroup.geneBagUI && Gaming.uiGroup.geneBagUI.visible)
               {
                  Gaming.uiGroup.geneBagUI.fleshData();
               }

               // 刷新物品背包UI
               if(Gaming.uiGroup.itemBagUI && Gaming.uiGroup.itemBagUI.visible)
               {
                  Gaming.uiGroup.itemBagUI.fleshData();
               }

               // 刷新宠物UI
               if(Gaming.uiGroup.petUI && Gaming.uiGroup.petUI.visible)
               {
                  Gaming.uiGroup.petUI.fleshData();
               }
            }
            catch(uiError:Error)
            {
               debugInfo.push("UI刷新失败: " + uiError.message);
            }

            var result:String = "🧬 添加基因体完成：成功 " + addedGenes + " 个，失败 " + failedGenes + " 个";
            result += "\n💡 提示：基因体已添加到背包，需要孵化才能获得宠物";
            if(debugInfo.length > 0)
            {
               result += "\n📋 调试信息:\n" + debugInfo.slice(0, 15).join("\n");
            }
            return result;
         }
         catch(e:Error)
         {
            return "❌ 添加基因体失败：" + e.message + "\n堆栈: " + e.getStackTrace();
         }
      }
   }
}


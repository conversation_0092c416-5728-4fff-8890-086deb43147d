package dataAll._app.active
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   
   public class ActiveSave
   {
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var haveGiftObj:Object = {};
      
      public var acc25_6:AccActiveSave = new AccActiveSave();
      
      public function ActiveSave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.haveGiftObj = ClassProperty.copyObj(obj0["haveGiftObj"]);
      }
      
      public function getAcc() : AccActiveSave
      {
         return this.acc25_6;
      }
      
      public function getAccShowB() : Boolean
      {
         return true;
      }
      
      public function newDayCtrl() : void
      {
         this.haveGiftObj = {};
         this.getAcc().newDayCtrl();
      }
      
      public function getHaveGiftB(name0:String) : Boolean
      {
         if(this.haveGiftObj.hasOwnProperty(name0))
         {
            return this.haveGiftObj[name0];
         }
         return false;
      }
      
      public function setHaveGiftB(name0:String, bb0:Boolean) : void
      {
         this.haveGiftObj[name0] = bb0;
      }
   }
}


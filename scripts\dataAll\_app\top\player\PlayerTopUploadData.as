package dataAll._app.top.player
{
   import UI.test.SaveTestBox;
   import com.adobe.serialization.json.JSON2;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.edit.boss.BossEditData;
   import dataAll._app.love.LoveData;
   import dataAll._app.top.define.TopBarDefineGroup;
   import dataAll._app.top.extra.ArenaTopExtra;
   import dataAll._app.top.extra.ArmsTopExtra;
   import dataAll._app.top.extra.BossEditTopExtra;
   import dataAll._app.top.extra.DpsTopExtra;
   import dataAll._app.top.extra.TopExtra;
   import dataAll._player.PlayerData;
   import dataAll._player.count.PlayerCountSave;
   import dataAll._player.more.MoreData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll._player.role.RoleName;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsType;
   import dataAll.pet.PetData;
   import dataAll.test.ZuoBiMax;
   
   public class PlayerTopUploadData
   {
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var rId:int = 0;
      
      public var extra:String = "";
      
      public function PlayerTopUploadData()
      {
         super();
         this.score = 0;
      }
      
      public function get score() : Number
      {
         return this.CF.getAttribute("score");
      }
      
      public function set score(v0:Number) : void
      {
         this.CF.setAttribute("score",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function inData_byPlayerData(type_d0:TopBarDefineGroup, pd0:PlayerData, initScore0:Number = -1) : void
      {
         if(initScore0 >= 0)
         {
            this.score = initScore0;
         }
         var type0:String = type_d0.uploadType;
         var obj0:Object = {};
         var count0:PlayerCountSave = pd0.getSave().getCount();
         if(count0.hasOwnProperty(type_d0.uploadType))
         {
            obj0 = this.dps_extra(type_d0,pd0);
            this.score = count0[type_d0.uploadType];
            if(type_d0.uploadType == "killNum")
            {
               this.score = pd0.moreWay.getAll_countNum("killNum");
            }
         }
         else if(ArmsType.TYPE_ARR.indexOf(type_d0.uploadType) >= 0)
         {
            obj0 = this.armsType_extra(type_d0,pd0);
         }
         else
         {
            obj0 = this[type_d0.uploadType + "_extra"](type_d0,pd0);
         }
         this.score = Math.round(this.score * type_d0.scoreMul);
         var extraD0:TopExtra = obj0 as TopExtra;
         if(Boolean(extraD0))
         {
            extraD0.m = TopExtra.getMByUidScore(Gaming.PG.getUid(),this.score);
            if(Gaming.testCtrl.canCheatingB())
            {
               SaveTestBox.addText("uid:" + Gaming.PG.getUid());
               SaveTestBox.addText("score:" + this.score);
               SaveTestBox.addText("m:" + extraD0.m);
            }
         }
         this.extra = JSON2.encode(obj0);
         this.rId = type_d0.id;
      }
      
      private function dps_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         return this.body_extra(d0,pd0,pd0.base.save.playerName);
      }
      
      private function life_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         var obj0:Object = this.body_extra(d0,pd0,pd0.base.save.playerName);
         this.score = pd0.base.getMaxLife();
         return obj0;
      }
      
      private function headDefence_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         var obj0:Object = this.body_extra(d0,pd0,pd0.base.save.playerName);
         this.score = pd0.base.getHeadDefence();
         return obj0;
      }
      
      private function lifeRate_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         var obj0:Object = this.body_extra(d0,pd0,pd0.base.save.playerName);
         this.score = pd0.base.getLifeRate();
         return obj0;
      }
      
      private function loveValue_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         var obj0:Object = this.body_extra(d0,pd0,pd0.base.save.playerName);
         var loveDa0:LoveData = pd0.getGirlLoveNull();
         if(Boolean(loveDa0))
         {
            this.score = loveDa0.getValue();
         }
         else
         {
            this.score = 0;
         }
         return obj0;
      }
      
      private function coin_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         var obj0:Object = this.body_extra(d0,pd0,pd0.base.save.playerName);
         this.score = pd0.main.save.coin;
         return obj0;
      }
      
      private function maxCompleteAchieve_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         var obj0:Object = this.body_extra(d0,pd0,pd0.base.save.playerName);
         this.score = pd0.achieve.getCompleteNum();
         return obj0;
      }
      
      private function Girl_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         return this.more_extra(RoleName.Girl,d0,pd0);
      }
      
      private function WenJie_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         return this.more_extra(RoleName.WenJie,d0,pd0);
      }
      
      private function ZangShi_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         return this.more_extra(RoleName.ZangShi,d0,pd0);
      }
      
      private function more_extra(name0:String, d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         var more0:NormalPlayerData = null;
         var moreData0:MoreData = pd0.getMoreDataByName(name0);
         if(Boolean(moreData0))
         {
            more0 = moreData0.DATA;
            return this.body_extra(d0,more0,pd0.base.save.playerName);
         }
         return new DpsTopExtra();
      }
      
      private function body_extra(d0:TopBarDefineGroup, pd0:NormalPlayerData, playerName0:String) : Object
      {
         var zuobi0:Boolean = false;
         var obj0:DpsTopExtra = new DpsTopExtra();
         obj0.dps = pd0.getDps();
         obj0.headDef = pd0.base.getHeadDefence();
         obj0.life = pd0.base.getMaxLife();
         obj0.lv = pd0.level;
         obj0.player = playerName0;
         obj0.role = pd0.getRoleName();
         obj0.equip = pd0.equip.getFourEquipImgNameArr().toString();
         obj0.veh = pd0.equip.getVehicleName();
         obj0.es = pd0.equip.getStrengthenLightLvTop();
         if(pd0 is PlayerData)
         {
            obj0.head = (pd0 as PlayerData).head.save.nowHead;
            obj0.post = (pd0 as PlayerData).post.getTopStr();
            obj0.coin = (pd0 as PlayerData).main.save.coin;
         }
         if(pd0 is PlayerData)
         {
            zuobi0 = obj0.dps > ZuoBiMax.dps;
            if(zuobi0)
            {
               obj0.dps = 0;
               this.score = 0;
            }
         }
         this.score = obj0.dps;
         return obj0;
      }
      
      private function endlessScore_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         var obj0:Object = this.body_extra(d0,pd0,pd0.base.save.playerName);
         this.score = pd0.worldMap.getMaxEndlessScore();
         return obj0;
      }
      
      private function petDps_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         var da0:PetData = pd0.pet.getBestPetData("dps");
         if(Boolean(da0))
         {
            return this.pet_extra(d0,da0,pd0.base.save.playerName,da0.base.getDps());
         }
         return new DpsTopExtra();
      }
      
      private function petLife_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         var da0:PetData = pd0.pet.getBestPetData("life");
         if(Boolean(da0))
         {
            return this.pet_extra(d0,da0,pd0.base.save.playerName,da0.base.getMaxLife());
         }
         return new DpsTopExtra();
      }
      
      private function pet_extra(d0:TopBarDefineGroup, petData0:PetData, playerName0:String, score0:Number) : Object
      {
         var obj0:DpsTopExtra = new DpsTopExtra();
         obj0.dps = petData0.base.getDps();
         obj0.headDef = petData0.base.getHeadDefence();
         obj0.life = petData0.base.getMaxLife();
         obj0.lv = petData0.base.save.level;
         obj0.player = playerName0;
         obj0.equip = petData0.base.save.playerName;
         this.score = score0;
         return obj0;
      }
      
      private function armsType_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         return this.arms_extra(d0.uploadType,d0,pd0);
      }
      
      private function arms_extra(armsType0:String, d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         var obj0:ArmsTopExtra = new ArmsTopExtra();
         var arms_da0:ArmsData = pd0.getArmsData_MaxDpsByType(armsType0);
         if(arms_da0 is ArmsData)
         {
            obj0.name = arms_da0.save.cnName;
            obj0.lv = arms_da0.save.getTrueLevel();
            obj0.color = arms_da0.save.getColorCnName();
            obj0.dps = arms_da0.getUIShowDps();
            obj0.hurt = arms_da0.hurtRatio;
            obj0.ca = arms_da0.capacity;
            obj0.img = arms_da0.getIconImgUrl();
            obj0.sGap = ComMethod.toFixed(1 / arms_da0.attackGap,1) + "发/秒";
            obj0.player = pd0.base.save.playerName;
            if(obj0.dps > ZuoBiMax.armsDps)
            {
               obj0.dps = 0;
            }
            this.score = obj0.dps;
         }
         return obj0;
      }
      
      private function arena_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         var obj2:DpsTopExtra = this.body_extra(d0,pd0,pd0.base.save.playerName) as DpsTopExtra;
         var obj0:ArenaTopExtra = new ArenaTopExtra();
         obj0.inData_byObj(obj2);
         obj0.skill = pd0.skill.getNameArr().toString();
         obj0.uname = Gaming.PG.loginData.name;
         this.score = pd0.arena.save.score;
         if(pd0.arena.overZuobiPan())
         {
            this.score = 1;
         }
         return obj0;
      }
      
      private function askScore_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         var obj0:Object = this.dps_extra(d0,pd0);
         this.score = pd0.ask.getScore();
         return obj0;
      }
      
      private function unionBattle_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         return null;
      }
      
      private function bossEdit_extra(d0:TopBarDefineGroup, pd0:PlayerData) : Object
      {
         var e0:BossEditTopExtra = null;
         var da0:BossEditData = pd0.bossEdit.getMain();
         if(Boolean(da0))
         {
            return da0.getTopExtra(Gaming.PG);
         }
         this.score = 0;
         return {};
      }
   }
}


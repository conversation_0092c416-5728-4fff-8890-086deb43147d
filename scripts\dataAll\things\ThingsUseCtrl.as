package dataAll.things
{
   import UI.UIOrder;
   import UI.UIShow;
   import UI.api.count.ShopCountObj;
   import UI.bag.ItemsGripBtnListCtrl;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.edit.boss.BossEditData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._player.PlayerData;
   import dataAll._player.base.PlayerMainData;
   import dataAll._player.define.MainPlayerType;
   import dataAll._player.role.RoleName;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.BagSpaceMust;
   import dataAll.things.define.ThingsDefine;
   import dataAll.things.define.ThingsEffectDefine;
   import dataAll.things.define.ThingsName;
   import dataAll.things.define.ThingsUseSave;
   import gameAll.body.IO_NormalBody;
   import gameAll.hero.HeroBody;
   import gameAll.more.DoubleCtrl;
   
   public class ThingsUseCtrl
   {
      private static var nowDg:ThingsDataGroup = null;
      
      private static var nowName:String = "";
      
      private static var nowNum:int = 0;
      
      public function ThingsUseCtrl()
      {
         super();
      }
      
      public static function getUseMax() : int
      {
         return 1000;
      }
      
      public static function useThings(dg0:ThingsDataGroup, name0:String, num0:int = 1, doEffectB0:Boolean = false, tipType0:String = "", mainPlayerType0:String = "me") : Boolean
      {
         var saveType0:String = null;
         var bb0:Boolean = false;
         var loginB0:Boolean = false;
         if(num0 < 1)
         {
            return false;
         }
         var da0:ThingsData = dg0.getDataBySaveName(name0) as ThingsData;
         if(da0 is ThingsData)
         {
            saveType0 = da0.getAffterUseSaveType();
            if(saveType0 != ThingsUseSave.no)
            {
               loginB0 = Gaming.PG.loginData.isLoginByJS();
               if(!loginB0)
               {
                  Gaming.uiGroup.alertBox.showNormal("您的账号已经退出登录，无法进行此操作。","yes",null,null,"no");
                  return false;
               }
            }
            bb0 = useThingsNoFlesh(dg0,da0,num0,doEffectB0,tipType0);
            if(bb0 && mainPlayerType0 == MainPlayerType.ME)
            {
               if(saveType0 != ThingsUseSave.shopAuto)
               {
                  ItemsGripBtnListCtrl.fleshAllBy(dg0);
                  Gaming.soundGroup.playSound("uiSound","getItems");
                  Gaming.uiGroup.gameWorldUI.useThingsEvent();
               }
               if(saveType0 == ThingsUseSave.box)
               {
                  ThingsProps.clearThingsTip();
                  UIOrder.save(true);
               }
               else if(saveType0 == ThingsUseSave.chest)
               {
                  UIOrder.save(true,true,true,ThingsProps.randomThingsTip,UIShow.hideNowApp,false,true);
               }
               else if(saveType0 == ThingsUseSave.shopAuto)
               {
                  ThingsProps.randomThingsTip(null,true);
               }
               else
               {
                  ThingsProps.randomThingsTip(null);
               }
            }
            return bb0;
         }
         return false;
      }
      
      private static function useThingsNoFlesh(dg0:ThingsDataGroup, da0:ThingsData, num0:int = 1, doEffectB0:Boolean = false, tipType0:String = "") : Boolean
      {
         var limit0:int = 0;
         var limitStr0:String = null;
         var continueB0:Boolean = false;
         var name0:String = da0.save.name;
         if(da0 is ThingsData)
         {
            limit0 = dg0.playerData.getThingsLevelUseLimitNum(name0);
            if(limit0 > 0)
            {
               num0 = useNumPan(dg0,da0,num0);
               limitStr0 = useLimit(dg0,da0,num0);
               if(limitStr0 != "")
               {
                  if(tipType0 == "tip")
                  {
                     Gaming.uiGroup.gameWorldUI.tipBox.showText(limitStr0);
                  }
                  else if(tipType0 != "no")
                  {
                     Gaming.uiGroup.alertBox.showError(limitStr0);
                  }
                  return false;
               }
               continueB0 = effectBagPan(dg0,da0,num0,doEffectB0);
               if(!continueB0)
               {
                  return false;
               }
               dg0.useThings(name0,num0,doEffectB0);
               return true;
            }
            return false;
         }
         return false;
      }
      
      private static function useNumPan(dg0:ThingsDataGroup, da0:ThingsData, num0:int) : int
      {
         var d0:ThingsDefine = da0.save.getDefine();
         var max0:Number = getUseMax();
         if(num0 > max0)
         {
            num0 = max0;
         }
         var level0:int = dg0.getLevelCanUseNum(d0.name);
         if(ThingsEffectDefine.haveLimitPan(level0))
         {
            if(num0 > level0)
            {
               num0 = level0;
            }
         }
         var day0:int = dg0.getDayCanUseNum(d0.name);
         if(ThingsEffectDefine.haveLimitPan(day0))
         {
            if(num0 > day0)
            {
               num0 = day0;
            }
         }
         return num0;
      }
      
      private static function useLimit(dg0:ThingsDataGroup, da0:ThingsData, num0:int) : String
      {
         var dieNum0:int = 0;
         var str0:String = null;
         var d0:ThingsDefine = da0.save.getDefine();
         if(Gaming.LG.isCardPKB())
         {
            return "魂卡PK时不能使用道具。";
         }
         if(Gaming.LG.isMemoryTaskB())
         {
            if(ThingsName.downUIArr.indexOf(d0.name) == -1)
            {
               return "回忆任务中不能使用该道具。";
            }
         }
         if(d0.effectD.useInLevelB && !Gaming.LG.isGaming())
         {
            return "该物品只能在关卡中使用！";
         }
         if(d0.effectD.sTaskNoB && (Gaming.LG.isSpecialTaskB() || Gaming.LG.isUnionBattleB()))
         {
            return "当前模式下无法使用！";
         }
         var levelLimit0:int = dg0.getLevelCanUseNum(da0.save.name);
         if(levelLimit0 <= 0)
         {
            return "关卡中该物品使用次数已用完！";
         }
         var dayLimit0:int = dg0.getDayCanUseNum(d0.name);
         if(dayLimit0 <= 0)
         {
            return "今天该物品使用次数已用完！";
         }
         if(d0.effectD.type == "addLove")
         {
            if(!dg0.playerData.moreWay.getDataByHeroName(RoleName.Girl))
            {
               return "你没有小樱队友，无法增加好感度。";
            }
         }
         var minLv0:int = d0.effectD.minLv;
         if(minLv0 > 0)
         {
            if(dg0.playerData.level < minLv0)
            {
               return "人物等级至少需要" + minLv0 + "级，才能使用当前道具。";
            }
         }
         var petB0:Boolean = false;
         if(d0.name == "teamLifeBottle" && dg0.playerData.pet.getLivePetBodyArr().length > 0)
         {
            petB0 = true;
         }
         if(d0.effectD.moreLimitB)
         {
            if(dg0.playerData.getMoreNum() <= 0 && !petB0)
            {
               return "你没有队友，不需要使用该物品！";
            }
            if(Gaming.LG.isGaming())
            {
               if(dg0.playerData.moreWay.getMoreShowNum() == 0 && !petB0)
               {
                  return "关卡中不存在队友，不需要使用该物品！";
               }
               if(d0.name == "teamRebirthCard")
               {
                  dieNum0 = dg0.playerData.getMoreDieNum();
                  if(dieNum0 == 0)
                  {
                     return "关卡中没有需要复活的队友，\n不需要使用该物品！";
                  }
               }
            }
         }
         var fun0:Function = ThingsUseCtrl[d0.name];
         if(fun0 is Function)
         {
            str0 = fun0(dg0,da0,num0);
            if(str0 != "")
            {
               return str0;
            }
         }
         return "";
      }
      
      private static function skillFleshCard(dg0:ThingsDataGroup, da0:ThingsData, num0:int) : String
      {
         var hero0:HeroBody = Gaming.PG.propsHero;
         if(!hero0.skill.haveNoFillCdB() && !hero0.vehicleCtrl.canUseFleshCardB())
         {
            return "你当前没有需要刷新冷却时间的主动技能，\n不需要使用该物品！";
         }
         return "";
      }
      
      private static function caisson(dg0:ThingsDataGroup, da0:ThingsData, num0:int) : String
      {
         var b0:HeroBody = null;
         var canB0:Boolean = false;
         var bb0:Boolean = true;
         var doubleB0:Boolean = DoubleCtrl.levelDoubleB();
         var arr0:Array = dg0.playerData.getHeroArr();
         for each(b0 in arr0)
         {
            if(b0 is HeroBody)
            {
               canB0 = doubleB0 ? true : b0 == Gaming.PG.propsHero;
               if(canB0)
               {
                  if(!b0.dat.chargerData.isAllFillB())
                  {
                     bb0 = false;
                     break;
                  }
               }
            }
         }
         if(bb0)
         {
            return "目标单位携弹量都已满，不需要使用该物品！";
         }
         return "";
      }
      
      private static function lifeBottle(dg0:ThingsDataGroup, da0:ThingsData, num0:int) : String
      {
         var hero0:HeroBody = Gaming.PG.propsHero;
         if(hero0.dat.getCtrlBody().getData().getLifePer() >= 1)
         {
            return "目标生命值已满，不需要使用该物品！";
         }
         if(hero0.getDie() > 0)
         {
            return "目标已经倒下，无法使用该物品！";
         }
         return "";
      }
      
      private static function teamLifeBottle(dg0:ThingsDataGroup, da0:ThingsData, num0:int) : String
      {
         var b0:IO_NormalBody = null;
         var bb0:Boolean = true;
         var arr0:Array = dg0.playerData.moreWay.getMoreLiveHeroArr(false).concat(dg0.playerData.pet.getFightAndSuppleBodyArr());
         for each(b0 in arr0)
         {
            if(b0 is IO_NormalBody)
            {
               if(b0.getData().canUseLifeBottleB())
               {
                  bb0 = false;
                  break;
               }
            }
         }
         if(bb0)
         {
            return "所有队友和尸宠的生命值已满，不需要使用该物品！";
         }
         return "";
      }
      
      public static function bossSumCard(dg0:ThingsDataGroup, da0:ThingsData, num0:int) : String
      {
         var max0:int = 0;
         var now0:int = 0;
         var main0:BossEditData = null;
         var pd0:PlayerData = dg0.playerData;
         if(Boolean(pd0))
         {
            if(Gaming.LG.canBossSumCardB() == false)
            {
               return "任务、争霸中无法使用该道具。";
            }
            max0 = 0;
            now0 = 0;
            if(Gaming.LG.isDemonOnlyThingsB())
            {
               max0 = PlayerMainData.getDemBossSumMax();
               now0 = pd0.main.save.dembs;
            }
            else
            {
               max0 = PlayerMainData.getBossSumMax();
               now0 = pd0.main.save.bs;
            }
            if(now0 >= max0)
            {
               return "该道具已到达每周使用上限。";
            }
            main0 = pd0.bossEdit.getMain();
            if(main0 == null)
            {
               return "你没有主力首领，无法使用该道具。";
            }
            if(main0.sumPan() == false)
            {
               return "不能召唤以战神、尸宠、载具以及我方单位为基础的首领。";
            }
            return "";
         }
         return "无法使用该道具。";
      }
      
      private static function effectBagPan(dg0:ThingsDataGroup, da0:ThingsData, num0:int, doEffectB0:Boolean) : Boolean
      {
         var str0:String = null;
         var must0:BagSpaceMust = null;
         var mustNum0:int = 0;
         var mustLabel0:String = null;
         var nowBagNum0:int = 0;
         var canUseNum0:int = 0;
         var bagStr0:String = null;
         var d0:ThingsDefine = da0.save.getDefine();
         if(doEffectB0 && d0.effectD.type != "" && d0.giftD.arr.length > 0)
         {
            str0 = GiftAddit.bagSpacePan(d0.giftD,num0);
            if(str0 == "")
            {
               return true;
            }
            if(d0.effectD.useSurplusBagB)
            {
               must0 = d0.giftD.getBagSpaceMust(num0);
               mustNum0 = must0.getOneNum();
               mustLabel0 = must0.getOneLabel();
               nowBagNum0 = int(dg0.playerData[mustLabel0 + "Bag"].getSpaceSiteNum());
               canUseNum0 = 0;
               if(d0.giftRandomB)
               {
                  canUseNum0 = must0.getCanUseNum(num0,dg0.playerData);
               }
               else
               {
                  canUseNum0 = nowBagNum0 * mustNum0 / num0;
               }
               if((mustNum0 % num0 == 0 || d0.giftRandomB) && canUseNum0 > 0)
               {
                  nowDg = dg0;
                  nowName = da0.save.name;
                  nowNum = canUseNum0;
                  bagStr0 = d0.giftRandomB ? "" : BagSpaceMust.getCnNameByName(mustLabel0);
                  Gaming.uiGroup.alertBox.showNormal(bagStr0 + "背包空位不足，该物品只能一次性使用" + ComMethod.color(canUseNum0 + "个","#00FF00") + "，\n是否继续？","yesAndNo",affterUse,null);
                  return false;
               }
               Gaming.uiGroup.alertBox.showNormal(str0,"yes",null,null,"no");
               return false;
            }
            Gaming.uiGroup.alertBox.showNormal(str0,"yes",null,null,"no");
            return false;
         }
         return true;
      }
      
      private static function affterUse() : void
      {
         useThings(nowDg,nowName,nowNum,true);
      }
      
      public static function playerUseThings(dg0:ThingsDataGroup, da0:ThingsData, num0:int) : void
      {
         var obj0:ShopCountObj = null;
         var goodsDefine0:GoodsDefine = Gaming.defineGroup.goods.getHavePropsIdDefine(da0.save.name);
         if(goodsDefine0 is GoodsDefine)
         {
            obj0 = new ShopCountObj();
            obj0.inDataByGoodsDefine(goodsDefine0,num0);
         }
      }
   }
}


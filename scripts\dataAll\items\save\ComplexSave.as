package dataAll.items.save
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.StringDate;
   
   public class ComplexSave extends ShopItemsSave
   {
      public var getTime:String = "";
      
      public var severTime:String = "";
      
      private var _inHouseTime:String = "";
      
      public var shopB:Boolean = false;
      
      public function ComplexSave()
      {
         super();
         this.inHouseTime = "";
      }
      
      public function set inHouseTime(str0:String) : void
      {
         this._inHouseTime = TextWay.toCode32(str0);
      }
      
      public function get inHouseTime() : String
      {
         return TextWay.getText32(this._inHouseTime);
      }
      
      override public function isImportantB() : Boolean
      {
         if(super.isImportantB())
         {
            return true;
         }
         if(this.shopB)
         {
            return true;
         }
         return false;
      }
      
      public function getSeverDay() : int
      {
         var now0:StringDate = null;
         var day0:int = 0;
         if(this.severTime != "" && this.severTime != "0-01-00 00:00:00")
         {
            now0 = Gaming.api.save.getNowServerDate();
            day0 = now0.reductionOneStr(this.severTime);
            if(day0 < 0)
            {
               day0 = -1;
            }
            return day0;
         }
         return -1;
      }
      
      public function getSeverTimeTip() : String
      {
         var day0:int = this.getSeverDay();
         if(day0 >= 0)
         {
            return "·已获得" + (day0 + 1) + "天·";
         }
         return "";
      }
   }
}


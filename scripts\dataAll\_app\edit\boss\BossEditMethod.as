package dataAll._app.edit.boss
{
   import UI.UIOrder;
   import com.common.data.Base64;
   import dataAll._app.edit.EditSave;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit._def.EditProGather;
   import dataAll._app.edit.list.EditListAgent;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.level.TempLevel;
   import dataAll.level.define.LevelDefine;
   import dataAll.pro.ProType;
   import dataAll.skill.define.SkillDefine;
   import flash.system.System;
   
   public class BossEditMethod
   {
      public static const changeCn:String = "changeCn";
      
      public static const mp:String = "mp";
      
      public static const addSkill:String = "addSkill";
      
      public static const baseSkill:String = "baseSkill";
      
      public static const semmonBoss:String = "semmonBoss";
      
      public static const sumBossTask:String = "sumBossTask";
      
      public static const canSemmonLevelArr:Array = [semmonBoss,sumBossTask];
      
      private static var tempDa:BossEditData = null;
      
      private static var meUidB:Boolean = true;
      
      private static var taskBoss:BossEditData = null;
      
      private static const taskBossCode:String = "CgsBBWRwBAIFcG4GGeayg+m+meWFiOeUnwNuBhlab21iaWVCYXR0bGUFbXAGCVdvVHUFbGkEFAVjbgYBBXNrCREBBgkxM180BgkxM18zBgkxM18xBgkzNV8xBgkzMl83BgkzM18yBgkzMl8zBgkyMl83BXVkBgcwXzAB";
      
      private static var armsAgent:EditListAgent = null;
      
      public function BossEditMethod()
      {
         super();
      }
      
      public static function gotoMap(da0:BossEditData, tipB0:Boolean = true) : void
      {
         if(Boolean(da0.getBodyDefine()))
         {
            tempDa = da0;
            if(tipB0)
            {
               Gaming.uiGroup.alertBox.showChoose("要挑战该首领吗？",yesGotoMap);
            }
            else
            {
               yesGotoMap();
            }
         }
         else
         {
            UIOrder.alertError("该首领数据不存在。");
         }
      }
      
      private static function yesGotoMap() : void
      {
         var d0:LevelDefine = tempDa.getNewLevelDefine();
         if(Boolean(d0))
         {
            TempLevel.addInObj(d0,d0.name);
            Gaming.PG.da.bossEdit.setLevelBoss(tempDa);
            tempDa = null;
            Gaming.LG.chooseByLevelDefine(d0);
         }
         else
         {
            UIOrder.alertError("地图不存在！");
         }
      }
      
      public static function shareCode(da0:BossEditData, meUidB0:Boolean = true) : void
      {
         var code0:String = null;
         tempDa = da0;
         meUidB = meUidB0;
         if(Gaming.isLoginAndHaveUid())
         {
            code0 = da0.getShareCode(Gaming.PG,meUidB0);
            Gaming.uiGroup.alertBox.textInput.showTextInput("确认复制以下首领代码？",code0,yesCode,"yesAndNo",1000);
         }
         else
         {
            UIOrder.alertError("无法分享代码，请重新登录！");
         }
      }
      
      private static function yesCode(str0:String) : void
      {
         var code0:String = tempDa.getShareCode(Gaming.PG,meUidB);
         System.setClipboard(code0);
         Gaming.uiGroup.alertBox.showSuccess("首领代码已复制到剪贴板！发给你的朋友，\n让他们在首领大厅中输入该代码，即可挑战首领。");
         tempDa = null;
      }
      
      public static function getTempDatabyCode(code0:String) : BossEditData
      {
         var s0:EditSave = new EditSave();
         s0.inObjData_byBase64(code0);
         var da0:BossEditData = new BossEditData();
         da0.inData_bySave(s0,true);
         return da0;
      }
      
      public static function codeToObjAndPan(code0:String) : Object
      {
         var obj0:Object = null;
         if(code0 == "")
         {
            return "代码不能为空！";
         }
         try
         {
            return codeToObj(code0);
         }
         catch(e:Error)
         {
         }
         return "代码格式不正确！";
      }
      
      public static function codeToObj(code0:String) : Object
      {
         return Base64.decodeObject(code0);
      }
      
      public static function objToCode(obj0:Object) : String
      {
         return Base64.encodeObject(obj0);
      }
      
      public static function getStarByScore(score0:Number) : int
      {
         if(score0 > 34000)
         {
            return 8;
         }
         if(score0 > 17000)
         {
            return 7;
         }
         if(score0 > 6800)
         {
            return 6;
         }
         if(score0 > 2700)
         {
            return 5;
         }
         if(score0 > 900)
         {
            return 4;
         }
         if(score0 > 300)
         {
            return 3;
         }
         if(score0 > 100)
         {
            return 2;
         }
         return 1;
      }
      
      public static function getScore(da0:BossEditData) : Number
      {
         var skill0:Number = NaN;
         var level0:Number = NaN;
         var life0:Number = NaN;
         var dpsMax0:Number = NaN;
         var dps0:Number = NaN;
         var sur0:Number = NaN;
         var score0:Number = NaN;
         if(Boolean(da0.getBodyDefine()))
         {
            skill0 = skillScoreMul(da0.getBaseSkillArr(),da0.getSkillNameArr());
            level0 = levelScoreMul(da0);
            life0 = da0.getLife();
            dpsMax0 = 3000000;
            dps0 = da0.getDps();
            if(dps0 > dpsMax0)
            {
               dps0 = dpsMax0;
            }
            sur0 = Math.pow(life0 * dps0 * skill0 * level0,0.5);
            return Math.ceil(sur0 / 1000000);
         }
         return 0;
      }
      
      private static function skillScoreMul(baseSkillArr0:Array, addSkillArr0:Array) : Number
      {
         var name0:* = null;
         var d0:SkillDefine = null;
         var oneMul0:Number = NaN;
         var arr0:Array = baseSkillArr0.concat(addSkillArr0);
         var v0:Number = 1;
         for each(name0 in arr0)
         {
            d0 = Gaming.defineGroup.skill.getDefine(name0);
            oneMul0 = d0.getBossEditScoreMul();
            v0 *= oneMul0;
         }
         return v0;
      }
      
      private static function levelScoreMul(da0:BossEditData) : Number
      {
         var d0:EditProDefine = null;
         var scoreMul0:Number = NaN;
         var name0:String = null;
         var type0:String = null;
         var v0:* = undefined;
         var mul0:Number = NaN;
         var allMul0:Number = 1;
         var arr0:Array = Gaming.defineGroup.editPro.getArrByGather(EditProGather.boss);
         for each(d0 in arr0)
         {
            scoreMul0 = d0.scoreMul;
            if(scoreMul0 > 0)
            {
               name0 = d0.name;
               type0 = d0.type;
               v0 = da0.getValueByDefName(name0);
               if(v0 != null)
               {
                  mul0 = 1;
                  if(type0 == ProType.BOOLEAN)
                  {
                     if(v0 == true)
                     {
                        mul0 = scoreMul0;
                     }
                  }
                  else if(type0 == ProType.NUMBER)
                  {
                     if(isNaN(v0) == false)
                     {
                        if(name0 == "tm")
                        {
                           mul0 = timeScoreMul(v0,d0);
                        }
                        else
                        {
                           mul0 = v0 * scoreMul0;
                        }
                     }
                  }
                  allMul0 *= mul0;
               }
            }
         }
         return allMul0;
      }
      
      private static function timeScoreMul(v0:Number, d0:EditProDefine) : Number
      {
         var max0:Number = d0.max;
         var min0:Number = d0.min;
         if(v0 > max0)
         {
            v0 = max0;
         }
         if(v0 < min0)
         {
            v0 = min0;
         }
         var m0:Number = 1 + (max0 - v0) / 90;
         return m0 * d0.scoreMul;
      }
      
      public static function getTaskBoss(heroLv0:int) : BossEditData
      {
         if(!taskBoss)
         {
            taskBoss = getTempDatabyCode(taskBossCode);
         }
         taskBoss.lv = heroLv0;
         return taskBoss;
      }
      
      public static function getArmsListAgent() : EditListAgent
      {
         var fatherArr0:Array = null;
         var cnArr0:Array = null;
         var darr0:Array = null;
         var rangeD0:ArmsRangeDefine = null;
         var d0:ArmsDefine = null;
         var father0:String = null;
         armsAgent = null;
         var a0:EditListAgent = armsAgent;
         if(Boolean(a0))
         {
            a0.clearFun();
         }
         else
         {
            a0 = new EditListAgent();
            fatherArr0 = ArmsType.uiArr.concat([]);
            cnArr0 = ArmsType.getCnArr(fatherArr0);
            fatherArr0.push("other","edit");
            cnArr0.push("其他","自制");
            a0.inTitle(fatherArr0,cnArr0);
            darr0 = Gaming.defineGroup.bullet.getRangeArr();
            for each(rangeD0 in darr0)
            {
               d0 = rangeD0.def;
               father0 = d0.armsType;
               if(fatherArr0.indexOf(father0) == -1)
               {
                  father0 = "other";
               }
               a0.addDataLast(d0,father0);
            }
            a0.addDataLastArr(Gaming.PG.da.armsTor.getArmsDefineArr().reverse(),"edit");
            armsAgent = a0;
         }
         a0.createAllText();
         return a0;
      }
   }
}


package dataAll._app.outfit
{
   import dataAll._app.outfit.define.OutfitDefine;
   
   public class OutfitData
   {
      public var define:OutfitDefine;
      
      public var haveB:Boolean = false;
      
      public var mustStr:String = "";
      
      public function OutfitData()
      {
         super();
      }
      
      public function inDataByDefine(d0:OutfitDefine) : void
      {
         this.define = d0;
      }
   }
}


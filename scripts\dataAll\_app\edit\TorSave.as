package dataAll._app.edit
{
   import com.sounto.utils.ClassProperty;
   import dataAll._base.OneSave;
   
   public class TorSave extends OneSave
   {
      public static var pro_arr:Array = null;
      
      public var obj:Object = {};
      
      public function TorSave()
      {
         super();
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.obj = ClassProperty.copyObj(obj0["obj"]);
      }
      
      public function initName(v0:String, baseLabel0:String) : void
      {
         this.setValue("name",v0);
         this.setValue("baseLabel",baseLabel0);
      }
      
      public function get name() : String
      {
         return this.getValue("name","");
      }
      
      public function get baseLabel() : String
      {
         return this.getValue("baseLabel","");
      }
      
      public function get pn() : String
      {
         return this.getValue("pn","");
      }
      
      private function getValue(pro0:String, noValue0:* = null) : *
      {
         if(this.obj.hasOwnProperty(pro0))
         {
            return this.obj[pro0];
         }
         return noValue0;
      }
      
      private function setValue(pro0:String, v0:*) : void
      {
         this.obj[pro0] = v0;
      }
   }
}


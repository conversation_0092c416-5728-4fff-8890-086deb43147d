package dataAll._app.task.define
{
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.utils.ClassProperty;
   
   public class TaskDropDefine
   {
      public static var pro_arr:Array = [];
      
      public var dropName:String = "";
      
      public var armsType:String = "";
      
      public var equipType:String = "";
      
      private var _coinMul:String = "";
      
      private var _skillStoneMul:String = "";
      
      public function TaskDropDefine()
      {
         super();
         this.coinMul = 1;
         this.skillStoneMul = 1;
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function set coinMul(v0:Number) : void
      {
         this._coinMul = Sounto64.encode(String(v0));
      }
      
      public function get coinMul() : Number
      {
         var v0:Number = Number(Sounto64.decode(this._coinMul));
         if(v0 < 0)
         {
            v0 = 0;
         }
         if(v0 > 3)
         {
            v0 = 3;
         }
         return v0;
      }
      
      public function set skillStoneMul(v0:Number) : void
      {
         this._skillStoneMul = Sounto64.encode(String(v0));
      }
      
      public function get skillStoneMul() : Number
      {
         var v0:Number = Number(Sounto64.decode(this._skillStoneMul));
         if(v0 < 0)
         {
            v0 = 0;
         }
         if(v0 > 3)
         {
            v0 = 3;
         }
         return v0;
      }
   }
}


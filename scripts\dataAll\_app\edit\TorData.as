package dataAll._app.edit
{
   import com.adobe.serialization.json.JSON2;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit._def.EditProMethod;
   import dataAll._app.edit.boss.BossEditPro;
   import dataAll._app.edit.tor.IO_TorEditDefine;
   import dataAll._app.edit.tor.TorEditAgent;
   import dataAll._app.edit.tor.TorEditMethod;
   import dataAll._base.IO_Define;
   import dataAll._base.OneData;
   import dataAll._base.OneSave;
   import dataAll._player.IO_UserGetter;
   import dataAll.image.ImageUrlDefine;
   import dataAll.image.SoundDefine;
   import dataAll.pro.ProType;
   import dataAll.ui.GatherColor;
   
   public class TorData extends OneData
   {
      protected var torSave:TorSave;
      
      protected var tempCode:String = "";
      
      public function TorData()
      {
         super();
      }
      
      override public function inData_bySave(s0:OneSave) : void
      {
         super.inData_bySave(s0);
         this.torSave = s0 as TorSave;
      }
      
      public function getTorSave() : TorSave
      {
         return this.torSave;
      }
      
      public function getTorEditAgent() : TorEditAgent
      {
         INIT.showError("getTorEditAgent函数必须被覆盖");
         return null;
      }
      
      public function getName() : String
      {
         if(Boolean(this.torSave))
         {
            return this.torSave.name;
         }
         return "";
      }
      
      public function havePn() : Boolean
      {
         return this.torSave.pn != "";
      }
      
      public function setTempCode(code0:String) : void
      {
         this.tempCode = code0;
      }
      
      public function getTempCode() : String
      {
         return this.tempCode;
      }
      
      public function getShareCode(userg0:IO_UserGetter) : String
      {
         if(this.tempCode != "")
         {
            return this.tempCode;
         }
         return this.getMeCode(userg0);
      }
      
      protected function getMeCode(userg0:IO_UserGetter) : String
      {
         var obj0:Object = ClassProperty.copyObj(this.torSave.obj);
         if(this.torSave.pn == "")
         {
            obj0[BossEditPro.pn] = userg0.getPlayerName();
         }
         return TorEditMethod.objToCode(obj0);
      }
      
      public function getInJson(userg0:IO_UserGetter) : String
      {
         var obj0:Object = ClassProperty.copyObj(this.torSave.obj);
         return JSON2.encode(obj0);
      }
      
      public function getObjValue(proD0:EditProDefine) : *
      {
         return proD0.getObjValue(this.torSave.obj);
      }
      
      public function getDefValue(proD0:EditProDefine) : *
      {
         INIT.showError("getDefValue函数必须被覆盖");
         return null;
      }
      
      public function getBaseValue(proD0:EditProDefine) : *
      {
         INIT.showError("getInitValue函数必须被覆盖");
         return null;
      }
      
      public function getValue(proD0:EditProDefine) : *
      {
         var v0:* = this.getDefValue(proD0);
         if(v0 == null)
         {
            v0 = this.getObjValue(proD0);
         }
         return v0;
      }
      
      public function getUIValue(proD0:EditProDefine) : String
      {
         var mstr0:String = null;
         var soundD0:SoundDefine = null;
         var arr0:Array = null;
         var imgD0:ImageUrlDefine = null;
         var baseImgD0:ImageUrlDefine = null;
         var v0:* = this.getValue(proD0);
         var str0:String = null;
         if(v0 is Boolean)
         {
            str0 = ProType.booleanToUI(v0);
         }
         else if(v0 is String)
         {
            mstr0 = EditProMethod.getUIValueStringM(proD0.method,v0);
            if(mstr0 != null)
            {
               str0 = mstr0;
            }
            else if(proD0.method == EditProMethod.color)
            {
               if(v0 != "")
               {
                  str0 = ComMethod.color(v0,GatherColor.uintToHtml(v0));
               }
            }
            else if(proD0.method == EditProMethod.sound)
            {
               if(v0 != null && v0 != "")
               {
                  soundD0 = Gaming.defineGroup.sound.getDefine(v0 as String);
                  if(Boolean(soundD0))
                  {
                     str0 = soundD0.cnName;
                  }
                  else
                  {
                     str0 = v0;
                  }
               }
               else
               {
                  str0 = "";
               }
            }
         }
         else if(proD0.isEditArray())
         {
            arr0 = v0 as Array;
            str0 = (Boolean(arr0) ? arr0.length : 0) + "个";
         }
         else
         {
            imgD0 = v0 as ImageUrlDefine;
            if(Boolean(imgD0))
            {
               baseImgD0 = Gaming.defineGroup.imageUrl.getDefine(imgD0.name);
               if(Boolean(baseImgD0))
               {
                  str0 = baseImgD0.cnName + " " + imgD0.url;
               }
            }
         }
         if(str0 == null)
         {
            str0 = v0;
         }
         return str0;
      }
      
      public function getChildUIValue(proD0:EditProDefine, td0:IO_TorEditDefine) : String
      {
         var childType0:String = proD0.getChildType();
         var childD0:IO_Define = EditProMethod.getChildDefine(childType0,td0.getName());
         if(Boolean(childD0))
         {
            return childD0.getCnName();
         }
         return td0.getName();
      }
      
      public function getUICn(proD0:EditProDefine) : String
      {
         return proD0.cnName;
      }
      
      public function getNumberMin(proD0:EditProDefine) : Number
      {
         return proD0.min;
      }
      
      public function getNumberMax(proD0:EditProDefine) : Number
      {
         return proD0.max;
      }
      
      public function getProTip(d0:IO_TorEditDefine) : String
      {
         return d0.getTorTip();
      }
      
      public function samePanBase(proD0:EditProDefine) : Boolean
      {
         var v0:* = this.getValue(proD0);
         var base0:* = this.getBaseValue(proD0);
         if(v0 == base0)
         {
            return true;
         }
         return false;
      }
      
      public function changeValue(proD0:EditProDefine, v0:*) : Boolean
      {
         var bb0:Boolean = false;
         var now0:* = this.getValue(proD0);
         if(v0 != now0)
         {
            return proD0.setObjValueAndAdd(this.torSave.obj,v0);
         }
         return false;
      }
      
      public function changeImageUrlDefine(proD0:EditProDefine, d0:ImageUrlDefine) : Boolean
      {
         proD0.setObjValue(this.torSave.obj,d0.name);
         return true;
      }
   }
}

import dataAll._app.edit.arms.ArmsTorData;

ArmsTorData;


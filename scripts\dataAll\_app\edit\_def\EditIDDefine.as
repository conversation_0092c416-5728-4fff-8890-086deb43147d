package dataAll._app.edit._def
{
   public class EditIDDefine
   {
      public var id:String = "";
      
      public var gather:int = 0;
      
      public var father:int = 0;
      
      public function EditIDDefine()
      {
         super();
      }
      
      public static function getNormalBossScoreMul() : Number
      {
         return 1.2;
      }
      
      public function inId(id0:String) : void
      {
         this.id = id0;
         this.gather = int(id0.substr(0,1));
         this.father = int(id0.substr(1,1));
      }
      
      public function getBossScoreMul(init0:Number) : Number
      {
         if(this.gather == 4)
         {
            if(this.father == 4)
            {
               return 0.1;
            }
            if(this.father == 3)
            {
               return 1;
            }
         }
         return init0;
      }
   }
}


package dataAll._app.edit.boss
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.ObjectMethod;
   import dataAll._app.edit.EditSaveGroup;
   import dataAll._app.task.TaskState;
   
   public class BossEditSaveGroup extends EditSaveGroup
   {
      public static var pro_arr:Array = null;
      
      public static var mePro_arr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var history:EditSaveGroup = new EditSaveGroup();
      
      public var collect:EditSaveGroup = new EditSaveGroup();
      
      public var mainId:String = "";
      
      public var diffO:Object = {};
      
      public var taskO:Object = {};
      
      public function BossEditSaveGroup()
      {
         super();
      }
      
      public function get upNum() : Number
      {
         return this.CF.getAttribute("upNum");
      }
      
      public function set upNum(v0:Number) : void
      {
         this.CF.setAttribute("upNum",v0);
      }
      
      public function get bA() : Number
      {
         return this.CF.getAttribute("bA");
      }
      
      public function set bA(v0:Number) : void
      {
         this.CF.setAttribute("bA",v0);
      }
      
      public function get mBA() : Number
      {
         return this.CF.getAttribute("mBA");
      }
      
      public function set mBA(v0:Number) : void
      {
         this.CF.setAttribute("mBA",v0);
      }
      
      public function get bs() : Number
      {
         return this.CF.getAttribute("bs");
      }
      
      public function set bs(v0:Number) : void
      {
         this.CF.setAttribute("bs",v0);
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObj(obj0);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
         this.taskO = ClassProperty.copyObj(obj0["taskO"]);
         this.diffO = ClassProperty.copyObj(obj0["diffO"]);
      }
      
      public function newDayCtrl() : void
      {
         this.upNum = 0;
      }
      
      public function newWeek() : void
      {
         this.taskO = {};
         this.diffO = {};
      }
      
      public function dealBossScore() : void
      {
         var n:* = undefined;
         var v0:int = 0;
         if(this.bA == 0)
         {
            for(n in this.taskO)
            {
               v0 = this.getTaskV(n);
               if(v0 >= 1)
               {
                  this.addBossScoreByName(n);
               }
            }
         }
      }
      
      public function setTaskDiff(name0:String, diff0:int) : void
      {
         this.diffO[name0] = diff0;
      }
      
      public function getTaskDiff(name0:String) : int
      {
         return ObjectMethod.getEleIfHave(this.diffO,name0,-1);
      }
      
      private function setTaskV(name0:String, v0:int) : void
      {
         this.taskO[name0] = v0;
      }
      
      private function getTaskV(name0:String) : int
      {
         return ObjectMethod.getEleIfHave(this.taskO,name0,-1);
      }
      
      public function taskWinEvent(name0:String) : void
      {
         if(this.getTaskState(name0) == TaskState.ing)
         {
            this.setTaskV(name0,1);
            this.addBossScoreByName(name0);
         }
      }
      
      private function addBossScoreByName(name0:String) : void
      {
         ++this.bA;
         var d0:BossTaskDefine = BossTaskCtrl.getDefine(name0);
         var diff0:int = this.getTaskDiff(name0);
         var maxDiff0:int = d0.getDiffLen() - 1;
         if(diff0 >= maxDiff0)
         {
            ++this.mBA;
         }
         if(diff0 >= 0)
         {
            this.bs += diff0 + 1;
         }
      }
      
      public function taskGiftEvent(name0:String) : void
      {
         var v0:int = this.getTaskV(name0);
         if(v0 > 1)
         {
            v0++;
         }
         else
         {
            v0 = 2;
         }
         this.setTaskV(name0,v0);
      }
      
      public function getTaskState(name0:String) : String
      {
         var v0:int = this.getTaskV(name0);
         if(v0 == 1)
         {
            return TaskState.complete;
         }
         if(v0 > 1)
         {
            return TaskState.over;
         }
         return TaskState.ing;
      }
   }
}


package dataAll.image
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._base.IO_TipDefine;
   import dataAll._base.NormalDefine;
   import dataAll.bullet.BulletDefine;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.skill.StateData;
   import gameAll.hero.HeroBody;
   
   public class ImageUrlDefine extends NormalDefine implements IO_TipDefine
   {
      public static var ZERO:ImageUrlDefine = new ImageUrlDefine();
      
      public static var pro_arr:Array = [];
      
      public static const SMOKETYPE_ONE:String = "one";
      
      public static const SMOKETYPE_FRAME:String = "frame";
      
      public static const CON_NORMAL:String = "";
      
      public static const CON_ADD:String = "add";
      
      public static const CON_FILTER:String = "filter";
      
      private static var finlPro_arr:Array = null;
      
      public var url:String = "";
      
      public var con:String = "";
      
      public var bottomLayerB:Boolean = false;
      
      public var cacheB:Boolean = false;
      
      public var bodyColor:uint = 0;
      
      public var bodyColorAlpha:Number = 0;
      
      public var partType:Array = [];
      
      public var xGap:Number = 0;
      
      public var noShowB:Boolean = false;
      
      public var followPartRaB:Boolean = false;
      
      public var noFollowB:Boolean = false;
      
      public var randomRange:Number = 0;
      
      public var ranAn:Number = 0;
      
      public var urlRandomValue:int = 0;
      
      public var raNum:int = 1;
      
      public var imgDieType:String = "";
      
      public var smokeType:String = "one";
      
      public var soundUrl:String = "";
      
      public var soundRan:int = 0;
      
      public var soundVolume:Number = -1;
      
      private var soundVolumeB:Boolean = false;
      
      public var sm:String = "";
      
      public var len:int = 0;
      
      public var time:Number = -1;
      
      public var waveAn:Number = 0;
      
      public var everFleshB:Boolean = false;
      
      public var shake:String = "";
      
      private var shakeD:BoomShakeDefine = null;
      
      public function ImageUrlDefine()
      {
         super();
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         var base0:ImageUrlDefine = null;
         if(obj0 is String)
         {
            base0 = Gaming.defineGroup.imageUrl.getDefine(obj0 as String);
            if(Boolean(base0))
            {
               obj0 = base0;
            }
         }
         if(obj0 != null)
         {
            ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         }
      }
      
      public function inDefineXml(xml0:XML, father0:String = "") : void
      {
         if(!xml0)
         {
            return;
         }
         super.inData_byXML(xml0,father0);
         this.url = String(xml0);
         if(this.soundVolume != -1)
         {
            this.soundVolumeB = true;
         }
         else
         {
            this.soundVolume = 1;
         }
      }
      
      override public function inData_byXML(xml0:XML, father0:String = "") : void
      {
         var baseD0:ImageUrlDefine = null;
         if(xml0 == null)
         {
            return;
         }
         var name0:String = xml0.@name;
         if(name0 != "")
         {
            baseD0 = Gaming.defineGroup.imageUrl.getDefine(name0);
            if(Boolean(baseD0))
            {
               this.inData_byObj(baseD0);
            }
            else
            {
               INIT.showError("inData_byXML找不到ImageUrlDefine：" + name0);
            }
         }
         else
         {
            this.inDefineXml(xml0,father0);
         }
      }
      
      override protected function getBaseClassProArr() : Array
      {
         return pro_arr;
      }
      
      public function getShakeD() : BoomShakeDefine
      {
         if(this.shake != "" && !this.shakeD)
         {
            this.shakeD = new BoomShakeDefine();
            this.shakeD.inData(this.shake);
         }
         return this.shakeD;
      }
      
      public function setSoundVolumeByBulletNum(num0:int) : void
      {
         if(!this.soundVolumeB)
         {
            this.soundVolume = 1 / num0 * 0.7 + 0.3;
         }
      }
      
      public function getSoundVolumeSetB() : Boolean
      {
         return this.soundVolumeB;
      }
      
      public function getSoundUrl() : String
      {
         var i0:int = 0;
         if(this.soundRan > 1)
         {
            i0 = 1 + Math.random() * this.soundRan;
            if(i0 > this.soundRan)
            {
               i0 = this.soundRan;
            }
            else if(i0 < 1)
            {
               i0 = 1;
            }
            return this.soundUrl + i0;
         }
         return this.soundUrl;
      }
      
      public function getPreSoundUrlArr() : Array
      {
         var i:int = 0;
         var arr0:Array = [];
         if(this.soundUrl != "")
         {
            if(this.soundRan > 1)
            {
               for(i = 1; i <= this.soundRan; i++)
               {
                  arr0.push(this.soundUrl + i);
               }
            }
            else
            {
               arr0.push(this.soundUrl);
            }
         }
         return arr0;
      }
      
      public function getCanShowB(b0:IO_NormalBody, da0:StateData) : Boolean
      {
         var hero0:HeroBody = b0 as HeroBody;
         if(Boolean(hero0))
         {
            if(name == "greatSageCloud")
            {
               if(da0.define.baseLabel == "gliding_hero")
               {
                  if(hero0.dat.equipData.isGreatSageB())
                  {
                     return true;
                  }
               }
            }
         }
         return this.url != "" && !this.noShowB;
      }
      
      public function getPre() : String
      {
         if(this.url.indexOf("/") > 0)
         {
            return this.url + "," + this.raNum;
         }
         return "";
      }
      
      public function getPreArr() : Array
      {
         var i:int = 0;
         var url0:String = null;
         var arr0:Array = [];
         if(this.urlRandomValue == 0)
         {
            arr0.push(this.getPre());
         }
         else
         {
            for(i = 1; i <= this.urlRandomValue; i++)
            {
               url0 = this.url + i;
               if(url0.indexOf("/") > 0)
               {
                  arr0.push(url0 + "," + this.raNum);
               }
            }
         }
         return arr0;
      }
      
      public function getRandomUrl() : String
      {
         var i0:int = 0;
         if(this.urlRandomValue == 0)
         {
            return this.url;
         }
         i0 = 1 + Math.random() * this.urlRandomValue;
         return this.url + i0;
      }
      
      public function getUrlArr() : Array
      {
         var arr0:Array = null;
         var i:int = 0;
         if(this.urlRandomValue == 0)
         {
            return [this.url];
         }
         arr0 = [];
         for(i = 1; i <= this.urlRandomValue; i++)
         {
            arr0.push(this.url + i);
         }
         return arr0;
      }
      
      public function getUrlFather() : String
      {
         return this.url.split("/")[0];
      }
      
      public function getTipStr() : String
      {
         var s0:String = "";
         if(this.url != "")
         {
            s0 += "模型地址：" + this.url;
         }
         if(this.con != "")
         {
            s0 += "\n图层效果：" + this.con;
         }
         if(Boolean(this.partType) && this.partType.length > 0)
         {
            s0 += "\n显示部位：" + this.partType;
         }
         if(this.soundUrl != "")
         {
            s0 += "\n音效：" + this.soundUrl;
         }
         if(this.soundVolumeB)
         {
            s0 += "\n音量：" + this.soundVolume;
         }
         if(this.len > 0)
         {
            s0 += "\n射线单元：" + this.len;
            if(this.time != -1)
            {
               s0 += "\n射线时长：" + this.time;
            }
            if(this.waveAn > 0)
            {
               s0 += "\n射线波动：" + this.waveAn;
            }
            if(this.everFleshB)
            {
               s0 += "\n射线持续刷新：是";
            }
         }
         return s0;
      }
      
      public function isNoB() : Boolean
      {
         if(this.url == "" && this.soundUrl == "" && this.shake == "")
         {
            return true;
         }
         return false;
      }
      
      public function isLongLine() : Boolean
      {
         return this.url == BulletDefine.LONG_LINE;
      }
      
      public function isBulletLineB() : Boolean
      {
         if(this.len > 0)
         {
            return true;
         }
         if(this.isLongLine())
         {
            return true;
         }
         return false;
      }
      
      public function setIdName(id0:String) : void
      {
         name = id0;
      }
      
      public function setIdCn(str0:String) : void
      {
         cnName = str0;
      }
      
      public function getUnicode() : String
      {
         var name0:* = null;
         var v0:* = undefined;
         if(this.isNoB())
         {
            return BulletDefine.NO;
         }
         if(this.url == BulletDefine.LONG_LINE)
         {
            return this.url;
         }
         if(finlPro_arr == null)
         {
            finlPro_arr = ArrayMethod.deductArr(pro_arr,["name","father","cnName"]);
         }
         var id0:String = "";
         var proArr2:Array = finlPro_arr;
         for each(name0 in proArr2)
         {
            v0 = this[name0];
            id0 += "_" + String(v0);
         }
         return id0;
      }
   }
}


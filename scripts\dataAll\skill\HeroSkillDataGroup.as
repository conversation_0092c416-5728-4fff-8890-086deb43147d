package dataAll.skill
{
   import com.sounto.utils.ArrayMethod;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.save.ItemsSave;
   import dataAll.items.save.ItemsSaveGroup;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.skill.save.HeroSkillSave;
   import dataAll.skill.save.HeroSkillSaveGroup;
   import dataAll.ui.tip.CheckData;
   
   public class HeroSkillDataGroup extends ItemsDataGroup
   {
      public var saveGroup:HeroSkillSaveGroup;
      
      public function HeroSkillDataGroup()
      {
         super();
         dataType = ItemsDataGroup.TYPE_SKILL;
      }
      
      public static function skillSwapTo(dg1:HeroSkillDataGroup, dg2:HeroSkillDataGroup, site1:int, site2:int, wearCompareLevel:int = 9999) : CheckData
      {
         return ItemsDataGroup.swapTo(dg1,dg2,site1,site2,wearCompareLevel);
      }
      
      override public function inData_bySaveGroup(sg0:ItemsSaveGroup) : void
      {
         var n:* = undefined;
         var s0:HeroSkillSave = null;
         var da0:HeroSkillData = null;
         clearData();
         this.saveGroup = sg0 as HeroSkillSaveGroup;
         for(n in sg0.arr)
         {
            s0 = sg0.arr[n];
            da0 = new HeroSkillData();
            da0.inData_bySave(s0,normalPlayerData,this);
            da0.setPlaceType(placeType);
            dataArr[n] = da0;
            _siteDataArr = null;
         }
      }
      
      public function clearSameName() : void
      {
         var da0:HeroSkillData = null;
         var name0:String = null;
         var tt:int = 0;
         var nameObj0:Object = {};
         var newArr0:Array = [];
         for each(da0 in dataArr)
         {
            name0 = da0.save.baseLabel;
            if(nameObj0[name0] == null)
            {
               nameObj0[name0] = true;
               newArr0.push(da0);
            }
            else
            {
               tt = 0;
            }
         }
         if(newArr0.length < dataArr.length)
         {
            dataArr = newArr0;
            fleshSaveGroup();
         }
      }
      
      public function clearNoInBaseLabelArr(sarr0:Array) : void
      {
         var da0:HeroSkillData = null;
         var newArr0:Array = [];
         for each(da0 in dataArr)
         {
            if(sarr0.indexOf(da0.save.baseLabel) >= 0)
            {
               newArr0.push(da0);
            }
            else
            {
               ArrayMethod.addNoRepeatInArr(this.saveGroup.delNameArr,da0.save.getTrueLabel());
            }
         }
         if(newArr0.length < dataArr.length)
         {
            dataArr = newArr0;
            fleshSaveGroup();
         }
      }
      
      public function addSkillByLabel(label0:String, mustLv0:int, lockB0:Boolean = false) : HeroSkillData
      {
         var d0:HeroSkillDefine = Gaming.defineGroup.skill.getDefine(label0) as HeroSkillDefine;
         if(!d0)
         {
            INIT.showErrorMust("找不到定义HeroSkillDefine：" + label0);
         }
         var s0:HeroSkillSave = new HeroSkillSave();
         s0.baseLabel = d0.name;
         s0.lv = 1;
         s0.studyBodyLv = mustLv0;
         s0.setLockB(lockB0);
         return this.addSave(s0) as HeroSkillData;
      }
      
      override public function addSave(s0:ItemsSave, fleshSaveGroupB0:Boolean = true) : IO_ItemsData
      {
         var da0:HeroSkillData = new HeroSkillData();
         da0.inData_bySave(s0 as HeroSkillSave,normalPlayerData,this);
         addData(da0,fleshSaveGroupB0);
         return da0;
      }
      
      public function getDataByBase(name0:String) : HeroSkillData
      {
         var n:* = undefined;
         var da0:HeroSkillData = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(da0.save.baseLabel == name0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getNameArr() : Array
      {
         var n:* = undefined;
         var da0:HeroSkillData = null;
         var arr0:Array = [];
         for(n in dataArr)
         {
            da0 = dataArr[n];
            arr0.push(da0.save.getDefine().name);
         }
         return arr0;
      }
      
      public function getBaseLabelArr() : Array
      {
         var n:* = undefined;
         var da0:HeroSkillData = null;
         var arr0:Array = [];
         for(n in dataArr)
         {
            da0 = dataArr[n];
            arr0.push(da0.save.baseLabel);
         }
         return arr0;
      }
      
      public function getTrueLabelArr(meCnRange0:Array, skillCnRange0:Array = null, skillNumLimit0:int = -1) : Array
      {
         var n:* = undefined;
         var da0:HeroSkillData = null;
         var arr0:Array = [];
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(meCnRange0.indexOf(da0.getCnName()) != -1)
            {
               if(!(Boolean(skillCnRange0) && skillCnRange0.indexOf(da0.getCnName()) == -1))
               {
                  if(!(skillNumLimit0 >= 0 && da0.save.site >= skillNumLimit0))
                  {
                     arr0.push(da0.save.getTrueLabel());
                  }
               }
            }
         }
         return arr0;
      }
      
      public function getNoNeedTrueLabelArr(meCnRange0:Array, skillCnRange0:Array = null) : Array
      {
         var n:* = undefined;
         var da0:HeroSkillData = null;
         var arr0:Array = [];
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(da0.save.getDefine().noNeedEquipB)
            {
               if(da0.save.effB)
               {
                  if(meCnRange0.indexOf(da0.getCnName()) != -1)
                  {
                     if(!(Boolean(skillCnRange0) && skillCnRange0.indexOf(da0.getCnName()) == -1))
                     {
                        arr0.push(da0.save.getTrueLabel());
                     }
                  }
               }
            }
         }
         return arr0;
      }
      
      public function sortByActive() : void
      {
         var da0:HeroSkillData = null;
         var d0:HeroSkillDefine = null;
         var id0:String = null;
         var i:int = 0;
         for each(da0 in dataArr)
         {
            d0 = da0.save.getDefine();
            id0 = 10000 + i + "";
            if(d0.isPassiveB())
            {
               id0 = 20000 + i + "";
            }
            da0.setTempSortId(id0);
            i++;
         }
         sortByTempSortId();
      }
      
      public function getProfiNoFull() : HeroSkillData
      {
         var da0:HeroSkillData = null;
         for each(da0 in dataArr)
         {
            if(da0.save.getProfiFullB() == false)
            {
               if(da0.save.getDefine().haveProfiB())
               {
                  return da0;
               }
            }
         }
         return null;
      }
      
      override public function newDayCtrl(timeStr0:String) : void
      {
         var da0:HeroSkillData = null;
         for each(da0 in dataArr)
         {
            da0.newDayCtrl(timeStr0);
         }
      }
      
      public function getNoLockNum() : int
      {
         var da0:HeroSkillData = null;
         var num0:int = 0;
         for each(da0 in dataArr)
         {
            if(!da0.getSave().getLockB())
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function resetAllSkill() : void
      {
         var da0:HeroSkillData = null;
         var arr2:Array = [];
         for each(da0 in dataArr)
         {
            if(!da0.isCanRebuildB())
            {
               arr2.push(da0);
            }
         }
         dataArr = arr2;
         fleshSaveGroup();
         _siteDataArr = null;
      }
      
      override public function getSaveGroup() : ItemsSaveGroup
      {
         return this.saveGroup;
      }
      
      override public function getGripType() : String
      {
         return "equipGrip";
      }
   }
}


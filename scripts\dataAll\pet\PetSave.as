package dataAll.pet
{
   import com.sounto.utils.ClassProperty;
   import dataAll.pet.base.PetBaseSave;
   import dataAll.pet.gene.save.GeneSave;
   import dataAll.skill.save.HeroSkillSaveGroup;
   
   public class PetSave
   {
      public static var pro_arr:Array = [];
      
      public var base:PetBaseSave = new PetBaseSave();
      
      public var skill:HeroSkillSaveGroup = new HeroSkillSaveGroup();
      
      public var gene:GeneSave = new GeneSave();
      
      public var fightB:Boolean = false;
      
      public var suppleB:Boolean = false;
      
      public function PetSave()
      {
         super();
         this.skill.gripMaxNum = 100;
         this.skill.unlockTo(99);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function getState() : String
      {
         if(this.fightB && !this.suppleB)
         {
            return PetState.FIGHT;
         }
         if(!this.fightB && this.suppleB)
         {
            return PetState.SUPPLE;
         }
         return PetState.DEFENCE;
      }
   }
}


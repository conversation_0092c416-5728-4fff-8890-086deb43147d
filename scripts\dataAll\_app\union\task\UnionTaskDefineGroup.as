package dataAll._app.union.task
{
   public class UnionTaskDefineGroup
   {
      public var arr:Array = [];
      
      private var obj:Object = {};
      
      private var idObj:Object = {};
      
      public function UnionTaskDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var x0:XML = null;
         var d0:UnionTaskDefine = null;
         var xml_list0:XMLList = xml0.task;
         for(i in xml_list0)
         {
            x0 = xml_list0[i];
            d0 = new UnionTaskDefine();
            d0.inData_byXML(x0);
            this.arr.push(d0);
            this.obj[d0.name] = d0;
            this.idObj[d0.id] = d0;
         }
      }
      
      public function getDefine(name0:String) : UnionTaskDefine
      {
         return this.obj[name0];
      }
      
      public function getDefineById(id0:String) : UnionTaskDefine
      {
         return this.idObj[id0];
      }
   }
}


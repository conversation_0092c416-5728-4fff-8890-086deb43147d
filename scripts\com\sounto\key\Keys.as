package com.sounto.key
{
   public class Keys
   {
      public static const DOWN:String = "down";
      
      public static const DOWNING:String = "downing";
      
      public static const UP:String = "up";
      
      public static const UPING:String = "uping";
      
      public var code:int = 0;
      
      public var label:String = "";
      
      public var s:String = "";
      
      public function Keys(_code:int = 0)
      {
         super();
         this.code = _code;
      }
      
      public function get state() : String
      {
         if(this.s == DOWN || this.s == DOWNING)
         {
            return DOWNING;
         }
         return UPING;
      }
      
      public function isDown() : <PERSON>olean
      {
         return this.s == DOWN;
      }
      
      public function isUp() : <PERSON>olean
      {
         return this.s == UP;
      }
      
      public function isDowning() : Boolean
      {
         return this.s == DOWNING;
      }
      
      public function isUping() : <PERSON><PERSON>an
      {
         return this.s == UPING;
      }
      
      public function toing() : void
      {
         if(this.s == DOWN)
         {
            this.s = DOWNING;
         }
         else if(this.s == UP)
         {
            this.s = UPING;
         }
      }
   }
}


package dataAll._app.top.simulated
{
   import com.sounto.utils.ClassProperty;
   import dataAll._app.login.SaveData4399;
   import dataAll._app.top.TopBarData;
   import dataAll._app.top.TopBarDataGroup;
   import dataAll._app.top.define.TopBarDefineGroup;
   import dataAll._app.top.player.PlayerTopReturnData;
   import dataAll._app.top.player.PlayerTopUploadData;
   
   public class SimulatedTopBarDataGroup
   {
      private static var tempTopRankArr:Array = [1,2,3,4,5,6,7,8,9,10,15,20,30,40,50,60,70,80,90,100,500,1000,5000,10000];
      
      private static var tempTopRankIndex:int = 0;
      
      public function SimulatedTopBarDataGroup()
      {
         super();
      }
      
      public static function getRankListsData(rankListId:uint, pageSize:uint, pageNum:uint) : Array
      {
         var d0:TopBarDefineGroup = Gaming.defineGroup.top.getDefineById(rankListId);
         return TopBarDataGroup.getSimulatedArr(d0,pageSize);
      }
      
      public static function getUserData(uid:String, idx:uint) : Object
      {
         return getUserDataByObj(Gaming.PG.save);
      }
      
      public static function getUserDataByObj(obj0:Object) : SaveData4399
      {
         var ud0:SaveData4399 = new SaveData4399();
         ud0.base.setToTest();
         ud0.data = ClassProperty.copyObj(obj0);
         return ud0;
      }
      
      public static function submitScore(obj0:Object) : Object
      {
         var da0:PlayerTopUploadData = new PlayerTopUploadData();
         da0.inData_byObj(obj0);
         var r_da0:PlayerTopReturnData = new PlayerTopReturnData();
         r_da0.setToSimulated(da0);
         return r_da0;
      }
      
      public static function getOneRankInfo(rId:uint) : Object
      {
         var d0:TopBarDefineGroup = Gaming.defineGroup.top.getDefineById(rId);
         var arr0:Array = TopBarDataGroup.getSimulatedArr(d0,1);
         var da0:TopBarData = arr0[0];
         da0.rank = getRandomRank();
         return arr0[0];
      }
      
      public static function getRandomRank() : int
      {
         tempTopRankIndex = (tempTopRankIndex + 1) % tempTopRankArr.length;
         return tempTopRankArr[tempTopRankIndex];
      }
   }
}


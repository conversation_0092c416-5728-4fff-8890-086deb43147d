package dataAll._app.tower
{
   import UI.base.button.BtnAgent;
   import UI.base.button.BtnAgentGroup;
   import com.sounto.cf.NiuBiCF;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.equip.add.IO_EquipAddGetter;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.LevelDiffGetting;
   import dataAll.level.TempLevel;
   import dataAll.level.define.LevelDefine;
   
   public class TowerData implements IO_EquipAddGetter
   {
      private var CF:NiuBiCF = new NiuBiCF();
      
      protected var save:TowerSave = null;
      
      private var nowTower:TowerDefine = null;
      
      private var diffAgent:BtnAgentGroup = null;
      
      public var unend:UnendData = new UnendData();
      
      public function TowerData()
      {
         super();
      }
      
      public static function gotoMap(d0:TowerDefine, diff0:int) : void
      {
         var levelD0:LevelDefine = getLevelDefine(d0,diff0);
         TempLevel.addInObj(levelD0,levelD0.name);
         Gaming.PG.da.tower.setNow(d0,diff0);
         Gaming.LG.chooseByLevelDefine(levelD0);
      }
      
      public static function getLevelDefine(td0:TowerDefine, diff0:int) : LevelDefine
      {
         var baseD0:LevelDefine = null;
         var mapD0:WorldMapDefine = Gaming.defineGroup.worldMap.getDefine(td0.getMapName());
         var d0:LevelDefine = Gaming.defineGroup.level.getDefineBy("tower").clone();
         var base0:String = mapD0.getLevelName();
         if(base0 != "")
         {
            baseD0 = Gaming.defineGroup.level.getDefine(base0);
            d0.rectG = baseD0.getCloneRectG();
         }
         d0.sceneLabel = mapD0.name;
         if(td0.oneB)
         {
            d0.info.toOne();
         }
         if(td0.ldiy != "")
         {
            d0.info.diy = td0.ldiy;
         }
         var fun0:Function = TowerDefineCtrl.getDealFun(td0);
         if(fun0 is Function)
         {
            fun0(td0,d0,diff0);
         }
         d0.unitG.addUnitOrderDefine(td0.getUnitOrderDefine(d0.unitG.allDefault,diff0));
         d0.fleshDataByOutChange();
         return d0;
      }
      
      public function get nowDiff() : Number
      {
         return this.CF.getAttribute("nowDiff");
      }
      
      public function set nowDiff(v0:Number) : void
      {
         this.CF.setAttribute("nowDiff",v0);
      }
      
      public function inData_bySave(s0:TowerSave) : void
      {
         this.save = s0;
         this.unend.inData_bySave(s0);
      }
      
      public function getSave() : TowerSave
      {
         return this.save;
      }
      
      public function getUIArr() : Array
      {
         var d0:TowerDefine = null;
         var baseArr0:Array = Gaming.defineGroup.tower.getUIArr();
         var arr0:Array = [];
         for each(d0 in baseArr0)
         {
            if(!(this.save.ws == false && this.isWinMax(d0) && this.haveCanGift(d0) == false))
            {
               arr0.push(d0);
            }
         }
         return arr0;
      }
      
      public function getProAddObj() : Object
      {
         return this.unend.getProAddObj();
      }
      
      public function getProAddMax(pro0:String) : Number
      {
         return this.unend.getProAddMax(pro0);
      }
      
      public function winEvent() : void
      {
         if(Boolean(this.nowTower))
         {
            this.save.winEvent(this.nowTower.getName(),this.nowDiff);
         }
      }
      
      public function isWinMax(d0:TowerDefine) : Boolean
      {
         var diff0:int = this.getWinDiff(d0);
         return diff0 >= TowerDefine.MAX_DIFF;
      }
      
      public function getWinDiff(d0:TowerDefine) : int
      {
         return this.save.getWinDiff(d0.getName());
      }
      
      public function winShowB() : Boolean
      {
         return this.save.ws;
      }
      
      public function swapWinShow() : void
      {
         this.save.ws = !this.save.ws;
      }
      
      public function getWinMaxNum() : int
      {
         var d0:TowerDefine = null;
         var num0:int = 0;
         var baseArr0:Array = Gaming.defineGroup.tower.getUIArr();
         for each(d0 in baseArr0)
         {
            if(this.isWinMax(d0))
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function getDiffAgent(d0:TowerDefine, clickFun:Function) : BtnAgentGroup
      {
         var a0:BtnAgent = null;
         var diff0:int = 0;
         var canGift0:GiftAddDefineGroup = null;
         var g0:BtnAgentGroup = this.diffAgent;
         if(g0 == null)
         {
            g0 = new BtnAgentGroup();
            g0.newNum(TowerDefine.MAX_DIFF);
         }
         var winDiff0:int = this.getWinDiff(d0);
         var giftDiff0:int = this.save.getGiftDiff(d0.getName());
         var arr0:Array = g0.getArr();
         for(var i:int = 0; i < arr0.length; i++)
         {
            a0 = arr0[i];
            diff0 = i + 1;
            a0.name = diff0 + "";
            a0.cn = LevelDiffGetting.getCnName(i,MapMode.REGULAR);
            if(winDiff0 >= diff0)
            {
               a0.smallIcon = "yes";
               a0.tipString = "已通关";
            }
            else
            {
               a0.smallIcon = "";
               canGift0 = d0.getCanGift(giftDiff0,diff0);
               a0.tipString = "当前通关奖励（包含所有低难度）：\n" + canGift0.getDescription();
            }
         }
         g0.name = d0.getName();
         g0.title = "第" + d0.sort + "层";
         g0.clickFun = clickFun;
         return g0;
      }
      
      public function getGiftTip(d0:TowerDefine) : String
      {
         var winDiff0:int = this.getWinDiff(d0);
         var giftDiff0:int = this.save.getGiftDiff(d0.getName());
         return d0.getGiftTip(giftDiff0,winDiff0);
      }
      
      public function giftEvent(d0:TowerDefine) : void
      {
         var name0:String = d0.getName();
         var winDiff0:int = this.save.getWinDiff(name0);
         this.save.giftEvent(name0,winDiff0);
      }
      
      public function haveCanGift(d0:TowerDefine) : Boolean
      {
         var name0:String = d0.getName();
         var nowDiff0:int = this.save.getGiftDiff(name0);
         var winDiff0:int = this.save.getWinDiff(name0);
         if(nowDiff0 < winDiff0)
         {
            return true;
         }
         return false;
      }
      
      public function getCanGift(d0:TowerDefine) : GiftAddDefineGroup
      {
         var name0:String = d0.getName();
         var nowDiff0:int = this.save.getGiftDiff(name0);
         var winDiff0:int = this.save.getWinDiff(name0);
         if(nowDiff0 < winDiff0)
         {
            return d0.getCanGift(nowDiff0,winDiff0);
         }
         return null;
      }
      
      public function setNow(d0:TowerDefine, diff0:int) : void
      {
         this.nowTower = d0;
         this.nowDiff = diff0;
      }
   }
}


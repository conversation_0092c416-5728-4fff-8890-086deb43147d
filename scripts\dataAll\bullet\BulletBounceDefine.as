package dataAll.bullet
{
   import com.sounto.utils.ClassProperty;
   import dataAll._base.IO_CanThinSave;
   
   public class BulletBounceDefine implements IO_CanThinSave
   {
      public static var pro_arr:Array = [];
      
      public var floor:int = 0;
      
      public var body:int = 0;
      
      public var vMul:Number = 1;
      
      public var liveInitB:Boolean = false;
      
      public var shakeString:String = "";
      
      public var glueFloorB:Boolean = false;
      
      public var hurtNumAdd:Number = 0;
      
      public var noHitTime:Number = 0;
      
      public var noDieB:Boolean = false;
      
      public function BulletBounceDefine()
      {
         super();
      }
      
      public function getProArr() : Array
      {
         return pro_arr;
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function copy() : BulletBounceDefine
      {
         var d0:BulletBounceDefine = new BulletBounceDefine();
         ClassProperty.inData(d0,this,pro_arr);
         return d0;
      }
      
      public function haveDataB() : Boolean
      {
         return this.floor != 0 || this.body != 0;
      }
      
      public function getHurtAddMax() : Number
      {
         return 12;
      }
   }
}


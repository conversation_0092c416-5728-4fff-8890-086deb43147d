package com.sounto.utils
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._base.IO_CanThinSave;
   import flash.geom.Point;
   import flash.utils.ByteArray;
   import flash.utils.describeType;
   
   public class ClassProperty
   {
      private static var normalClass:Array = [int,String,Number];
      
      public function ClassProperty()
      {
         super();
      }
      
      public static function isNormalDataType(v0:*) : Boolean
      {
         var class0:Class = null;
         for each(class0 in normalClass)
         {
            if(v0 is class0)
            {
               return true;
            }
         }
         return false;
      }
      
      public static function getProArr(obj:Object, hasSetGetB:Boolean = true) : Array
      {
         var n:* = undefined;
         var arr0:Array = [];
         var xml0:XML = describeType(obj);
         var list0:XMLList = xml0.variable;
         var list2:XMLList = xml0.accessor;
         for(n in list0)
         {
            arr0.push(String(list0[n].@name));
         }
         if(hasSetGetB)
         {
            for(n in list2)
            {
               arr0.push(String(list2[n].@name));
            }
         }
         return arr0;
      }
      
      public static function getFunArr(obj:Object) : Array
      {
         var n:* = undefined;
         var arr0:Array = [];
         var xml0:XML = describeType(obj);
         var list0:XMLList = xml0.method;
         for(n in list0)
         {
            arr0.push(String(list0[n].@name));
         }
         return arr0;
      }
      
      public static function inData(target:Object, obj:Object, pro_arr:Array) : *
      {
         var n:* = undefined;
         var name0:String = null;
         for(n in pro_arr)
         {
            name0 = pro_arr[n];
            if(obj.hasOwnProperty(name0))
            {
               if(obj[name0] == null)
               {
                  target[name0] = null;
               }
               else if(Boolean(obj[name0].hasOwnProperty("clone2")))
               {
                  target[name0] = obj[name0].clone2();
               }
               else if(Boolean(obj[name0].hasOwnProperty("clone")))
               {
                  target[name0] = obj[name0].clone();
               }
               else if(obj[name0] is Array)
               {
                  target[name0] = copyArray(obj[name0]);
               }
               else
               {
                  target[name0] = obj[name0];
               }
            }
         }
      }
      
      public static function inDataOnlyValue(target:Object, obj:Object, pro_arr:Array) : *
      {
         var n:* = undefined;
         var name0:String = null;
         var b0:* = undefined;
         for(n in pro_arr)
         {
            name0 = pro_arr[n];
            if(obj.hasOwnProperty(name0))
            {
               b0 = obj[name0];
               target[name0] = b0;
            }
         }
      }
      
      public static function inData_byXML(target:Object, xml0:XML, pro_arr:Array, arrToNumberB:Boolean = false) : *
      {
         var n:* = undefined;
         var name0:String = null;
         var value0:String = null;
         for(n in pro_arr)
         {
            name0 = pro_arr[n];
            value0 = String(xml0[name0]);
            setProByString(target,name0,value0,arrToNumberB);
         }
      }
      
      public static function inData_byXMLAt(target:Object, xml0:XML, pro_arr:Array) : *
      {
         var n:* = undefined;
         var name0:String = null;
         var value0:String = null;
         for(n in pro_arr)
         {
            name0 = pro_arr[n];
            value0 = String(xml0[name0]);
            setProByString(target,name0,value0);
         }
      }
      
      public static function setProByString(target:Object, name0:String, value0:String, arrToNumberB:Boolean = false) : *
      {
         if(!(value0 == "" || !value0))
         {
            if(target[name0] is Number)
            {
               target[name0] = Number(value0);
            }
            else if(target[name0] is int)
            {
               target[name0] = int(value0);
            }
            else if(target[name0] is uint)
            {
               target[name0] = uint(value0);
            }
            else if(target[name0] is String)
            {
               target[name0] = String(value0);
            }
            else if(target[name0] is Boolean)
            {
               target[name0] = Boolean(int(value0));
            }
            else if(target[name0] is Point)
            {
               target[name0] = ComMethod.getPoint(String(value0));
            }
            else if(target[name0] is Array)
            {
               if(arrToNumberB)
               {
                  target[name0] = ComMethod.stringToNumberArr(value0);
               }
               else
               {
                  target[name0] = String(value0).split(",");
               }
            }
         }
      }
      
      public static function copyArray(arr0:Array) : Array
      {
         var n:* = undefined;
         var obj0:* = undefined;
         var arr1:Array = [];
         if(!(arr0 is Array))
         {
            return arr1;
         }
         for(n in arr0)
         {
            obj0 = arr0[n];
            if(obj0 != null)
            {
               if(Boolean(obj0.hasOwnProperty("clone2")))
               {
                  arr1.push(obj0.clone2());
               }
               else if(Boolean(obj0.hasOwnProperty("clone")))
               {
                  arr1.push(obj0.clone());
               }
               else
               {
                  arr1.push(obj0);
               }
            }
         }
         return arr1;
      }
      
      public static function copyObj(obj0:Object) : Object
      {
         if(!(obj0 is Object))
         {
            return {};
         }
         var byte0:ByteArray = new ByteArray();
         byte0.writeObject(obj0);
         byte0.position = 0;
         return byte0.readObject();
      }
      
      public static function isNormalPro(v0:*) : Boolean
      {
         if(v0 is Number || v0 is int || v0 is Boolean || v0 is String)
         {
            return true;
         }
         return false;
      }
      
      public static function inData_bySaveObj(target:Object, obj:Object, pro_arr:Array) : *
      {
         var n:* = undefined;
         var name0:String = null;
         if(!(target is Object) || !(obj is Object))
         {
            return;
         }
         for(n in pro_arr)
         {
            name0 = pro_arr[n];
            if(obj.hasOwnProperty(name0))
            {
               if(obj[name0] == null)
               {
                  target[name0] = null;
               }
               else if(Boolean(target[name0]) && Boolean(target[name0].hasOwnProperty("inData_byObj")))
               {
                  target[name0].inData_byObj(obj[name0]);
               }
               else if(obj[name0] is Array)
               {
                  target[name0] = copyArray(obj[name0]);
               }
               else if(obj[name0] is Number || obj[name0] is int || obj[name0] is Boolean || obj[name0] is String)
               {
                  target[name0] = obj[name0];
               }
            }
         }
      }
      
      public static function copySaveArray(arr0:Array, childClassOrFun0:Object) : Array
      {
         var n:* = undefined;
         var obj0:Object = null;
         var class0:Class = null;
         var obj2:* = undefined;
         if(!arr0)
         {
            return [];
         }
         var arr1:Array = [];
         for(n in arr0)
         {
            obj0 = arr0[n];
            class0 = childClassOrFun0 is Class ? childClassOrFun0 as Class : childClassOrFun0(obj0);
            obj2 = new class0();
            obj2.inData_byObj(obj0);
            arr1.push(obj2);
         }
         return arr1;
      }
      
      public static function copySaveObj(mObj0:Object, class0:Class) : Object
      {
         var n:* = undefined;
         var obj0:Object = null;
         var obj2:* = undefined;
         if(!mObj0)
         {
            return {};
         }
         var mObj2:Object = {};
         for(n in mObj0)
         {
            obj0 = mObj0[n];
            obj2 = new class0();
            obj2.inData_byObj(obj0);
            mObj2[n] = obj2;
         }
         return mObj2;
      }
      
      public static function countObjNum(obj0:Object) : Number
      {
         var n:* = undefined;
         var num0:int = 0;
         for(n in obj0)
         {
            num0++;
         }
         return num0;
      }
      
      public static function thinSaveObj(mo:Object, obj0:Object, proArr0:Array) : Object
      {
         var n0:* = null;
         var now0:* = undefined;
         var zero0:* = undefined;
         var d0:IO_CanThinSave = null;
         var o22:Object = null;
         var newObj0:Object = {};
         for each(n0 in proArr0)
         {
            if(mo.hasOwnProperty(n0))
            {
               if(obj0.hasOwnProperty(n0))
               {
                  now0 = mo[n0];
                  zero0 = obj0[n0];
                  if(now0 != zero0)
                  {
                     d0 = zero0 as IO_CanThinSave;
                     if(Boolean(d0))
                     {
                        o22 = thinSaveObj(now0,zero0,d0.getProArr());
                        if(ObjectMethod.getObjElementNum(o22) > 0)
                        {
                           newObj0[n0] = o22;
                        }
                     }
                     else if(isNormalPro(now0))
                     {
                        newObj0[n0] = now0;
                     }
                     else if(now0 is Array)
                     {
                        if(ArrayMethod.elementSamePan(now0 as Array,zero0 as Array) == false)
                        {
                           newObj0[n0] = now0;
                        }
                     }
                     else if(now0 is Object)
                     {
                        if(ObjectMethod.getObjElementNum(now0) > 0 || ObjectMethod.getObjElementNum(zero0) > 0)
                        {
                           newObj0[n0] = now0;
                        }
                     }
                     else
                     {
                        newObj0[n0] = now0;
                     }
                  }
               }
               else
               {
                  newObj0[n0] = now0;
               }
            }
         }
         return newObj0;
      }
      
      public static function proToSaveObj(obj0:Object, pro0:String) : void
      {
         if(obj0.hasOwnProperty(pro0))
         {
            obj0[pro0] = copyObj(obj0[pro0]);
         }
      }
      
      protected static function arrToSaveObj(arr0:Array) : Array
      {
         var arr1:Array = null;
         if(arr0.length > 0)
         {
            if(isNormalPro(arr0[0]) == false)
            {
               return copyObj(arr0) as Array;
            }
         }
         return arr0;
      }
      
      protected static function objToSaveObj(obj0:Object) : Object
      {
         var n:* = undefined;
         var obj2:Object = null;
         for(n in obj0)
         {
            if(isNormalPro(obj0[n]) == false)
            {
               return copyObj(obj0);
            }
         }
         return obj0;
      }
   }
}


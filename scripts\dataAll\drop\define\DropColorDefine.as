package dataAll.drop.define
{
   import com.sounto.oldUtils.ComMethod;
   
   public class DropColorDefine
   {
      public var name:Array = [];
      
      public var normalPro:Array = [];
      
      public var superPro:Array = [];
      
      public var bossPro:Array = [];
      
      public var itemsLvRange:Array = [];
      
      public const MAX_LEVEL:int = 99;
      
      public function DropColorDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var pro0:String = null;
         var arr0:Array = null;
         var pro_arr0:Array = ["normalPro","superPro","bossPro"];
         for(n in pro_arr0)
         {
            pro0 = pro_arr0[n];
            arr0 = ComMethod.stringToNumberArr(String(xml0[pro0]));
            this.inProArray(pro0,arr0);
            if(this[pro0].length < this.name.length)
            {
               INIT.showError(pro0 + "的长度" + this[pro0].length + "<" + this.name.length);
            }
         }
         this.name = ComMethod.stringToStringArr(String(xml0.name));
         this.itemsLvRange = ComMethod.stringToRangeArr(String(xml0.itemsLvRange));
      }
      
      private function inProArray(pro0:String, arr0:Array) : void
      {
         this[pro0] = arr0;
      }
      
      public function getProArray(type0:String) : Array
      {
         return this[type0 + "Pro"];
      }
      
      public function getOneDrop(type0:String, bodyLv0:int = 0, dropMul0:Number = 0) : String
      {
         var proArr0:Array = this.getProArray(type0).concat([]);
         if(!proArr0)
         {
            INIT.showError("不存在这个类型的数据：" + type0);
         }
         if(bodyLv0 < 50)
         {
            proArr0[4] *= 2;
         }
         if(bodyLv0 < 60)
         {
            proArr0[5] *= 1 + dropMul0;
         }
         proArr0[4] *= 1 + dropMul0;
         var index0:int = ComMethod.getPro_byArrSum(proArr0);
         return this.name[index0];
      }
      
      public function getBossProByName(name0:String) : Number
      {
         var f0:int = int(this.name.indexOf(name0));
         if(f0 >= 0)
         {
            return this.bossPro[f0];
         }
         return 0;
      }
   }
}


package dataAll.level
{
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._player.PlayerData;
   import dataAll.level.modeDiy.ModeDiyDefine;
   import dataAll.skill.define.SkillName;
   
   public class DemonDataCtrl
   {
      public static const demStone:String = "demStone";
      
      public static const demBall:String = "demBall";
      
      public static const demonChest:String = "demonChest";
      
      private static const START_OPEN:StringDate = new StringDate("2022-2-28");
      
      private static const TEN_OPEN9:StringDate = new StringDate("2025-5-5");
      
      private static const SKILL8:Array = [];
      
      private static const SKILL8Obj:Object = {};
      
      private static const BOSS9_CN:Array = [];
      
      public function DemonDataCtrl()
      {
         super();
      }
      
      public static function setReadStringDate(readStringDate0:StringDate) : void
      {
         var mapD0:WorldMapDefine = null;
         var skill8:String = null;
         var diyName0:String = null;
         var diyArr0:Array = null;
         var i0:int = 0;
         skill8Init();
         dem9Init();
         var mapArr0:Array = Gaming.defineGroup.worldMap.getShowMapArr();
         var day0:int = START_OPEN.compareDate(readStringDate0);
         var week0:int = Math.floor(day0 / 7);
         var closeIndexArr0:Array = getWeekCloseIndexArr(readStringDate0);
         var openArr:Array = [];
         var index0:int = 0;
         for each(mapD0 in mapArr0)
         {
            skill8 = "";
            diyName0 = ModeDiyDefine.CLOSE;
            if(mapD0.demBossSkillArr.length == 0)
            {
               diyName0 = ModeDiyDefine.CLOSE;
            }
            else
            {
               if(closeIndexArr0 == null || closeIndexArr0.indexOf(index0) >= 0)
               {
                  diyName0 = ModeDiyDefine.CLOSE;
               }
               else
               {
                  diyArr0 = Gaming.defineGroup.modelDiy.dealDiyArr(mapD0,ModeDiyDefine.USE_ARR);
                  i0 = (index0 + week0) % diyArr0.length;
                  diyName0 = diyArr0[i0];
                  skill8 = getSkill8(index0 + week0,diyName0,mapD0.demBossSkillArr);
                  openArr.push(mapD0);
               }
               index0++;
            }
            mapD0.setDemonDiy(diyName0,skill8);
         }
         dealBoss9(openArr,week0,readStringDate0);
      }
      
      private static function getWeekCloseIndexArr(readStringDate0:StringDate) : Array
      {
         var NEW_ARR:Array = [24,25];
         var indexArr0:Array = getCloseArr("2023-9-25",6,readStringDate0);
         if(indexArr0 == null)
         {
            indexArr0 = getCloseArr("2022-10-3",4,readStringDate0);
            if(Boolean(indexArr0))
            {
               indexArr0 = indexArr0.concat(NEW_ARR);
            }
         }
         return indexArr0;
      }
      
      private static function getCloseArr(closeTime0:String, closeNum0:int, readStringDate0:StringDate) : Array
      {
         var indexArr0:Array = null;
         var index0:int = 0;
         var i:int = 0;
         var time0:StringDate = new StringDate(closeTime0);
         var day0:int = time0.compareDate(readStringDate0);
         var week0:int = Math.floor(day0 / 7);
         if(week0 < 0)
         {
            return null;
         }
         indexArr0 = [];
         index0 = week0 * closeNum0 % 20;
         for(i = 0; i < closeNum0; i++)
         {
            indexArr0.push(index0 + i);
         }
         return indexArr0;
      }
      
      public static function getLvMust() : int
      {
         return 90;
      }
      
      public static function openPan(pd0:PlayerData) : String
      {
         var mapD0:WorldMapDefine = null;
         if(pd0.worldMap.saveGroup.getWinB("BuDong"))
         {
            return "";
         }
         mapD0 = Gaming.defineGroup.worldMap.getDefine("BuDong");
         return "通关地图" + mapD0.cnName + "后开启。";
      }
      
      public static function getStonePro(diff0:int, mapD0:WorldMapDefine) : Number
      {
         var demDiff0:int = diff0;
         var v0:Number = 0;
         if(demDiff0 == 0)
         {
            v0 = 2;
         }
         else if(demDiff0 == 1)
         {
            v0 = 3;
         }
         else if(demDiff0 == 2)
         {
            v0 = 4;
         }
         else if(demDiff0 == 3)
         {
            v0 = 5;
         }
         else if(demDiff0 == 4)
         {
            v0 = 6;
         }
         else if(demDiff0 == 5)
         {
            v0 = 7;
         }
         else if(demDiff0 == 6)
         {
            v0 = 8;
         }
         else if(demDiff0 == 7)
         {
            v0 = 9;
         }
         else if(demDiff0 == 8)
         {
            v0 = 10;
         }
         var add0:int = Gaming.PG.da.moreWay.get_demStroneDropNum();
         if(add0 > 0)
         {
            if(add0 > 3)
            {
               add0 = 3;
            }
            v0 += add0;
         }
         return v0;
      }
      
      public static function getStoneMax(diff0:int, mapD0:WorldMapDefine) : Number
      {
         var pro0:Number = getStonePro(diff0,mapD0);
         return Math.round(pro0);
      }
      
      public static function getStoneMaxAll(mapD0:WorldMapDefine) : int
      {
         var maxDiff0:int = LevelDiffGetting.getDemonDiffMax(mapD0);
         return getStoneMax(maxDiff0,mapD0);
      }
      
      public static function getBallPro(diff0:int, mapD0:WorldMapDefine) : Number
      {
         var demDiff0:int = diff0;
         var v0:Number = demDiff0 + 6;
         if(v0 > 15.1)
         {
            v0 = 15;
         }
         var add0:int = Gaming.PG.da.moreWay.get_demBallDropNum();
         if(add0 > 0)
         {
            if(add0 > 6)
            {
               add0 = 6;
            }
            v0 += add0;
         }
         return v0;
      }
      
      public static function getBallMax(diff0:int, mapD0:WorldMapDefine) : Number
      {
         var pro0:Number = getBallPro(diff0,mapD0);
         return Math.round(pro0);
      }
      
      public static function getBallMaxAll(mapD0:WorldMapDefine) : int
      {
         var maxDiff0:int = LevelDiffGetting.getDemonDiffMax(mapD0);
         return getBallMax(maxDiff0,mapD0);
      }
      
      private static function skill8Init() : void
      {
         addSkill8("rifleSensitive",[ModeDiyDefine.NOCOM]);
         addSkill8("shotgunSensitive",[ModeDiyDefine.NOCOM],["FoggyDefence"]);
         addSkill8("sniperSensitive");
         addSkill8("pistolSensitive",null,["FoggyDefence"]);
         addSkill8("rocketSensitive",[ModeDiyDefine.NOCOM],["FoggyDefence"]);
         addSkill8("flamerSensitive",null,["FoggyDefence"]);
         addSkill8("laserSensitive",[ModeDiyDefine.NOCOM],["noUnderLaser","FoggyDefence"]);
         addSkill8("otherSensitive",null,["FoggyDefence"]);
         addSkill8("weaponSensitive",[ModeDiyDefine.PARTNER,ModeDiyDefine.TEAM],["weaponDefence","bladeWrap","fightReduct"]);
         addSkill8("verShield");
         addSkill8("lockLife");
         addSkill8("enemyToSpider");
         addSkill8("everSilenceEnemy");
         addSkill8("redArmsSensitive");
      }
      
      private static function addSkill8(name0:String, noModeArr0:Array = null, noSkillArr0:Array = null) : void
      {
         SKILL8.push(name0);
         var obj0:Object = {};
         obj0.noModeArr = noModeArr0;
         obj0.noSkillArr = noSkillArr0;
         SKILL8Obj[name0] = obj0;
      }
      
      private static function panSkill8(name0:String, diyMode0:String, demSkillArr0:Array) : Boolean
      {
         var obj0:Object = SKILL8Obj[name0];
         var noModeArr0:Array = obj0.noModeArr;
         var noSkillArr0:Array = obj0.noSkillArr;
         if(Boolean(noModeArr0))
         {
            if(noModeArr0.indexOf(diyMode0) >= 0)
            {
               return false;
            }
         }
         if(Boolean(noSkillArr0))
         {
            if(ArrayMethod.onlyOneElementSamePan(noSkillArr0,demSkillArr0))
            {
               return false;
            }
         }
         return true;
      }
      
      private static function getSkill8(index0:int, diyMode0:String, demSkillArr0:Array) : String
      {
         var name0:* = null;
         var i0:int = 0;
         var bb0:Boolean = false;
         var arr0:Array = SKILL8;
         var newArr0:Array = [];
         for each(name0 in arr0)
         {
            bb0 = panSkill8(name0,diyMode0,demSkillArr0);
            if(bb0)
            {
               newArr0.push(name0);
            }
         }
         i0 = index0 % newArr0.length;
         return newArr0[i0];
      }
      
      private static function dem9Init() : void
      {
         BOSS9_CN.push("末日坦克");
         BOSS9_CN.push("冥王");
         BOSS9_CN.push("铁犬");
         BOSS9_CN.push("狂野收割者");
         BOSS9_CN.push("狂人机器");
         BOSS9_CN.push("斩之使者");
         BOSS9_CN.push("幻影X");
         BOSS9_CN.push("毒魔");
         BOSS9_CN.push("野帝");
         BOSS9_CN.push("防毒僵尸");
         BOSS9_CN.push("狂战狼");
         BOSS9_CN.push("异火龙");
         BOSS9_CN.push("球形闪电");
         BOSS9_CN.push("女爵尸");
         BOSS9_CN.push("电棍僵尸");
         BOSS9_CN.push("古飙");
         BOSS9_CN.push("多肉");
         BOSS9_CN.push("掘金尸");
      }
      
      public static function get9OpenNum() : int
      {
         if(LevelDiffGetting.useNewDemonB)
         {
            return 10;
         }
         return 3;
      }
      
      private static function dealBoss9(openArr0:Array, week0:int, readStringDate0:StringDate) : void
      {
         var d0:WorldMapDefine = null;
         var i2:int = 0;
         var bossIndex0:int = 0;
         var bossCn0:String = null;
         var skillArr9:Array = null;
         var max0:int = get9OpenNum();
         var len0:int = int(openArr0.length);
         var start0:int = week0 * 3 % len0;
         var end0:int = start0 + max0 - 1;
         var index0:int = 0;
         var num0:int = 0;
         for each(d0 in openArr0)
         {
            d0.setDemon9Boss("",null);
            i2 = index0 + len0;
            if(index0 >= start0 && index0 <= end0 || i2 >= start0 && i2 <= end0)
            {
               num0++;
               bossIndex0 = (index0 + week0) % BOSS9_CN.length;
               bossCn0 = BOSS9_CN[bossIndex0];
               skillArr9 = null;
               if(num0 > 3)
               {
                  skillArr9 = [SkillName.noUnderMulHurt];
               }
               d0.setDemon9Boss(bossCn0,skillArr9);
               INIT.tempTrace(d0.cnName + " ---- " + bossCn0 + " " + bossIndex0 + " 技能：" + skillArr9);
            }
            index0++;
         }
      }
   }
}


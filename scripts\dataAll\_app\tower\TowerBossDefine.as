package dataAll._app.tower
{
   import com.adobe.serialization.json.JSON2;
   import com.sounto.utils.ClassProperty;
   import dataAll.body.define.BodyCamp;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.level.define.unit.OneUnitOrderDefine;
   import dataAll.level.define.unit.UnitType;
   
   public class TowerBossDefine
   {
      public static var pro_arr:Array = null;
      
      public var cnName:String = "";
      
      private var _def:NormalBodyDefine = null;
      
      public var skillArr:Array = [];
      
      public var lifeMul:Number = 1;
      
      public var dpsMul:Number = 1;
      
      public function TowerBossDefine(cnName0:String = "", lifeMul0:Number = 1, dpsMul0:Number = 1, skillArr0:Array = null)
      {
         super();
         this.cnName = cnName0;
         if(Bo<PERSON><PERSON>(skillArr0))
         {
            this.skillArr = skillArr0;
         }
         this.lifeMul = lifeMul0 * 100;
         this.dpsMul = dpsMul0 * 10;
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function inJson(json0:String) : void
      {
         var obj0:Object = JSON2.decode(json0);
         this.inData_byObj(obj0);
      }
      
      public function getBodyDefine() : NormalBodyDefine
      {
         if(this._def == null)
         {
            this._def = Gaming.defineGroup.body.getCnDefine(this.cnName);
         }
         return this._def;
      }
      
      public function getName() : String
      {
         return this.getBodyDefine().name;
      }
      
      public function getOneUnitOrderDefine(diff0:int) : OneUnitOrderDefine
      {
         var od0:OneUnitOrderDefine = new OneUnitOrderDefine();
         od0.defaultDeal();
         od0.inData_byObj(this);
         od0.name = this.getBodyDefine().name;
         od0.camp = BodyCamp.ENEMY;
         od0.unitType = UnitType.BOSS;
         od0.warningRange = 99999;
         var lifeMul0:Number = TowerDefine.getLifeMulByDiff(diff0);
         var dpsMul0:Number = TowerDefine.getDpsMulByDiff(diff0);
         od0.lifeMul *= lifeMul0;
         od0.dpsMul *= dpsMul0;
         return od0;
      }
   }
}


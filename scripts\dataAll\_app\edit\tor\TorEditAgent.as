package dataAll._app.edit.tor
{
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.edit.TorData;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit._def.EditProDefineGroup;
   import dataAll.pro.ProType;
   
   public class TorEditAgent
   {
      public var da:TorData;
      
      private var defArr:Array = [];
      
      private var noChangeObj:Object = {};
      
      private var lockAllB:Boolean = false;
      
      public var changeFun:Function = null;
      
      public function TorEditAgent()
      {
         super();
      }
      
      public static function get editProG() : EditProDefineGroup
      {
         return Gaming.defineGroup.editPro;
      }
      
      public function clearDataArr() : void
      {
         var d0:IO_TorEditDefine = null;
         for each(d0 in this.defArr)
         {
            if(d0 is TorEditDefine)
            {
               TorEditDefine.recoverPool(d0 as TorEditDefine);
            }
         }
         this.defArr.length = 0;
      }
      
      public function initByData(da0:TorData, gather0:String, hideHighB0:Boolean, showCa0:String = "") : void
      {
         var father0:* = null;
         var fatherCn0:String = null;
         var darr0:Array = null;
         var showArr0:Array = null;
         var d0:EditProDefine = null;
         var valueArr0:Array = null;
         var index0:int = 0;
         var value0:* = undefined;
         var child0:TorEditDefine = null;
         this.clearDataArr();
         this.da = da0;
         var fatherArr0:Array = editProG.getFatherNameArr(gather0);
         if(Boolean(fatherArr0))
         {
            for each(father0 in fatherArr0)
            {
               if(editProG.isHideFather(father0) == false)
               {
                  fatherCn0 = editProG.getFatherCn(gather0,father0);
                  darr0 = editProG.getArrByFather(father0);
                  showArr0 = [];
                  for each(d0 in darr0)
                  {
                     if(d0.getTorShow(hideHighB0,showCa0,da0.getTorSave().pn))
                     {
                        showArr0.push(d0);
                     }
                  }
                  if(showArr0.length > 0)
                  {
                     this.newDefByFather(father0,fatherCn0);
                     for each(d0 in showArr0)
                     {
                        this.addDefine(d0);
                        if(d0.type == ProType.ARRAY)
                        {
                           valueArr0 = da0.getValue(d0);
                           if(Boolean(valueArr0) && valueArr0.length > 0)
                           {
                              index0 = 0;
                              for each(value0 in valueArr0)
                              {
                                 child0 = TorEditDefine.getNewClass();
                                 child0.name = value0 as String;
                                 child0.index = index0;
                                 child0.setParent(d0);
                                 child0.cnName = da0.getChildUIValue(d0,child0);
                                 this.addDefine(child0);
                                 index0++;
                              }
                           }
                        }
                     }
                  }
               }
            }
         }
      }
      
      private function newDefByFather(name0:String, cn0:String) : void
      {
         var d0:TorEditDefine = TorEditDefine.getNewClass();
         d0.name = name0;
         d0.cnName = cn0 + " -------------------";
         this.addDefine(d0);
      }
      
      private function addDefine(d0:IO_TorEditDefine) : void
      {
         this.defArr.push(d0);
      }
      
      public function getDefArr() : Array
      {
         return this.defArr;
      }
      
      public function getUIData(index0:int) : IO_TorEditDefine
      {
         return ArrayMethod.getElement(this.defArr,index0,null,null) as IO_TorEditDefine;
      }
      
      public function getNewMe() : TorEditAgent
      {
         var a0:TorEditAgent = this.da.getTorEditAgent();
         a0.changeFun = this.changeFun;
         return a0;
      }
      
      public function getChangeBIO(d0:IO_TorEditDefine) : Boolean
      {
         if(this.lockAllB)
         {
            return false;
         }
         var proD0:EditProDefine = d0.getParent() as EditProDefine;
         if(!proD0)
         {
            proD0 = d0 as EditProDefine;
         }
         if(Boolean(proD0))
         {
            return this.getChangeB(proD0);
         }
         return false;
      }
      
      public function getChangeB(proD0:EditProDefine) : Boolean
      {
         if(this.lockAllB)
         {
            return false;
         }
         if(proD0.changeB == false)
         {
            return false;
         }
         return !this.getNoChangeInObj(proD0.name);
      }
      
      private function getNoChangeInObj(pro0:String) : Boolean
      {
         if(this.noChangeObj.hasOwnProperty(pro0))
         {
            return true;
         }
         return false;
      }
      
      public function addNoChange(pro0:String) : void
      {
         if(this.noChangeObj.hasOwnProperty(pro0) == false)
         {
            this.noChangeObj[pro0] = 0;
         }
      }
      
      public function addNoChangeDefPos(pos0:String) : void
      {
         var v0:* = undefined;
         var d0:EditProDefine = null;
         for each(v0 in this.defArr)
         {
            d0 = v0 as EditProDefine;
            if(Boolean(d0))
            {
               if(d0.proPos == pos0)
               {
                  this.addNoChange(d0.name);
               }
            }
         }
      }
      
      public function clearNoChange() : void
      {
         this.noChangeObj = {};
      }
      
      public function setLockAllB(bb0:Boolean) : void
      {
         this.lockAllB = bb0;
      }
      
      public function getLockAllB() : Boolean
      {
         return this.lockAllB;
      }
   }
}


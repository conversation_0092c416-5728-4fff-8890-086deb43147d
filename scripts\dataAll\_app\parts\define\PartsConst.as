package dataAll._app.parts.define
{
   import com.sounto.utils.ArrayMethod;
   import dataAll.equip.define.EquipColor;
   import dataAll.things.define.ThingsDefine;
   
   public class PartsConst
   {
      public static const minLv:int = 3;
      
      public static const cLv:int = 3;
      
      private static var partsCnNameFirstArr:Array = ["小","","初级","中级","高级","特级","超级","究级","闪耀·","绝世·","超凡·"];
      
      public function PartsConst()
      {
         super();
      }
      
      public static function getMaxPartsLevel() : int
      {
         return 93;
      }
      
      public static function getMaxDecomposeLv() : int
      {
         return 85;
      }
      
      public static function getComposeMustNum(lv0:int) : int
      {
         if(lv0 <= 20)
         {
            return 3;
         }
         if(lv0 < 30)
         {
            return 4;
         }
         if(lv0 < 40)
         {
            return 5;
         }
         if(lv0 < 50)
         {
            return 5;
         }
         if(lv0 < 60)
         {
            return 5;
         }
         if(lv0 < 69)
         {
            return 6;
         }
         if(lv0 < 72)
         {
            return 6;
         }
         if(lv0 == 84)
         {
            return 3;
         }
         if(lv0 == 87)
         {
            return 4;
         }
         if(lv0 == 90)
         {
            return 3;
         }
         return 6;
      }
      
      public static function getSpecialComposeMustNum(lv0:int) : int
      {
         var n0:int = 100;
         if(lv0 == 1)
         {
            n0 = 50;
         }
         else if(lv0 >= 2)
         {
            n0 = 3;
         }
         return n0;
      }
      
      public static function getSpecialDpsAddByLv(lv0:int) : Number
      {
         if(lv0 == 1)
         {
            return 0;
         }
         if(lv0 == 2)
         {
            return 0.1;
         }
         if(lv0 == 3)
         {
            return 0.2;
         }
         if(lv0 == 4)
         {
            return 0.3;
         }
         return 0;
      }
      
      public static function getMaxPartsLv(lv0:int) : Number
      {
         return int(Math.ceil((lv0 - minLv) / cLv) * cLv + minLv);
      }
      
      private static function getIconIndex(lv0:int) : int
      {
         var index0:int = 0;
         if(lv0 <= 51)
         {
            index0 = -1;
         }
         else if(lv0 <= 66)
         {
            index0 = 0;
         }
         else
         {
            index0 = (lv0 - 69) / 3 + 1;
         }
         return index0;
      }
      
      public static function getIconUrl(gripType0:String, lv0:int, beforeB0:Boolean = false) : String
      {
         var index0:int = getIconIndex(lv0);
         var name0:String = "";
         if(beforeB0)
         {
            if(index0 < 1)
            {
               index0 = 1;
            }
            name0 = gripType0 + "Parts_" + index0;
         }
         else
         {
            if(index0 < 1)
            {
               index0 = 1;
            }
            name0 = "stablerParts_" + index0;
         }
         return "ThingsIcon/" + name0;
      }
      
      public static function getCn(baseCn0:String, lv0:int) : String
      {
         var index0:int = getIconIndex(lv0);
         return ArrayMethod.getElementLimit(partsCnNameFirstArr,index0 + 1) + baseCn0;
      }
      
      public static function getIconColor(d0:ThingsDefine, lv0:int) : String
      {
         if(d0.baseLabel == PartsName.purgoldCpu)
         {
            return EquipColor.PURGOLD;
         }
         if(d0.baseLabel == PartsName.darkgoldCpu)
         {
            return EquipColor.DARKGOLD;
         }
         if(d0.isPartsSpecialB())
         {
            return EquipColor.BLACK;
         }
         if(d0.isPartsRareB())
         {
            return EquipColor.DARKGOLD;
         }
         if(lv0 >= 93)
         {
            return EquipColor.DARKGOLD;
         }
         if(lv0 >= 87)
         {
            return EquipColor.BLACK;
         }
         if(lv0 >= 69)
         {
            return EquipColor.RED;
         }
         if(lv0 < 69 && lv0 >= 54)
         {
            return EquipColor.PARTS_TYPE_ARR[int((lv0 - 54) / 3)];
         }
         return EquipColor.WHITE;
      }
      
      public static function test() : void
      {
         for(var i:int = 0; i < 100; i++)
         {
         }
      }
   }
}


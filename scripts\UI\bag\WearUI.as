package UI.bag
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.UIShow;
   import UI.bag.chargerShow.ChargerShowBox;
   import UI.bag.wear.FashionHDBox;
   import UI.bag.wear.NameChangeBox;
   import UI.bag.wear.PlayerInfoBar;
   import UI.bag.wear.WearProBox;
   import UI.bag.ItemsEditCtrl;
   import UI.base.AppNormalUI;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.heroImg.HeroEquipImgBox;
   import UI.base.label.LabelBox;
   import UI.base.loadBar.LoadBar;
   import UI.base.tip.TipBox;
   import UI.love.LoveBox;
   import UI.outfit.OutfitListBoard;
   import UI.partner.PartnerAIBoard;
   import UI.partner.growth.PartnerGrowthBoard;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.love.LoveData;
   import dataAll._app.partner.ability.PartnerAbility;
   import dataAll._app.vip.define.VipLevelDefine;
   import dataAll._player.PlayerData;
   import dataAll._player.base.PlayerBaseSave;
   import dataAll._player.base.PlayerMainSave;
   import dataAll._player.more.MorePlayerData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll._player.role.RoleName;
   import dataAll.body.define.HeroDefine;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.EquipDataSwapPan;
   import dataAll.equip.EquipPropertyData;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipPro;
   import dataAll.equip.define.EquipType;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.items.ItemsCompareData;
   import dataAll.pet.PetDataGroup;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import gameAll.level.LevelGroup;
   
   public class WearUI extends AppNormalUI
   {
      private var labelBox:LabelBox = new LabelBox();
      
      private var coverSp:Sprite;
      
      private var proBox:WearProBox = new WearProBox();
      
      private var dropBox:WearProBox = new WearProBox();
      
      private var outfitBox:OutfitListBoard = new OutfitListBoard();
      
      private var growthBox:PartnerGrowthBoard = new PartnerGrowthBoard();
      
      private var aiBox:PartnerAIBoard = new PartnerAIBoard();
      
      private var boxArr:Array = [this.proBox,this.dropBox,this.growthBox,this.aiBox,this.outfitBox];
      
      public var loveBox:LoveBox = new LoveBox();
      
      public var equipBox:WearEquipGripBox = new WearEquipGripBox();
      
      public var armsBox:ItemsGripBox = new ItemsGripBox();
      
      public var heroImgBox:HeroEquipImgBox = new HeroEquipImgBox();
      
      public var equipBackMc:MovieClip;
      
      public var showFashionBtn:NormalBtn = new NormalBtn();
      
      public var headBtn:NormalBtn = new NormalBtn();
      
      public var loveBtn:NormalBtn = new NormalBtn();
      
      public var loveGiftBtn:NormalBtn = new NormalBtn();
      
      public var attackBtn:NormalBtn = new NormalBtn();
      
      public var defenceBtn:NormalBtn = new NormalBtn();
      
      public var lockBtn:NormalBtn = new NormalBtn();
      
      public var uplevelBtn:NormalBtn = new NormalBtn();
      
      public var aiBtn:NormalBtn = new NormalBtn();
      
      public var otherBtn:NormalBtn = new NormalBtn();
      
      public var nameBtn:NormalBtn = new NormalBtn();
      
      private var leftLabelBox:LabelBox = new LabelBox();
      
      public var chargerShow:ChargerShowBox = new ChargerShowBox();
      
      public var titleTxt:TextField;
      
      public var suitTxt:TextField;
      
      public var lvTxt:TextField;
      
      public var dpsTxt:TextField;
      
      public var vipTxt:TextField;
      
      public var levelGiftBtn:SimpleButton;
      
      private var infoArr:Array = [];
      
      private var togeSp:Sprite = null;
      
      private var infoTag:Sprite = null;
      
      private var armsTag:Sprite = null;
      
      private var heroImgTag:Sprite = null;
      
      private var chargerTag:Sprite = null;
      
      private var showFashionBtnSp:MovieClip;
      
      private var headBtnSp:MovieClip;
      
      private var loveBtnSp:MovieClip;
      
      private var loveGiftBtnSp:MovieClip;
      
      private var attackBtnSp:MovieClip;
      
      private var defenceBtnSp:MovieClip;
      
      private var lockBtnSp:MovieClip;
      
      private var uplevelBtnSp:MovieClip;
      
      private var aiBtnSp:MovieClip;
      
      private var otherBtnSp:MovieClip;

      private var nameBtnSp:MovieClip;

      // 编辑按钮
      private var editButton:NormalBtn = null;
      
      private var nowSuitConverTip:String = "";
      
      public var canWearB:Boolean = true;
      
      private var fashionBox:FashionHDBox;
      
      public function WearUI()
      {
         super();
         UICn = "背包";
      }
      
      public function init() : void
      {
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["togeSp","coverSp","defenceBtnSp","nameBtnSp","attackBtnSp","aiBtnSp","otherBtnSp","uplevelBtnSp","lockBtnSp","loveGiftBtnSp","loveBtnSp","levelGiftBtn","headBtnSp","showFashionBtnSp","titleTxt","suitTxt","equipBackMc","infoTag","lvTxt","dpsTxt","vipTxt","armsTag","heroImgTag","chargerTag"];
         super.setImg(img0);
         addChild(this.labelBox);
         this.labelBox.x = 3;
         this.labelBox.y = 70;
         this.labelBox.arg.init(6,1,-6,0);
         this.labelBox.inData("bagLabelBtn2",["main","pro","drop","growth","ai","outfit"],["主要","属性","掉率","能力","AI","套件"]);
         this.labelBox.setChoose_byIndex(0);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         addChild(this.heroImgBox);
         addChild(this.armsBox);
         addChild(this.equipBox);
         addChild(this.chargerShow);
         this.heroImgBox.x = 146;
         this.heroImgBox.y = 168;
         this.armsBox.imgType = "HouseUI/armsGrip";
         this.armsBox.arg.init(2,3,4,4);
         this.armsBox.y = 210;
         this.armsBox.evt.setWantEvent(true,true,true,true,true,true);
         this.equipBox.evt.setWantEvent(true,true,true,true,true);
         ItemsGripMoveCtrl.addEvent_byItemsGripBox(this.armsBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.armsBox);
         ItemsGripUnlockCtrl.addEvent_byItemsGripBox(this.armsBox);
         ItemsEditCtrl.addEvent_byItemsGripBox(this.armsBox);
         ItemsGripMoveCtrl.addEvent_byItemsGripBox(this.equipBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.equipBox);
         ItemsEditCtrl.addEvent_byItemsGripBox(this.equipBox);
         ItemsGripTipCtrl.addEvent_byHeroImage(this.heroImgBox);
         this.heroImgBox.addEventListener(MouseEvent.MOUSE_OVER,this.heroImgOver);
         this.heroImgBox.addEventListener(MouseEvent.MOUSE_OUT,this.heroImgOut);
         this.levelGiftBtn.addEventListener(MouseEvent.CLICK,this.levelGiftClick);
         addChild(this.levelGiftBtn);
         addChild(this.showFashionBtn);
         this.showFashionBtn.setImg(this.showFashionBtnSp);
         this.showFashionBtn.addEventListener(MouseEvent.CLICK,this.showFashionClick);
         this.equipBackMc.stop();
         this.equipBox.setGrip_byOutside(img0);
         this.chargerShow.imgInit();
         NormalUICtrl.setTag(this.leftLabelBox,this.chargerTag);
         this.leftLabelBox.x -= 5;
         this.leftLabelBox.arg.init(2,1,-5,0);
         this.leftLabelBox.inData("shortLabelBtn2",["charger"],["携弹量"]);
         this.leftLabelBox.setChoose_byIndex(0);
         this.leftLabelBox.addEventListener(ClickEvent.ON_CLICK,this.leftLabelClick);
         NormalUICtrl.setTag(this.armsBox,this.armsTag);
         NormalUICtrl.setTag(this.heroImgBox,this.heroImgTag);
         NormalUICtrl.setTag(this.chargerShow,this.chargerTag);
         addChild(this.dpsTxt);
         addChild(this.loveBtn);
         this.loveBtn.setImg(this.loveBtnSp);
         this.loveBtn.setName("对话");
         this.loveBtn.addEventListener(MouseEvent.CLICK,this.loveBtnClick);
         addChild(this.loveGiftBtn);
         this.loveGiftBtn.setImg(this.loveGiftBtnSp);
         this.loveGiftBtn.setName("领奖");
         this.loveGiftBtn.addEventListener(MouseEvent.CLICK,this.loveGiftBtnClick);
         addChild(this.attackBtn);
         this.attackBtn.setImg(this.attackBtnSp);
         this.attackBtn.setName("提升");
         this.attackBtn.addEventListener(MouseEvent.CLICK,this.attackBtnClick);
         addChild(this.defenceBtn);
         this.defenceBtn.setImg(this.defenceBtnSp);
         this.defenceBtn.setName("提升");
         this.defenceBtn.addEventListener(MouseEvent.CLICK,this.attackBtnClick);
         this.addInfoList();
         addChild(this.headBtn);
         this.headBtn.setImg(this.headBtnSp);
         this.headBtn.setName("前往");
         this.headBtn.addEventListener(MouseEvent.CLICK,this.headBtnClick);
         addChild(this.uplevelBtn);
         this.uplevelBtn.setImg(this.uplevelBtnSp);
         this.uplevelBtn.setName("升级");
         this.uplevelBtn.addEventListener(MouseEvent.CLICK,this.uplevelBtnClick);
         addChild(this.lockBtn);
         this.lockBtn.setImg(this.lockBtnSp);
         this.lockBtn.setName("锁定");
         this.lockBtn.addEventListener(MouseEvent.CLICK,this.lockBtnClick);
         ItemsGripTipCtrl.addNormalBtnTip(this.lockBtn);
         addChild(this.aiBtn);
         this.aiBtn.setImg(this.aiBtnSp);
         this.aiBtn.setName("AI设置");
         this.aiBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.aiBtn);
         this.aiBtn.addEventListener(MouseEvent.CLICK,this.aiBtnClick);
         addChild(this.otherBtn);
         this.otherBtn.setImg(this.otherBtnSp);
         this.otherBtn.setName("");
         this.otherBtn.addEventListener(MouseEvent.CLICK,this.otherBtnClick);
         addChild(this.nameBtn);
         this.nameBtn.setImg(this.nameBtnSp);
         this.nameBtn.activedAndEnabled = false;
         this.nameBtn.addEventListener(MouseEvent.CLICK,this.nameBtnClick);
         ItemsGripTipCtrl.addNormalBtnTip(this.nameBtn);
         this.otherBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.otherBtn);
         this.dpsTxt.addEventListener(MouseEvent.MOUSE_OVER,this.barOver);
         this.dpsTxt.addEventListener(MouseEvent.MOUSE_OUT,this.barOut);
         addChild(this.attackBtn);
         addChild(this.defenceBtn);
         addChild(this.loveBtn);
         addChild(this.loveGiftBtn);
         this.loveBox.setCon(this);
         this.loveBox.setImg(Gaming.swfLoaderManager.getResource("LoveUI","loveBox"));
         this.loveBox.hide();
         this.loveBox.setSize(this,Gaming.uiGroup.bagUI);
         this.proBox.UILabel = "pro";
         this.proBox.setImgUrl("BasicUI/proBox");
         this.proBox.setCon(this);
         this.proBox.setEquipProArr(EquipPro.getNormalArr());
         this.proBox.hide();
         this.dropBox.UILabel = "drop";
         this.dropBox.setImgUrl("BasicUI/proBox");
         this.dropBox.setCon(this);
         this.dropBox.setEquipProArr(EquipPro.getDropArr(),true);
         this.dropBox.hide();
         this.outfitBox.UILabel = "outfit";
         this.outfitBox.setImgUrl("OutfitUI/listBoard");
         this.outfitBox.setCon(this);
         this.outfitBox.hide();
         this.growthBox.UILabel = "growth";
         this.growthBox.setImgUrl("PartnerUI/growthBoard");
         this.growthBox.setCon(this);
         this.growthBox.hide();
         this.aiBox.UILabel = "ai";
         this.aiBox.setImgUrl("PartnerUI/aiBoard");
         this.aiBox.setCon(this);
         this.aiBox.hide();
         this.setCover();
         FontDeal.dealOne(this.titleTxt);
         FontDeal.dealOne(this.lvTxt);
         FontDeal.dealOne(this.dpsTxt);
         FontDeal.dealOne(this.vipTxt);
         FontDeal.dealOne(this.suitTxt);

         // 创建编辑按钮
         this.createEditButton();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }

      // 创建编辑按钮
      private function createEditButton() : void
      {
         if(!this.editButton)
         {
            this.editButton = new NormalBtn();
            this.editButton.addEventListener(MouseEvent.CLICK, this.editButtonClick);

            addChild(this.editButton);

            try
            {
               this.editButton.setName("编辑");
               this.editButton.width = 40;
               this.editButton.height = 20;

               this.editButton.graphics.clear();
               this.editButton.graphics.beginFill(0xFF6600, 0.9);
               this.editButton.graphics.drawRoundRect(0, 0, 40, 20, 5, 5);
               this.editButton.graphics.endFill();

               if(this.editButton.textField)
               {
                  this.editButton.textField.textColor = 0xFFFFFF;
                  this.editButton.textField.size = 10;
                  this.editButton.textField.text = "编辑";
               }
            }
            catch(styleError:Error)
            {
               this.editButton.setName("编辑");
            }

            // 设置按钮位置
            this.editButton.x = 650;
            this.editButton.y = 50;
         }
      }

      // 编辑按钮点击事件
      private function editButtonClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.alertBox.showInfo("装备编辑功能已激活！");
      }
      
      public function setCover(str0:String = "") : void
      {
         if(str0 == "")
         {
            this.coverSp.visible = false;
         }
         else
         {
            this.coverSp.visible = true;
            this.coverSp["txt"].htmlText = str0;
         }
      }
      
      private function addInfoList() : void
      {
         var bar0:PlayerInfoBar = null;
         for(var i:int = 0; i < 8; i++)
         {
            bar0 = new PlayerInfoBar();
            bar0.addStyle("normal",Gaming.uiGroup.getBasicMovieClip("infoBar"));
            bar0.addStyle("love",Gaming.uiGroup.getBasicMovieClip("infoLoveBar"));
            addChild(bar0);
            this.infoArr[i] = bar0;
            bar0.index = i;
            bar0.mouseChildren = false;
            bar0.btn.visible = false;
            bar0.x = this.infoTag.x;
            bar0.y = this.infoTag.y + 30 * i;
            bar0.addEventListener(MouseEvent.MOUSE_OVER,this.barOver);
            bar0.addEventListener(MouseEvent.MOUSE_OUT,this.barOut);
         }
         this.loveBtn.y = this.infoTag.y + 180 + 3;
         this.loveGiftBtn.y = this.loveBtn.y;
         this.defenceBtn.y = this.infoTag.y + 150 + 3;
         this.attackBtn.y = this.infoTag.y + 120 + 3;
      }
      
      override public function show() : void
      {
         var box0:NormalUI = null;
         super.show();
         if(!Gaming.uiGroup.bagUI.visible)
         {
            Gaming.uiGroup.bagUI.show();
         }
         this.fleshAllBox();
         this.loveBox.hide();
         for each(box0 in this.boxArr)
         {
            if(box0.visible)
            {
               box0.show();
            }
         }
      }
      
      override public function hide() : void
      {
         if(Gaming.uiGroup.bagUI.visible)
         {
            Gaming.uiGroup.bagUI.hide();
         }
         super.hide();
         this.hideHDFashion();
         this.growthBox.removeHeroImg();
      }
      
      override public function getNowUICompareData(dd0:ItemsCompareData) : ItemsCompareData
      {
         return Gaming.PG.getUICompareData(dd0);
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      private function showBox(label0:String) : void
      {
         var box0:NormalUI = null;
         this.setCover();
         for each(box0 in this.boxArr)
         {
            if(box0.UILabel == label0)
            {
               box0.show();
            }
            else
            {
               box0.hide();
            }
         }
         this.labelBox.setChoose(label0);
         addChild(this.coverSp);
      }
      
      public function startLevel() : void
      {
      }
      
      public function overGamingClear() : void
      {
      }
      
      public function outLoginEvent() : void
      {
         if(Boolean(this.fashionBox))
         {
            this.fashionBox.outLoginEvent();
         }
         this.outfitBox.outLoginEvent();
         this.growthBox.outLoginEvent();
      }
      
      public function fleshAllBox() : void
      {
         if(!this.visible)
         {
            return;
         }
         this.fleshArmsEquip();
         this.fleshHeroImgBox();
         this.fleshConverBtn();
         this.fleshInfo();
         this.fleshShowFashionBtn();
         this.fleshUplevelBtn();
         this.fleshOtherBtn();
         this.fleshNameBtn();
      }
      
      private function fleshArmsEquip() : void
      {
         var grip0:ItemsGrid = null;
         var bb0:Boolean = false;
         var canUseB0:Boolean = false;
         this.armsBox.inData_byDataGroup(Gaming.PG.DATA.arms);
         this.equipBox.inData_byDataGroup(Gaming.PG.DATA.equip);
         for each(grip0 in this.equipBox.gripArr)
         {
            bb0 = EquipDataSwapPan.equipGripActivedPan(Gaming.PG.DATA,grip0.equipType);
            canUseB0 = Gaming.PG.DATA.panEquipCanUse(Gaming.LG,grip0.equipType);
            grip0.actived = bb0;
            if(canUseB0 == false)
            {
               grip0.setSmallIcon("noEquip");
            }
         }
      }
      
      private function fleshHeroImgBox() : void
      {
         this.heroImgBox.setEquip_byPlayerData(Gaming.PG.DATA);
      }
      
      private function fleshConverBtn(visibleB0:Boolean = true) : void
      {
         this.labelBox.getBtnByLabel("outfit").setName(Gaming.PG.DATA.outfit.getBtnName());
      }
      
      private function suitTxtOver(e:MouseEvent) : void
      {
         this.fleshConverBtn(true);
      }
      
      private function suitTxtOut(e:MouseEvent) : void
      {
         this.fleshConverBtn(false);
      }
      
      public function fleshInfo() : void
      {
         var main0:PlayerMainSave = null;
         var morePD0:MorePlayerData = null;
         var loveGift0:GiftAddDefineGroup = null;
         var vipDef0:VipLevelDefine = null;
         if(!visible)
         {
            return;
         }
         var isgamingB:Boolean = Gaming.LG.isGaming();
         var PD:NormalPlayerData = Gaming.PG.DATA;
         var heroPD:PlayerData = PD as PlayerData;
         var save0:PlayerBaseSave = PD.base.save;
         var hero_d0:HeroDefine = PD.heroData.def;
         var heroB0:Boolean = PD is PlayerData;
         var str0:String = "";
         var v0:String = "";
         var per0:String = "";
         str0 += "角色";
         v0 += PD.heroData.getWearCn2();
         this.headBtn.visible = isgamingB == false && heroB0;
         this.loveBtn.visible = false;
         this.loveGiftBtn.visible = false;
         this.attackBtn.visible = false;
         this.defenceBtn.visible = false;
         if(heroB0)
         {
            str0 += ComMethod.color("\n称号","#00FF00");
            per0 += "\n";
            v0 += "\n" + heroPD.head.getNowHeadCn("无",true);
         }
         str0 += "\n生命";
         per0 += "\n";
         v0 += "\n" + NumberMethod.toBigWan(PD.base.getMaxLife());
         str0 += "\n头部防御";
         per0 += "\n";
         v0 += "\n" + NumberMethod.toBigWan(PD.base.getHeadDefence());
         str0 += "\n经验";
         per0 += "\n" + PD.base.getNowExp() / PD.base.getNextMaxExp();
         v0 += "\n" + NumberMethod.toBigWan(PD.base.getNowExp()) + "/" + NumberMethod.toBigWan(PD.base.getNextMaxExp());
         if(heroB0)
         {
            main0 = heroPD.main.save;
            str0 += "\n幸运值";
            per0 += "\n";
            v0 += "\n" + PD.getDropMerge().lottery;
            this.loveBtn.visible = false;
            heroPD.fleshMaxDps();
         }
         else
         {
            morePD0 = PD as MorePlayerData;
            str0 += "\n攻击能力";
            per0 += "\n" + morePD0.partner.getWearPointPer(PartnerAbility.attack);
            v0 += "\n" + morePD0.partner.getWearPointStr(PartnerAbility.attack);
            str0 += "\n防御能力";
            per0 += "\n" + morePD0.partner.getWearPointPer(PartnerAbility.defence);
            v0 += "\n" + morePD0.partner.getWearPointStr(PartnerAbility.defence);
            this.attackBtn.visible = !isgamingB;
            this.defenceBtn.visible = !isgamingB;
         }
         var loveDa0:LoveData = PD.love;
         this.loveBtn.visible = false;
         this.loveGiftBtn.visible = false;
         if(loveDa0.isOpenB())
         {
            str0 += "\n" + loveDa0.getCn();
            per0 += "\n" + loveDa0.getLovePer();
            v0 += "\n" + loveDa0.getLoveStr();
            this.loveBtn.visible = true;
            this.loveBtn.actived = PD.isP1() == false && !isgamingB;
            if(loveDa0.hadGivingB())
            {
               loveGift0 = loveDa0.getCanGetGift();
               if(loveGift0.haveDataB())
               {
                  this.loveGiftBtn.visible = true;
                  this.loveGiftBtn.actived = !isgamingB;
               }
            }
         }
         if(heroB0)
         {
            str0 += "\n虚天塔";
            per0 += "\n";
            v0 += "\n" + heroPD.tower.unend.getWinLv() + "层";
         }
         this.showInfoBy(str0,v0,per0,hero_d0);
         if(Boolean(this.lvTxt))
         {
            this.lvTxt.htmlText = "Lv." + ComMethod.color(save0.level + "","#FFFF00");
            this.dpsTxt.htmlText = "战斗力 " + ComMethod.color(NumberMethod.toBigWan(PD.getDps()) + "","#FFFF00");
            vipDef0 = Gaming.PG.da.vip.def;
            if(vipDef0.getTrueLevel() > 0)
            {
               this.vipTxt.htmlText = "<b>VIP</b>.<b>" + vipDef0.getTrueLevel() + "</b>";
            }
            else
            {
               this.vipTxt.htmlText = "";
            }
            this.levelGiftBtn.visible = heroB0 && heroPD.gift.haveNewGiftGetB();
            this.fleshLeftLabel();
         }
         var suitText0:String = PD.equip.getSuitTitleText();
         this.suitTxt.htmlText = "";
         this.suitTxt.addEventListener(MouseEvent.MOUSE_OVER,this.suitTxtOver);
         this.heroImgBox.showBackLight(PD.equip.getSuitColor());
      }
      
      private function getFoolDps(PD0:NormalPlayerData) : Number
      {
         var dps0:Number = PD0.getDps();
         var uid0:String = Gaming.getUid();
         var v0:int = int(uid0.substr(uid0.length - 2));
         if(v0 > 50)
         {
            dps0 *= v0 / 10;
         }
         else if(v0 > 0)
         {
            dps0 /= v0 / 10;
         }
         return NumberMethod.toFixed(dps0,0);
      }
      
      private function showInfoBy(label0:String, v0:String, per0:String, hero_d0:HeroDefine) : void
      {
         var n:* = undefined;
         var bar0:PlayerInfoBar = null;
         var label_arr0:Array = label0.split("\n");
         var v_arr0:Array = v0.split("\n");
         var per_arr0:Array = per0.split("\n");
         for(n in this.infoArr)
         {
            bar0 = this.infoArr[n];
            bar0.changeStyle("normal");
            if(Boolean(label_arr0[n]))
            {
               bar0.setTopText(label_arr0[n]);
               bar0.setText(v_arr0[n]);
               bar0.setPer(Number(per_arr0[n]));
            }
            else
            {
               bar0.clearData();
            }
         }
      }
      
      private function fleshShowFashionBtn() : void
      {
         var dg0:EquipDataGroup = Gaming.PG.DATA.equip;
         this.showFashionBtn.isChosen = dg0.saveGroup.showFashionB;
         this.showFashionBtn.visible = dg0.haveFashionB();
      }
      
      private function perChangeToText(v0:Number) : String
      {
         if(v0 < 0.34)
         {
            return "低";
         }
         if(v0 < 0.7)
         {
            return "中";
         }
         return "高";
      }
      
      private function fleshUplevelBtn() : void
      {
         var lockB0:Boolean = false;
         var PD:NormalPlayerData = Gaming.PG.DATA;
         if(Gaming.LG.isGaming())
         {
            this.lockBtn.visible = false;
            this.uplevelBtn.visible = false;
         }
         else
         {
            lockB0 = PD.base.save.lockB;
            if(lockB0)
            {
               this.lockBtn.setName("解锁");
               this.lockBtn.tipString = "";
               this.lockBtn.visible = true;
               this.uplevelBtn.visible = true;
               this.uplevelBtn.actived = PD.base.canUplevelB();
            }
            else
            {
               this.lockBtn.setName("锁定");
               this.lockBtn.tipString = "锁定人物等级后，人物将不再自动升级，需要你进行手动升级。";
               this.lockBtn.visible = PD.base.canLockB();
               this.uplevelBtn.visible = false;
            }
         }
      }
      
      private function lockBtnClick(e:MouseEvent) : void
      {
         var PD:NormalPlayerData = Gaming.PG.DATA;
         PD.base.save.lockB = !PD.base.save.lockB;
         this.fleshUplevelBtn();
      }
      
      private function uplevelBtnClick(e:MouseEvent) : void
      {
         var PD:NormalPlayerData = Gaming.PG.DATA;
         var bb0:Boolean = PD.base.typeUplevel();
         if(bb0)
         {
            UIOrder.playSuccessSound();
            this.fleshAllBox();
            Gaming.uiGroup.bagUI.fleshNowBox();
         }
      }
      
      private function loveBtnClick(e:MouseEvent) : void
      {
         this.loveBox.show();
      }
      
      private function loveGiftBtnClick(e:MouseEvent) : void
      {
         var bb0:Boolean = false;
         var PD:NormalPlayerData = Gaming.PG.DATA;
         var gift0:GiftAddDefineGroup = PD.love.getCanGetGift();
         if(gift0.haveDataB())
         {
            bb0 = GiftAddit.addAndAutoBagSpacePan(gift0);
            if(bb0)
            {
               PD.love.save.setGiftBByLvArr(gift0.getExtraArr(),true);
               this.fleshInfo();
               Gaming.uiGroup.bagUI.fleshNowBox();
            }
         }
      }
      
      private function fleshOtherBtn() : void
      {
         var aiB0:Boolean = false;
         var PD:NormalPlayerData = Gaming.PG.DATA;
         this.aiBtn.actived = PD.partner.aiOpenB();
         this.aiBtn.tipString = PD.partner.getAiOpenError();
         if(PD.isMainPlayerB())
         {
            aiB0 = Gaming.PG.da.main.getAi();
            this.otherBtn.actived = true;
            this.otherBtn.setName("开启AI战斗" + (aiB0 ? ComMethod.color("<b>√</b>","#00FF00") : ""));
            this.otherBtn.tipString = LevelGroup.getCanOpenAIStr();
            if(Gaming.LG.canOpenAI() == false)
            {
               this.otherBtn.actived = false;
               if(LevelGroup.getOpenAITaskB() == false)
               {
                  this.otherBtn.setName("AI功能未开启");
               }
               else
               {
                  this.otherBtn.setName("当前AI被禁用");
               }
            }
         }
         else
         {
            this.otherBtn.tipString = "";
            this.otherBtn.actived = false;
            this.otherBtn.setName("默认开启AI战斗");
         }
      }
      
      private function attackBtnClick(e:MouseEvent) : void
      {
         if(this.attackBtn.actived)
         {
            this.showBox("growth");
         }
      }
      
      private function aiBtnClick(e:MouseEvent) : void
      {
         if(this.aiBtn.actived)
         {
            this.showBox("ai");
         }
      }
      
      private function otherBtnClick(e:MouseEvent) : void
      {
         if(this.otherBtn.actived)
         {
            Gaming.PG.da.main.swapAi();
            this.fleshOtherBtn();
         }
      }
      
      private function fleshNameBtn() : void
      {
         this.titleTxt.text = Gaming.PG.DATA.heroData.getWearCn();
         if(Gaming.PG.DATA.isP1())
         {
            this.nameBtn.visible = true;
            this.nameBtn.tipString = Gaming.PG.da.main.getNameChangeTip();
            this.nameBtn.actived = Gaming.PG.da.main.save.changeNameB == false;
         }
         else
         {
            this.nameBtn.visible = false;
         }
      }
      
      private function nameBtnClick(e:MouseEvent) : void
      {
         if(this.nameBtn.actived)
         {
            NameChangeBox.getterNamePan(Gaming.PG.DATA.base,this.yesNameChange);
         }
      }
      
      private function yesNameChange() : void
      {
         this.fleshNameBtn();
      }
      
      private function headBtnClick(e:MouseEvent) : void
      {
         UIShow.showByLabel("head");
         Gaming.uiGroup.headUI.showBox("have");
      }
      
      private function barOver(e:MouseEvent) : void
      {
         var index0:int = 0;
         var lifeRate0:Number = NaN;
         var expValue0:Number = NaN;
         var tipBox0:TipBox = null;
         var pd0:NormalPlayerData = Gaming.PG.DATA;
         var mainPd0:PlayerData = pd0 as PlayerData;
         var morePd0:MorePlayerData = pd0 as MorePlayerData;
         var heroB0:Boolean = pd0 is PlayerData;
         var hero_d0:HeroDefine = pd0.heroData.def;
         var me0:EquipPropertyData = pd0.getHeroMerge();
         var petAdd0:Object = PetDataGroup.getZeroAddData();
         if(heroB0)
         {
            petAdd0 = (pd0 as PlayerData).pet.tempAddData;
         }
         var allMe0:EquipPropertyData = Gaming.PG.da.getHeroMerge();
         var str0:String = "";
         if(e.target is LoadBar)
         {
            index0 = int(e.target.index);
            if(heroB0)
            {
               index0 -= 1;
            }
            if(index0 != 0)
            {
               if(index0 == 1)
               {
                  lifeRate0 = pd0.base.getLifeRate();
                  if(Boolean(morePd0))
                  {
                     if(allMe0.moreLifeMul > 0)
                     {
                        str0 += "\n队友生命值加成：<yellow " + TextWay.numberToPer(allMe0.moreLifeMul,0) + "/>";
                     }
                  }
                  if(me0.vipDef.lifeMul > 0)
                  {
                     str0 += "\nVIP生命加成：<yellow " + me0.vipDef.getValueStringByPro("lifeMul") + "/>";
                  }
                  if(lifeRate0 > 0)
                  {
                     str0 += "\n生命回复：<yellow " + lifeRate0 + "点/秒/>";
                  }
                  if(me0.life > 0)
                  {
                     str0 += "\n装备生命值加成：<yellow " + (me0.life - petAdd0.life) + "/>";
                  }
                  if(me0.lifeMul > 0)
                  {
                     str0 += "\n装备生命值加成：<yellow " + TextWay.numberToPer(me0.lifeMul,0) + "/>";
                  }
                  if(me0.lifeAll > 0)
                  {
                     str0 += "\n其他生命值加成：<yellow " + TextWay.numberToPer(me0.lifeAll,0) + "/>";
                  }
                  if(petAdd0.life > 0)
                  {
                     str0 += "\n尸宠对生命值的加成：<yellow " + petAdd0.life + "/>";
                  }
                  str0 += Gaming.PG.da.union.getLifeMulString();
                  if(me0.dodge > 0)
                  {
                     str0 += "\n闪避值：<yellow " + TextWay.numberToPer(me0.dodge,0) + "/><purple (上限" + TextWay.numberToPer(EquipPro.getDodgeMax(),0) + ")/>";
                  }
                  if(me0.fightDedut > 0)
                  {
                     str0 += "\n近战伤害减免：<yellow " + TextWay.numberToPer(me0.fightDedut,0) + "/><purple (上限" + TextWay.numberToPer(EquipPro.getFightDedutMax(),0) + ")/>";
                  }
                  if(me0.bulletDedut > 0)
                  {
                     str0 += "\n防弹值：<yellow " + TextWay.numberToPer(me0.bulletDedut,0) + "/><purple (上限" + TextWay.numberToPer(EquipPro.getBulletDedutMax(),0) + ")/>";
                  }
                  if(me0.skillDedut > 0)
                  {
                     str0 += "\n防化值：<yellow " + TextWay.numberToPer(me0.skillDedut,0) + "/><purple (上限" + TextWay.numberToPer(EquipPro.getSkillDedutMax(),0) + ")/>";
                  }
               }
               else if(index0 == 2)
               {
                  if(me0.vipDef.defenceMul > 0)
                  {
                     str0 += "\nVIP防御加成：<yellow " + me0.vipDef.getValueStringByPro("defenceMul") + "/>";
                  }
                  str0 += "\n头部减伤百分比：<yellow " + int(pd0.base.getHeadHurtMul() * 100) + "%/>";
               }
               else if(index0 == 3)
               {
                  expValue0 = pd0.getDropMerge().expMul;
                  if(expValue0 > 0)
                  {
                     str0 += "经验获取加成：<yellow " + TextWay.numberToPer(expValue0,0) + "/>";
                  }
                  if(allMe0.vipDef.expMul > 0)
                  {
                     str0 += "\nVIP经验加成：<yellow " + allMe0.vipDef.getValueStringByPro("expMul") + "/>";
                  }
               }
               else if(index0 == 4)
               {
                  if(heroB0)
                  {
                     str0 += "对橙色红色稀有武器装备、随机属性黑色武器、黑色武器装备碎片、大部分材料、副手装置的掉率加成：" + TextWay.numberToPer(me0.lottery / 100,0);
                  }
               }
               else if(index0 == 5)
               {
                  if(heroB0)
                  {
                     str0 += pd0.love.getOverTip();
                  }
               }
               else if(index0 == 6)
               {
                  if(heroB0 == false)
                  {
                     str0 += pd0.love.getOverTip();
                  }
               }
            }
         }
         else if(e.target == this.dpsTxt)
         {
            if(Boolean(mainPd0))
            {
               str0 += "\n历史最大战斗力：<yellow " + NumberMethod.toBigWan(mainPd0.main.save.maxDp) + "/>";
               if(me0.dpsWhole > 0)
               {
                  str0 += "\n全体战斗力加成：<yellow " + TextWay.numberToPer(me0.dpsWhole,3) + "/>";
               }
            }
            if(Boolean(morePd0))
            {
               if(allMe0.moreDpsMul > 0)
               {
                  str0 += "\n队友战斗力加成：<yellow " + TextWay.numberToPer(allMe0.moreDpsMul,0) + "/>";
               }
            }
            str0 += "\n99级增伤衰减系数：<yellow " + RoleName.getUnderHurtMulLimit(pd0.getRoleName()) + "/>";
            if(me0.damageMul > 0)
            {
               str0 += "\n穿透伤害：<yellow " + TextWay.numberToPer(me0.damageMul,0) + "/>";
            }
            if(me0.vipDef.dpsMul > 0)
            {
               str0 += "\nVIP战斗力加成：<yellow " + me0.vipDef.getValueStringByPro("dpsMul") + "/>";
            }
            if(me0.dps > 0)
            {
               str0 += "\n装备战斗力加成：<yellow " + (me0.dps - petAdd0.dps) + "/>";
            }
            if(me0.dpsMul > 0)
            {
               str0 += "\n装备战斗力加成：<yellow " + TextWay.numberToPer(me0.dpsMul,0) + "/>";
            }
            if(me0.zodiacArmsHurtAdd > 0)
            {
               str0 += "\n生肖武器伤害加成：<yellow " + TextWay.numberToPer(me0.zodiacArmsHurtAdd,0) + "/>";
            }
            if(petAdd0.dps > 0)
            {
               str0 += "\n尸宠对战斗力的加成：<yellow " + petAdd0.dps + "/>";
            }
            if(Boolean(mainPd0))
            {
               str0 += mainPd0.union.getDpsMulString();
            }
            if(me0.attackGap > 0)
            {
               str0 += "\n射击速度加成：<yellow " + TextWay.numberToPer(me0.attackGap,0) + "/>";
            }
            if(me0.critPro3 > 0)
            {
               str0 += "\n3倍暴击概率：<yellow " + TextWay.numberToPer(me0.critPro3,0) + "/>";
            }
            if(me0.moveMul > 0)
            {
               str0 += "\n移动速度加成：<yellow " + TextWay.numberToPer(me0.moveMul,0) + "/>";
            }
            if(me0.cdMul > 0)
            {
               str0 += "\n技能回复速度加成：<yellow " + TextWay.numberToPer(me0.cdMul,0) + "/>";
            }
            if(me0.vehicleDpsMul > 0)
            {
               str0 += "\n载具攻击力加成：<yellow " + TextWay.numberToPer(me0.vehicleDpsMul,0) + "/>";
            }
            str0 += "\n\n" + me0.getDropProTip(pd0);
         }
         if(str0.indexOf("\n") == 0)
         {
            str0 = str0.replace("\n","");
         }
         if(str0 != "")
         {
            tipBox0 = Gaming.uiGroup.tipBox;
            tipBox0.textTip.setText(str0);
            tipBox0.textTip.show();
            tipBox0.followMouseB = true;
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function barOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function levelGiftClick(e:MouseEvent) : void
      {
         if(!Gaming.LG.isGaming())
         {
            UIShow.showByLabel("gift");
            Gaming.uiGroup.giftUI.showBox("level");
         }
      }
      
      private function outfitOver(e:MouseEvent) : void
      {
         var str0:String = Gaming.PG.DATA.outfit.getTip();
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
      }
      
      private function heroImgOver(e:MouseEvent) : void
      {
         this.showHDFashion();
      }
      
      private function heroImgOut(e:MouseEvent) : void
      {
         this.hideHDFashion();
      }
      
      private function showFashionClick(e:MouseEvent) : void
      {
         var dg0:EquipDataGroup = Gaming.PG.DATA.equip;
         dg0.saveGroup.showFashionB = !dg0.saveGroup.showFashionB;
         this.fleshShowFashionBtn();
         this.fleshHeroImgBox();
         this.showHDFashion(2);
      }
      
      public function showHDFashionFastPan(da0:EquipData) : void
      {
         if(Boolean(da0))
         {
            if(da0.save.getDefine().type == EquipType.FASHION)
            {
               this.showHDFashion(2);
               Gaming.uiGroup.moreBox.fleshIconOnly();
            }
         }
      }
      
      private function showHDFashion(hideT0:Number = -1) : void
      {
         var ed0:EquipDefine = Gaming.PG.DATA.equip.getWearShowFashionDef();
         if(Boolean(ed0) && ed0.hd)
         {
            if(this.fashionBox == null)
            {
               this.fashionBox = new FashionHDBox();
               this.fashionBox.setCon(this);
            }
            this.fashionBox.setFashion(ed0,hideT0);
            this.fashionBox.show();
         }
      }
      
      private function hideHDFashion() : void
      {
         if(Boolean(this.fashionBox))
         {
            this.fashionBox.hide();
         }
      }
      
      private function fleshLeftLabel() : void
      {
         var label0:String = this.leftLabelBox.nowLabel;
         this.chargerShow.visible = false;
         if(label0 == "charger")
         {
            this.chargerShow.visible = true;
            this.chargerShow.inData(Gaming.PG.DATA.charger);
         }
         else if(label0 == "toge")
         {
         }
      }
      
      private function leftLabelClick(e:ClickEvent) : void
      {
         this.leftLabelBox.setChoose(e.label);
         this.fleshLeftLabel();
      }
      
      override public function set visible(bb0:Boolean) : void
      {
         super.visible = bb0;
         if(bb0)
         {
            this.heroImgBox.play();
         }
         else
         {
            this.heroImgBox.stop();
         }
      }
      
      public function stopAction() : void
      {
         this.heroImgBox.stop();
      }
      
      public function pauseAction() : void
      {
         if(visible)
         {
            this.heroImgBox.play();
         }
      }
      
      public function FTimer() : void
      {
         if(this.visible)
         {
            this.heroImgBox.Ftimer();
            this.loveBox.FTimer();
            if(Boolean(this.fashionBox))
            {
               this.fashionBox.FTimer();
            }
         }
      }
   }
}


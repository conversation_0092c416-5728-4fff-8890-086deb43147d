package dataAll._app.active
{
   import dataAll._app.active.define.ActiveGiftDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class ActiveGiftData
   {
      private var def:ActiveGiftDefine;
      
      public var enoughB:Boolean = false;
      
      public var haveGiftB:Boolean = false;
      
      public function ActiveGiftData()
      {
         super();
      }
      
      public function get name() : String
      {
         return this.def.name;
      }
      
      public function get must() : Number
      {
         return this.def.must;
      }
      
      public function init(d0:ActiveGiftDefine) : void
      {
         this.def = d0;
      }
      
      public function getGift() : GiftAddDefineGroup
      {
         return this.def.gift;
      }
   }
}


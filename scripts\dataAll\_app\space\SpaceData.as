package dataAll._app.space
{
   import UI.base.button.BtnAgent;
   import UI.base.button.BtnAgentGroup;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.space.achieve.SpaceAchieveDefine;
   import dataAll._app.space.astron.AstronData;
   import dataAll._app.space.craft.CraftData;
   import dataAll._app.space.craft.CraftDataGroup;
   import dataAll._app.task.TaskData;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._player.IO_PlayerLevelGetter;
   import dataAll._player.PlayerData;
   import dataAll.body.attack.HurtData;
   import dataAll.level.LevelDiffGetting;
   import dataAll.level.define.LevelDefine;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   import gameAll.bullet.BulletBody;
   
   public class SpaceData
   {
      public var playerData:PlayerData;
      
      public var save:SpaceSave;
      
      public var craft:CraftDataGroup = new CraftDataGroup();
      
      public var count:SpaceLevelCount = new SpaceLevelCount();
      
      private var diffAgent:BtnAgentGroup = null;
      
      private var nowAstron:AstronData = null;
      
      public function SpaceData()
      {
         super();
      }
      
      public function inData_bySave(s0:SpaceSave) : void
      {
         this.save = s0;
         this.craft.inData_bySave(s0.craft);
      }
      
      public function newWeek(str0:String) : void
      {
         this.save.setMapProAll("exp",0);
         this.craft.newWeek();
      }
      
      public function newDayCtrl(timeDa0:StringDate) : void
      {
      }
      
      public function openUI(timeStr0:String) : void
      {
      }
      
      public function startLevel() : void
      {
         this.count.clearData();
      }
      
      public function overGamingClear() : void
      {
         this.craft.overGamingClear();
      }
      
      public function getNowCraft() : CraftData
      {
         return this.craft.getNowData();
      }
      
      public function getNowAstron() : AstronData
      {
         var c0:CraftData = this.getNowCraft();
         if(Boolean(c0))
         {
            if(this.nowAstron == null)
            {
               this.nowAstron = new AstronData();
            }
            this.nowAstron.inCraftData(c0,this.playerData);
            return this.nowAstron;
         }
         return null;
      }
      
      public function addNowCraftExp(exp0:Number) : void
      {
         var da0:CraftData = this.getNowCraft();
         if(Boolean(da0))
         {
            da0.addExp(exp0);
         }
      }
      
      public function levelWin(lg0:IO_PlayerLevelGetter) : void
      {
         var taskDa0:TaskData = null;
         var diff0:int = 0;
         var mapName0:String = lg0.getNowWorldMapName();
         var d0:WorldMapDefine = Gaming.defineGroup.worldMap.getDefine(mapName0);
         if(d0.isSpaceB())
         {
            taskDa0 = lg0.getTaskData();
            if(taskDa0 == null)
            {
               this.addMapWinNum(mapName0,1);
            }
            diff0 = int(lg0.getDiff());
            this.unlockMapDiff(mapName0,diff0 + 1);
         }
      }
      
      public function getUIMapDefArr() : Array
      {
         return Gaming.defineGroup.worldMap.getArrByFather("space");
      }
      
      public function getMapWinNum(name0:String) : Number
      {
         return this.save.getMapPro(name0,"win",0);
      }
      
      private function addMapWinNum(name0:String, v0:int) : void
      {
         var now0:Number = this.getMapWinNum(name0);
         now0 += v0;
         this.save.setMapPro(name0,"win",now0);
      }
      
      public function getMapUnlock(name0:String) : Boolean
      {
         var d0:WorldMapDefine = Gaming.defineGroup.worldMap.getDefine(name0);
         if(d0.unlockTask != "")
         {
            return this.playerData.task.isCompleteB(d0.unlockTask);
         }
         return false;
      }
      
      public function getMapDiff(name0:String) : int
      {
         var v0:int = this.save.getMapPro(name0,"diff",0);
         if(v0 <= 0)
         {
            if(this.getMapWinNum(name0) > 0)
            {
               v0 = 1;
            }
         }
         return v0;
      }
      
      public function getMapDiffWinB(name0:String, diff0:int) : Boolean
      {
         var unlock0:int = this.getMapDiff(name0);
         if(unlock0 >= diff0 + 1)
         {
            return true;
         }
         return false;
      }
      
      public function unlockMapDiff(name0:String, diff0:int) : Boolean
      {
         var now0:Number = this.getMapDiff(name0);
         if(now0 < diff0)
         {
            this.save.setMapPro(name0,"diff",diff0);
            return true;
         }
         return false;
      }
      
      public function getMapExp(name0:String) : Number
      {
         return this.save.getMapPro(name0,"exp",0);
      }
      
      public function getMapExpMaxAllDiff(name0:String) : Number
      {
         return SpaceDiff.getMapExpMax(name0,SpaceDiff.getDiffMax());
      }
      
      public function getMapExpSurplus(name0:String, diff0:int) : Number
      {
         var v0:Number = SpaceDiff.getMapExpMax(name0,diff0) - this.getMapExp(name0);
         if(v0 < 0)
         {
            v0 = 0;
         }
         return v0;
      }
      
      private function addMapExp(name0:String, v0:Number) : void
      {
         var now0:Number = this.getMapExp(name0);
         now0 += v0;
         this.save.setMapPro(name0,"exp",now0);
      }
      
      public function getDiffAgent(name0:String, clickFun:Function) : BtnAgentGroup
      {
         var a0:BtnAgent = null;
         var g0:BtnAgentGroup = this.diffAgent;
         if(g0 == null)
         {
            g0 = new BtnAgentGroup();
            g0.newNum(SpaceDiff.getDiffMax() + 1);
         }
         var unlockDiff0:int = this.getMapDiff(name0);
         var arr0:Array = g0.getArr();
         var mapD0:WorldMapDefine = Gaming.defineGroup.worldMap.getDefine(name0);
         for(var i:int = 0; i < arr0.length; i++)
         {
            a0 = arr0[i];
            a0.name = i + "";
            a0.cn = LevelDiffGetting.getCnName(i,MapMode.REGULAR);
            a0.tipString = "每周经验获取上限：<green " + SpaceDiff.getMapExpMax(name0,i) + "/>";
            a0.smallIcon = "";
            if(unlockDiff0 >= i)
            {
               a0.actived = true;
               if(unlockDiff0 >= i + 1)
               {
                  a0.smallIcon = "yes";
               }
            }
            else
            {
               a0.actived = false;
               a0.tipString = "<red 通关上一难度后开放/>\n\n" + a0.tipString;
            }
         }
         g0.name = name0;
         g0.title = mapD0.cnName;
         g0.clickFun = clickFun;
         return g0;
      }
      
      public function killEnemyEvent(b0:IO_NormalBody, b1:IO_NormalBody, h0:HurtData, lg0:IO_PlayerLevelGetter) : IO_NormalBody
      {
         var c0:CraftData = null;
         var levelD0:LevelDefine = null;
         var expMul0:Number = NaN;
         var exp0:Number = NaN;
         var mapExpSurplus0:Number = NaN;
         var upgradeB0:Boolean = false;
         var mapName0:String = null;
         var bdat0:NormalBodyData = b0.getData();
         if(bdat0.noNumB == false)
         {
            c0 = this.getNowCraft();
            levelD0 = Gaming.LG.getLevelDefineNull();
            if(Boolean(c0) && Boolean(levelD0))
            {
               ++this.count.enemy;
               expMul0 = bdat0.expValue;
               if(expMul0 > 2 || expMul0 <= 0)
               {
                  expMul0 = 1;
               }
               exp0 = Math.round(bdat0.maxLife * expMul0 * 0.3 * levelD0.drop.exp);
               mapExpSurplus0 = this.getMapExpSurplus(lg0.getNowWorldMapName(),lg0.getDiff());
               if(exp0 > mapExpSurplus0)
               {
                  exp0 = mapExpSurplus0;
               }
               if(exp0 > 0)
               {
                  if(c0.expIsFillB() == false)
                  {
                     mapName0 = lg0.getNowWorldMapName();
                     this.addMapExp(mapName0,exp0);
                     this.count.exp += exp0;
                  }
                  upgradeB0 = c0.addExp(exp0);
                  if(upgradeB0)
                  {
                     return c0.getBody();
                  }
               }
            }
         }
         this.killEnemyCount(b0,b1,h0,lg0);
         return null;
      }
      
      private function killEnemyCount(b0:IO_NormalBody, b1:IO_NormalBody, h0:HurtData, lg0:IO_PlayerLevelGetter) : void
      {
         var haveSilverShieldB0:Boolean = false;
         var bu0:BulletBody = h0.fromBullet;
         if(Boolean(bu0))
         {
            if(bu0.define.name == "silverScreenBullet")
            {
               this.save.c.addNum("screenKill",1);
               if(b0.getData().isBossB())
               {
                  this.save.c.addNum("screenKillB",1);
               }
            }
         }
         if(Boolean(b1))
         {
            haveSilverShieldB0 = Boolean(b1.getState().getState_bySkillName("silverShield"));
            if(haveSilverShieldB0)
            {
               this.save.c.addNum("shieldKill",1);
            }
         }
      }
      
      public function setAchieveGiftB(name0:String) : void
      {
         ArrayMethod.addNoRepeatInArr(this.save.cgArr,name0);
         this.save.c.addNum("cg",1);
      }
      
      public function getAchieveGiftAllNum() : int
      {
         return this.save.c.getAttribute("cg");
      }
      
      public function getAchieveGiftB(name0:String) : Boolean
      {
         return this.save.cgArr.indexOf(name0) >= 0;
      }
      
      public function isAchieveCompleteB(d0:SpaceAchieveDefine) : Boolean
      {
         var v0:Number = this.getAchieveValue(d0);
         var must0:Number = d0.must;
         return v0 >= must0;
      }
      
      public function getAchieveValue(d0:SpaceAchieveDefine) : Number
      {
         return this["get_" + d0.fun](d0);
      }
      
      private function get_craftMaxLv(d0:SpaceAchieveDefine = null) : int
      {
         return this.craft.getCraftMaxLv();
      }
      
      private function get_mapWin(d0:SpaceAchieveDefine) : int
      {
         return this.getMapNumByWinMust(d0.extra);
      }
      
      private function get_diffWinNum(d0:SpaceAchieveDefine) : int
      {
         var md0:WorldMapDefine = null;
         var num0:int = 0;
         var diffMust0:int = d0.extra;
         var darr0:Array = this.getUIMapDefArr();
         for each(md0 in darr0)
         {
            if(this.getMapDiffWinB(md0.name,diffMust0))
            {
               num0++;
            }
         }
         return num0;
      }
      
      private function get_coveDiff(d0:SpaceAchieveDefine) : int
      {
         return this.getMapDiff("GanymedeCave") > d0.extra ? 1 : 0;
      }
      
      private function get_playerSkillNum(d0:SpaceAchieveDefine) : int
      {
         var a0:AstronData = this.getNowAstron();
         if(Boolean(a0))
         {
            return a0.getPlayerSkillArr().length;
         }
         return 0;
      }
      
      private function get_screenKill(d0:SpaceAchieveDefine = null) : Number
      {
         return this.save.c.getAttribute("screenKill");
      }
      
      private function get_screenKillB(d0:SpaceAchieveDefine = null) : Number
      {
         return this.save.c.getAttribute("screenKillB");
      }
      
      private function get_shieldKill(d0:SpaceAchieveDefine = null) : Number
      {
         return this.save.c.getAttribute("shieldKill");
      }
      
      private function getMapNumByWinMust(winMust0:int) : int
      {
         var d0:WorldMapDefine = null;
         var win0:int = 0;
         var num0:int = 0;
         var darr0:Array = this.getUIMapDefArr();
         for each(d0 in darr0)
         {
            win0 = this.getMapWinNum(d0.name);
            if(win0 >= winMust0)
            {
               num0++;
            }
         }
         return num0;
      }
   }
}


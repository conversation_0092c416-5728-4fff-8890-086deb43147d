package dataAll.skill.define
{
   public class SkillFather
   {
      public static const heroSkill:String = "heroSkill";
      
      public static const deviceSkill:String = "deviceSkill";
      
      public static const enemySuper:String = "enemySuper";
      
      public static const noEnemySuper:String = "noEnemySuper";
      
      public static const headSkill:String = "headSkill";
      
      public static const coatSkill:String = "coatSkill";
      
      public static const fashionSkill:String = "fashionSkill";
      
      public static const armsSkill:String = "armsSkill";
      
      public static const godArmsSkill:String = "godArmsSkill";
      
      public static const partsSkill:String = "partsSkill";
      
      public static const loveSkill:String = "loveSkill";
      
      public static const unionSkill:String = "loveSkill";
      
      public static const cnObj:Object = {
         "dropEffect":"掉落",
         "thingsEffect":"道具",
         "purgoldEquip":"装备",
         "headSkill":"装备",
         "vehicle":"载具",
         "vehicleSkill":"载具",
         "vehicleNormal":"载具",
         "coatSkill":"装备",
         "fashion":"时装",
         "fashionSkill":"时装",
         "equipSkill_link":"装备",
         "heroSkill":"角色",
         "heroSkillLink":"角色",
         "deviceSkill":"装置",
         "jewelry":"饰品",
         "shield":"护盾",
         "weaponSkill":"副手",
         "enemy":"敌人",
         "enemySuper":"精英",
         "noEnemySuper":"飞精英",
         "headSkill":"头盔",
         "coatSkill":"战衣",
         "fashionSkill":"时装",
         "armsSkill":"武器",
         "godArmsSkill":"武器",
         "godArmsSkill_link":"武器",
         "partsSkill":"零件",
         "loveSkill":"好感",
         "unionSkill":"军队",
         "peakSkill":"巅峰",
         "pet":"尸宠",
         "petSkill":"尸宠",
         "petBodySkill":"尸宠",
         "demonSkill":"敌人",
         "nightmare":"敌人",
         "otherEnemy":"敌人",
         "snake":"血蟒",
         "craft":"飞船",
         "forest":"其他",
         "outfitSkill":"套件",
         "task":"任务"
      };
      
      public static const charmNoSkillArr:Array = ["skillCopyTransport","MeatySkillBack","skillCopy_enemy","deadlyArrow","deadlyGhost","revengeGhost","revengeArrow","corpsePoison","ruleRange","toLand","offAllSkill"];
      
      public static const noBeCharmIfHaveSkill:Array = ["MeatySkillBack"];
      
      public static const likeMissleNoArr:Array = ["likeMissle_Shapers","likeMissleNo","likeMissleNo2"];
      
      public static const flyArr:Array = ["goldFalcon","dragonHeadSkill"];
      
      public static const enemyToArr:Array = ["enemyToZombie","enemyToSpider"];
      
      public static const noWeaponArr:Array = ["bladeShield","weaponDefence","bladeWrap"];
      
      public static const addDoArr:Array = ["longGlasses_jewelry","superGlasses_jewelry"];
      
      public function SkillFather()
      {
         super();
      }
      
      public static function getCn(name0:String) : String
      {
         if(cnObj.hasOwnProperty(name0))
         {
            return cnObj[name0];
         }
         return name0;
      }
   }
}


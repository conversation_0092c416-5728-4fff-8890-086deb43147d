package dataAll.bullet
{
   import com.sounto.utils.ClassProperty;
   
   public class BulletBindingDefine
   {
      public static var pro_arr:Array = [];
      
      public var cnName:String = "";
      
      public var skillArr:Array = [];
      
      public var lifeMul:Number = 1;
      
      public function BulletBindingDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
   }
}


package dataAll._app.space
{
   import com.sounto.cf.NumberEncodeObj;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.space.craft.CraftSaveGroup;
   
   public class SpaceSave
   {
      public static var pro_arr:Array = null;
      
      public var mapO:Object = {};
      
      public var craft:CraftSaveGroup = new CraftSaveGroup();
      
      public var c:NumberEncodeObj = new NumberEncodeObj();
      
      public var cgArr:Array = [];
      
      public function SpaceSave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.mapO = ClassProperty.copyObj(obj0["mapO"]);
      }
      
      public function getMapObj(name0:String) : Object
      {
         return this.mapO[name0];
      }
      
      public function getMapObjAndAdd(name0:String) : Object
      {
         if(this.mapO.hasOwnProperty(name0) == false)
         {
            this.mapO[name0] = {};
         }
         return this.mapO[name0];
      }
      
      public function getMapPro(name0:String, pro0:String, noValue0:* = null) : *
      {
         var obj0:Object = this.getMapObj(name0);
         if(Boolean(obj0))
         {
            if(obj0.hasOwnProperty(pro0))
            {
               return obj0[pro0];
            }
            return noValue0;
         }
         return noValue0;
      }
      
      public function setMapPro(name0:String, pro0:String, v0:*) : void
      {
         var obj0:Object = this.getMapObjAndAdd(name0);
         if(Boolean(obj0))
         {
            obj0[pro0] = v0;
         }
      }
      
      public function setMapProAll(pro0:String, v0:*) : void
      {
         var obj0:Object = null;
         for each(obj0 in this.mapO)
         {
            obj0[pro0] = v0;
         }
      }
   }
}


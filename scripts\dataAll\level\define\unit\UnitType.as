package dataAll.level.define.unit
{
   import com.sounto.oldUtils.ComMethod;
   
   public class UnitType
   {
      public static const BOSS:String = "boss";
      
      public static const NORMAL:String = "normal";
      
      public static const SUPER:String = "super";
      
      public static const player:String = "player";
      
      public static const pet:String = "pet";
      
      public static const normal_super:String = "normal_super";
      
      public static const normal_superNoDemon:String = "normal_superNoDemon";
      
      public static const super_boss:String = "super_boss";
      
      public static const vehicle:String = "vehicle";
      
      public static const bossCard:String = "bossCard";
      
      public static const charm:String = "charm";
      
      public static const sum:String = "sum";
      
      public static const sumBoss:String = "sumBoss";
      
      public static const craft:String = "craft";
      
      public static const astron:String = "astron";
      
      private static const cnObj:Object = {
         "boss":"首领",
         "normal":"小怪",
         "player":"角色",
         "pet":"尸宠",
         "vehicle":"载具",
         "bossCard":"魂卡",
         "charm":"魅惑单位",
         "sum":"召唤单位",
         "sumBoss":"首领召唤卡",
         "craft":"飞船",
         "astron":"宇航员"
      };
      
      public static const TYPE_ARR:Array = [NORMAL,SUPER,BOSS];
      
      private static const lifeMul:Array = [1,17,45];
      
      private static const dpsMul:Array = [1,1,1.2];
      
      private static const coinMul:Array = [1,3,8];
      
      private static const expMul:Array = [1,4,20];
      
      public function UnitType()
      {
         super();
      }
      
      public static function getMul(name0:String, type0:String) : Number
      {
         return UnitType[name0 + "Mul"][TYPE_ARR.indexOf(type0)];
      }
      
      public static function getLifeMul(type0:String) : Number
      {
         return lifeMul[TYPE_ARR.indexOf(type0)];
      }
      
      public static function getDpsMul(type0:String) : Number
      {
         return dpsMul[TYPE_ARR.indexOf(type0)];
      }
      
      public static function getColorCnName(type0:String) : String
      {
         if(type0 == BOSS)
         {
            return ComMethod.color("首领","#FF6600");
         }
         if(type0 == SUPER)
         {
            return ComMethod.color("精英","#FF66FF");
         }
         return "";
      }
      
      public static function getCn(type0:String) : String
      {
         if(type0 == SUPER)
         {
            return "精英怪";
         }
         return cnObj[type0];
      }
      
      public static function minus(type1:String, type2:String) : int
      {
         var index1:int = int(TYPE_ARR.indexOf(type1));
         var index2:int = int(TYPE_ARR.indexOf(type2));
         return index1 - index2;
      }
      
      public static function gethurtMineMul(unitType0:String) : Number
      {
         if(unitType0 == NORMAL)
         {
            return 1;
         }
         if(unitType0 == SUPER)
         {
            return 0.2;
         }
         if(unitType0 == BOSS)
         {
            return 0.05;
         }
         if(unitType0 == player)
         {
            return 0.4;
         }
         if(unitType0 == pet)
         {
            return 0.3;
         }
         return 0.5;
      }
   }
}


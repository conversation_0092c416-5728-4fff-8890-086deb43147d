package dataAll._app.task.define
{
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class TaskDefineGroup
   {
      public static const mainAddGiftArr:Array = [];
      
      private var arr:Array = [];
      
      private var obj:Object = {};
      
      private var fatherObj:Object = {};
      
      private var fatherArrObj:Object = {};
      
      private var mainLevelObj:Object = {};
      
      private var mainLevelArr:Array = [];
      
      private var fatherDefineObj:Object = {};
      
      public function TaskDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var skillXML0:* = undefined;
         var fatherName0:String = null;
         var father_d0:TaskFatherDefine = null;
         var n:* = undefined;
         var d0:TaskDefine = null;
         var fatherXML0:XMLList = xml0.father;
         for(i in fatherXML0)
         {
            skillXML0 = fatherXML0[i].task;
            fatherName0 = fatherXML0[i].@name;
            father_d0 = this.fatherDefineObj[fatherName0];
            if(!father_d0)
            {
               father_d0 = new TaskFatherDefine();
               father_d0.inData_byXML(fatherXML0[i]);
               this.fatherDefineObj[fatherName0] = father_d0;
            }
            for(n in skillXML0)
            {
               d0 = new TaskDefine();
               d0.inData_byXML(skillXML0[n],fatherName0);
               this.addDefine(d0);
            }
         }
      }
      
      private function addDefine(d0:TaskDefine) : void
      {
         var fatherName0:String = d0.father;
         this.obj[d0.name] = d0;
         if(!this.fatherObj[fatherName0])
         {
            this.fatherObj[fatherName0] = {};
         }
         this.fatherObj[fatherName0][d0.name] = d0;
         if(!this.fatherArrObj[fatherName0])
         {
            this.fatherArrObj[fatherName0] = [];
         }
         this.fatherArrObj[fatherName0].push(d0);
      }
      
      public function getDefine(father0:String, name0:String) : TaskDefine
      {
         if(Boolean(this.fatherObj[father0]))
         {
            return this.fatherObj[father0][name0];
         }
         return null;
      }
      
      public function getOneDefine(name0:String) : TaskDefine
      {
         return this.obj[name0];
      }
      
      public function getArrByType(type0:String) : Array
      {
         var arr0:Array = this.fatherArrObj[type0];
         if(!arr0)
         {
            arr0 = [];
            this.fatherArrObj[type0] = arr0;
         }
         return arr0;
      }
      
      public function getArrByTypeCut(type0:String, cutName0:String) : Array
      {
         var d0:TaskDefine = null;
         var arr0:Array = this.getArrByType(type0);
         var newArr0:Array = [];
         for each(d0 in arr0)
         {
            newArr0.push(d0);
            if(d0.name == cutName0)
            {
               return newArr0;
            }
         }
         return arr0;
      }
      
      public function getFatherDefine(type0:String) : TaskFatherDefine
      {
         return this.fatherDefineObj[type0];
      }
      
      public function getMainByMaxLevel(lv0:int) : TaskDefine
      {
         var d0:TaskDefine = null;
         for(var i:int = lv0; i < lv0 + 10; i++)
         {
            d0 = this.mainLevelObj["lv" + lv0];
            if(d0 is TaskDefine)
            {
               return d0;
            }
         }
         var arr0:Array = this.mainLevelArr;
         return arr0[arr0.length - 1];
      }
      
      public function afterInit() : void
      {
         this.dealGift();
         this.cloneToMemory();
      }
      
      private function cloneToMemory() : void
      {
         var d0:TaskDefine = null;
         var cd0:TaskDefine = null;
         var arr0:Array = this.mainLevelArr;
         for each(d0 in arr0)
         {
            if(d0.lv <= 99)
            {
               cd0 = d0.clone();
               cd0.name = d0.name + "M";
               cd0.father = TaskType.MEMORY;
               cd0.gift = new GiftAddDefineGroup();
               cd0.gift.addGiftByStr("base;anniCoin;16");
               cd0.gift.addGiftByStr("things;madheart;2");
               if(d0.name == "BaWang_king")
               {
                  cd0.enemyLifeMul = 0.6;
               }
               this.addDefine(cd0);
            }
         }
      }
      
      private function dealGift() : void
      {
         var n:* = undefined;
         var d0:TaskDefine = null;
         var obn0:String = null;
         var expMul0:Number = NaN;
         var arr0:Array = this.getArrByType("main");
         var startB0:Boolean = false;
         var endB0:Boolean = false;
         for(n in arr0)
         {
            d0 = arr0[n];
            obn0 = "lv" + d0.lv;
            if(!this.mainLevelObj.hasOwnProperty(obn0))
            {
               this.mainLevelObj[obn0] = d0;
               this.mainLevelArr.push(d0);
            }
            expMul0 = 0.12;
            if(d0.lv > 55)
            {
               expMul0 = 0.05;
            }
            if(d0.gift.arr.length == 0)
            {
               d0.gift.addGiftByStr("base;exp;" + expMul0 + ";;;;HeroExp");
               d0.gift.addGiftByStr("base;coin;0.05;;;;PlayerCoinIncome");
               d0.gift.addGiftByStr("things;lifeBottle;5");
               d0.gift.addGiftByStr("things;caisson;5");
            }
            if(startB0 == false)
            {
               if(d0.name == "WoTu_back")
               {
                  startB0 = true;
               }
            }
            if(endB0 == false)
            {
               if(d0.name == "XingPeak_plot")
               {
                  endB0 = true;
               }
            }
            if(startB0 && endB0 == false)
            {
               mainAddGiftArr.push(d0.name);
            }
         }
      }
   }
}


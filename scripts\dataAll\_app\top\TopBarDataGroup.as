package dataAll._app.top
{
   import dataAll._app.top.define.TopBarDefine;
   import dataAll._app.top.define.TopBarDefineGroup;
   
   public class TopBarDataGroup implements IO_TopDataGroup
   {
      public static var TEST_SCORE:Number = 0;
      
      public var arr:Array = [];
      
      public var define:TopBarDefineGroup;
      
      public function TopBarDataGroup()
      {
         super();
      }
      
      public static function getSimulatedArr(dg0:TopBarDefineGroup, num0:int) : Array
      {
         var da0:TopBarData = null;
         var arr0:Array = [];
         for(var i:int = 0; i < num0; i++)
         {
            da0 = new TopBarData();
            da0.setToSimulated(dg0.objType);
            da0.rank = int(Math.random() * 9 + 1);
            if(TEST_SCORE > 0)
            {
               da0.score = TEST_SCORE;
            }
            else
            {
               da0.score = 14;
            }
            arr0.push(da0);
         }
         return arr0;
      }
      
      public function getTopArr(sortName0:String = "") : Array
      {
         return this.arr;
      }
      
      public function getTopFunName() : String
      {
         return "inData_byTopBarData";
      }
      
      public function getTopTitleCnArr() : Array
      {
         return this.getTitleArr();
      }
      
      public function inData_byArr(arr0:Array, name0:String) : void
      {
         var n:* = undefined;
         var obj0:Object = null;
         var da0:TopBarData = null;
         this.arr.length = 0;
         var dg0:TopBarDefineGroup = Gaming.defineGroup.top.getDefine(name0);
         this.define = dg0;
         for(n in arr0)
         {
            obj0 = arr0[n];
            da0 = new TopBarData();
            da0.inData_byObj(obj0,dg0);
            this.arr.push(da0);
         }
      }
      
      public function getTitleArr() : Array
      {
         var n:* = undefined;
         var d0:TopBarDefine = null;
         var d_arr0:Array = this.define.arr;
         var n_arr0:Array = [];
         for(n in d_arr0)
         {
            d0 = d_arr0[n];
            n_arr0.push(String(d0.cnName));
         }
         return n_arr0;
      }
      
      public function delOneByUid(uid0:String) : void
      {
         var n:* = undefined;
         var da0:TopBarData = null;
         var arr0:Array = [];
         for(n in this.arr)
         {
            da0 = this.arr[n];
            if(da0.uid != uid0)
            {
               arr0.push(da0);
            }
         }
         this.arr = arr0;
      }
   }
}


package dataAll._app.ask
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.ask.define.AskDefine;
   
   public class AskTemp
   {
      public var def:AskDefine;
      
      public var answerArr:Array = [];
      
      public var noArr:Array = [];
      
      public var correct:String = "";
      
      public function AskTemp()
      {
         super();
      }
      
      public function inData_byDefine(def0:AskDefine) : void
      {
         this.def = def0;
         this.answerArr = def0.getAnswerArr();
         this.correct = this.answerArr[0];
         ComMethod.sortRandomArray(this.answerArr);
      }
      
      public function getCanChooseArr() : Array
      {
         return ComMethod.deductArr(this.answerArr,this.noArr);
      }
      
      public function clearError() : String
      {
         var arr2:Array = null;
         var answer0:String = null;
         var arr0:Array = this.getCanChooseArr();
         if(arr0.length > 1)
         {
            arr2 = ComMethod.deductArr(arr0,this.def.correctArr);
            answer0 = arr2[int(Math.random() * arr2.length)];
            this.noArr.push(answer0);
            return answer0;
         }
         return null;
      }
      
      public function canClearErrorB() : Boolean
      {
         return this.answerArr.length - this.noArr.length > 1;
      }
      
      public function getRandomAnswer() : String
      {
         var arr0:Array = this.getCanChooseArr();
         return arr0[int(Math.random() * arr0.length)];
      }
   }
}


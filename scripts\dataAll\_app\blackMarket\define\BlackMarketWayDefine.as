package dataAll._app.blackMarket.define
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   
   public class BlackMarketWayDefine
   {
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      private var _taxMul:String = "";
      
      private var _mustNum:String = "";
      
      public var info:String = "";
      
      public function BlackMarketWayDefine()
      {
         super();
         this.taxMul = 1;
         this.mustNum = 1;
      }
      
      public function get taxMul() : Number
      {
         return Number(TextWay.getText32(this._taxMul));
      }
      
      public function set taxMul(v0:Number) : void
      {
         this._taxMul = TextWay.toCode32(String(v0));
      }
      
      public function get mustNum() : Number
      {
         return Number(TextWay.getText32(this._mustNum));
      }
      
      public function set mustNum(v0:Number) : void
      {
         this._mustNum = TextWay.toCode32(String(v0));
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         ClassProperty.inData_byXML(this,xml0,pro_arr);
      }
      
      public function getTipText() : String
      {
         var str0:String = "";
         str0 += ComMethod.color("<b>" + this.cnName + "</b>","#FFFF00");
         return str0 + ("\n" + this.info);
      }
   }
}


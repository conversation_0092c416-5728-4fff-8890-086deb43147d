package w_test
{
   import UI.bag.ItemsGripTipCtrl;
   import UI.test.LevelTestCtrl;
   import UI.test.SaveTestBox;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll._data.ConstantDefine;
   import dataAll.skill.define.SkillDefine;
   import dataAll.skill.define.otherObj.SkillChangeHurt;
   import flash.display.Graphics;
   import flash.events.KeyboardEvent;
   import flash.system.System;
   import flash.ui.Keyboard;
   import flash.utils.getTimer;
   import gameAll.body.IO_NormalBody;
   import w_test.bug.UplevelBug;
   import w_test.cheating._CheatingController;
   
   public class AllTestCtrl
   {
      public var isApiB:Boolean = false;
      
      public var isComB:Boolean = false;
      
      public var enabled:Boolean = false;
      
      public var gr:Graphics;
      
      public var drop:TestCtrl_Drop = new TestCtrl_Drop();
      
      public var map:TestCtrl_Map = new TestCtrl_Map();
      
      public var hit:TestCtrl_Hit = new TestCtrl_Hit();
      
      public var ai:TestCtrl_AI = new TestCtrl_AI();
      
      public var arms:TestCtrl_Arms = new TestCtrl_Arms();
      
      public var text:TestCtrl_Text = new TestCtrl_Text();
      
      public var tip:TestCtrl_Tip = new TestCtrl_Tip();
      
      public var say:TestCtrl_Say = new TestCtrl_Say();
      
      public var skill:TestCtrl_skill = new TestCtrl_skill();
      
      public var save:TestCtrl_Save = new TestCtrl_Save();
      
      public var level:TestCtrl_Level = new TestCtrl_Level();
      
      public var ui:TestCtrl_UI = new TestCtrl_UI();
      
      public var arr:Array = [];
      
      public var uplevelBug:UplevelBug = new UplevelBug();
      
      public var cheating:_CheatingController = new _CheatingController();
      
      public var levelTest:LevelTestCtrl = new LevelTestCtrl();
      
      public function AllTestCtrl()
      {
         super();
         this.arr.push(this.ai);
         this.cheating.fun_arr = ClassProperty.getFunArr(this.cheating);
      }
      
      public function opneOrClose(name0:String) : void
      {
         var obj0:Object = this[name0];
         var i0:int = int(this.arr.indexOf(obj0));
         if(i0 >= 0)
         {
            this.arr.splice(i0,1);
         }
         else
         {
            this.arr.push(i0);
         }
      }
      
      public function afterUIInit(url0:String) : void
      {
         this.gr = Gaming.gameSprite.L_testShape.graphics;
         this.map.gr = this.gr;

         // 强制启用GM功能，无任何限制
         this.enabled = true;
         this.isComB = false;
         this.isApiB = false;
         this.cheating.enabled = true;

         Gaming.uiGroup.showStat(false);
      }
      
      public function canCheatingB() : Boolean
      {
         return this.enabled == true;
      }
      
      public function mouseClickFun(e:*) : void
      {
         this.mouseClick();
      }
      
      public function mouseClick() : void
      {
         var n:* = undefined;
         var x0:int = 0;
         var y0:int = 0;
         var d0:TestCtrl_Normal = null;
         if(Gaming.LG.isGaming())
         {
            x0 = Gaming.gameSprite.L_game.mouseX;
            y0 = Gaming.gameSprite.L_game.mouseY;
         }
         if(!this.enabled)
         {
            return;
         }
         if(!this.cheating.enabled)
         {
            return;
         }
         for(n in this.arr)
         {
            d0 = this.arr[n];
            d0.mouseClick();
         }
      }
      
      public function keyDown(e:KeyboardEvent) : void
      {
         var n:* = undefined;
         var d2:TestCtrl_Normal = null;
         if(!this.enabled)
         {
            return;
         }
         this.cheating.cheating(e);
         if(!this.cheating.enabled)
         {
            return;
         }
         for(n in this.arr)
         {
            d2 = this.arr[n];
            d2.keyDown(e);
         }
         if(e.keyCode != Keyboard.NUMBER_2)
         {
            if(e.keyCode != Keyboard.NUMBER_3)
            {
               if(e.keyCode != Keyboard.NUMBER_4)
               {
                  if(e.keyCode != Keyboard.T)
                  {
                     if(e.keyCode == Keyboard.K)
                     {
                        SaveTestBox.addText(Gaming.TG.hurt.count.getCumSortTrace());
                     }
                     else if(e.keyCode != Keyboard.O)
                     {
                        if(e.keyCode == Keyboard.L)
                        {
                           Gaming.TG.hurt.ctrl.startAlert();
                        }
                        else if(e.keyCode == Keyboard.M)
                        {
                           Gaming.uiGroup.testUI.showAndHide();
                        }
                        else if(e.keyCode == Keyboard.RIGHTBRACKET)
                        {
                           ItemsGripTipCtrl.saveTipPng();
                        }
                        else if(e.keyCode == Keyboard.BACKSLASH)
                        {
                           if(Gaming.gameSprite.cover.visible)
                           {
                              Gaming.gameSprite.hideAllUI();
                              Gaming.uiGroup.getTopOver().visible = false;
                           }
                           else
                           {
                              Gaming.gameSprite.showAllUI();
                              Gaming.uiGroup.getTopOver().visible = true;
                           }
                        }
                     }
                  }
               }
            }
         }
      }
      
      public function test() : void
      {
         var b0:IO_NormalBody = null;
         var bArr0:Array = Gaming.BG.getAllArr();
         for each(b0 in bArr0)
         {
            b0.getMot().maxJumpNumAdd = 9999999999;
            b0.getMot().F_G = 1;
            b0.getMot().Fi_vymax = 14;
         }
      }
      
      private function showRole() : void
      {
         var b0:IO_NormalBody = null;
         var bArr0:Array = Gaming.BG.WE_ARR;
         for each(b0 in bArr0)
         {
            INIT.tempTrace(b0.getDefine().cnName + "：" + b0.getData().dpsMul + "   " + b0.getData().underHurtMul);
         }
      }
      
      public function testTime() : void
      {
         var s2:StringDate = null;
         var cx0:Number = NaN;
         var s0:StringDate = new StringDate();
         s0.inData_byStr("2014-1-20 18:45:11");
         for(var i:int = 0; i < 24; i++)
         {
            s2 = new StringDate();
            s2.inData_byStr("2014-2-19 " + i + ":55:10");
            cx0 = s0.compareDate(s2);
         }
      }
      
      private function testEffect() : void
      {
         var x0:int = Gaming.gameSprite.L_game.mouseX;
         var y0:int = Gaming.gameSprite.L_game.mouseY;
         Gaming.EG.addMoveBmpEffect("boomMoveSmall",x0,y0,Math.random() * Math.PI * 2,30,"",0.5);
      }
      
      public function testObjGetter() : void
      {
         this.testObjGetter_static();
         var d0:SkillDefine = Gaming.defineGroup.skill.getDefine("Hit_atry_godArmsSkill");
         var tt0:Number = getTimer();
         var mem0:Number = System.totalMemory;
         for(var i:int = 0; i < 1000000; i++)
         {
         }
         trace("testObjGetter耗时：" + (getTimer() - tt0) + "   内存提升：" + (System.totalMemory - mem0));
      }
      
      public function testObjGetter_static() : void
      {
         var d0:SkillDefine = Gaming.defineGroup.skill.getDefine("Hit_atry_godArmsSkill");
         var tt0:Number = getTimer();
         var mem0:Number = System.totalMemory;
         for(var i:int = 0; i < 1000000; i++)
         {
            if("" != SkillChangeHurt.lifePerLess)
            {
               if("" != SkillChangeHurt.player)
               {
                  if("" != SkillChangeHurt.lifePerMore)
                  {
                     if("" != SkillChangeHurt.onlyAttack)
                     {
                        if("" != SkillChangeHurt.onlyWeapon)
                        {
                           if("" != SkillChangeHurt.accurate)
                           {
                              if("" != SkillChangeHurt.hyperopia)
                              {
                                 if("" != SkillChangeHurt.myopia)
                                 {
                                    if("" != SkillChangeHurt.backWeak)
                                    {
                                       if("" != SkillChangeHurt.atry)
                                       {
                                          if("" != SkillChangeHurt.despise)
                                          {
                                             if("" != SkillChangeHurt.killCharm)
                                             {
                                                if("" != SkillChangeHurt.defenceBounceAndImploding)
                                                {
                                                   if("" != SkillChangeHurt.likeMissle)
                                                   {
                                                      if("" != SkillChangeHurt.followBullet)
                                                      {
                                                         if("" != SkillChangeHurt.enemyType)
                                                         {
                                                            if("" != SkillChangeHurt.cmldef)
                                                            {
                                                               if("" != SkillChangeHurt.vehicle)
                                                               {
                                                                  if("" == SkillChangeHurt.stationaryBoss)
                                                                  {
                                                                  }
                                                               }
                                                            }
                                                         }
                                                      }
                                                   }
                                                }
                                             }
                                          }
                                       }
                                    }
                                 }
                              }
                           }
                        }
                     }
                  }
               }
            }
         }
         trace("testObjGetter_static耗时：" + (getTimer() - tt0) + "   内存提升：" + (System.totalMemory - mem0));
      }
      
      private function testObjGetter2() : void
      {
         var d0:SkillDefine = Gaming.defineGroup.skill.getDefine("Hit_atry_godArmsSkill");
         var tt0:Number = getTimer();
         var mem0:Number = System.totalMemory;
         for(var i:int = 0; i < 1000000; i++)
         {
            switch(19)
            {
               case 0:
                  break;
               case 1:
                  break;
               case 2:
                  break;
               case 3:
                  break;
               case 4:
                  break;
               case 5:
                  break;
               case 6:
                  break;
               case 7:
                  break;
               case 8:
                  break;
               case 9:
                  break;
               case 10:
                  break;
               case 11:
                  break;
               case 12:
                  break;
               case 13:
                  break;
               case 14:
                  break;
               case 15:
                  break;
               case 16:
                  break;
               case 17:
                  break;
               case 18:
                  break;
            }
         }
         trace("testObjGetter switch耗时：" + (getTimer() - tt0) + "   内存提升：" + (System.totalMemory - mem0));
      }
      
      public function FTimer() : void
      {
         var n:* = undefined;
         var d0:TestCtrl_Normal = null;
         if(!this.cheating.enabled)
         {
            return;
         }
         this.levelTest.FTimer2();
         if(!this.enabled)
         {
            return;
         }
         for(n in this.arr)
         {
            d0 = this.arr[n];
            d0.FTimer();
         }
      }
   }
}


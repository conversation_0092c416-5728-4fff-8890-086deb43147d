package dataAll.skill.define.otherObj
{
   import com.sounto.utils.ClassProperty;
   
   public class SkillSummoneObj
   {
      public static var pro_arr:Array = [];
      
      public var cnName:String = "";
      
      public var randomCnArr:Array = [];
      
      public var num:int = 1;
      
      public var maxNum:int = 10;
      
      public var maxFromWeAllB:Boolean = false;
      
      public var skillArr:Array = [];
      
      public var lifeMul:Number = 1;
      
      public var dpsMul:Number = 1;
      
      public var mulByFatherB:Boolean = false;
      
      public var lifeTime:Number = 0;
      
      public var existB:Boolean = true;
      
      public var noUnderHurtB:Boolean = false;
      
      public var noUnderHitB:Boolean = false;
      
      public var noAiFindB:Boolean = false;
      
      public var lifeBarB:Boolean = false;
      
      public var unitType:String = "";
      
      public var noAttackArr:Array = [];
      
      public var cx:int = 0;
      
      public var cy:int = 0;
      
      public var flipB:Boolean = true;
      
      public var position:String = "";
      
      public var firstVx:Number = 0;
      
      public var camp:String = "";
      
      public var nextTimeRan:Number = 0;
      
      public var pointEf:Boolean = false;
      
      public var noNumB:Boolean = false;
      
      public function SkillSummoneObj()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function getAllCnNameArr() : Array
      {
         return this.randomCnArr.concat([this.cnName]);
      }
      
      public function getCnName() : String
      {
         var arr0:Array = null;
         if(this.randomCnArr.length > 0)
         {
            arr0 = this.getAllCnNameArr();
            return arr0[int(Math.random() * arr0.length)];
         }
         return this.cnName;
      }
   }
}


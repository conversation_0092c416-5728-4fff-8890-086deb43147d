package com.sounto.hit
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class LineHit
   {
      private static var p0:Point = new Point();
      
      public function LineHit()
      {
         super();
      }
      
      private static function hit_LineRect2(x0:Number, y0:Number, x1:Number, y1:Number, x2:Number, y2:Number, x3:Number, y3:Number) : Point
      {
         var cx0:int = x1 - x0;
         var cy0:int = y1 - y0;
         if(cx0 > 0)
         {
            if(x2 < x0 && x3 > x1)
            {
               return null;
            }
         }
         else if(x2 > x0 && x3 < x1)
         {
            return null;
         }
         if(cy0 > 0)
         {
            if(y2 < y0 && y3 > y1)
            {
               return null;
            }
         }
         else if(y2 > y0 && y3 < y1)
         {
            return null;
         }
         return null;
      }
      
      public static function hit_LineRectClass(x0:Number, y0:Number, x1:Number, y1:Number, rect1:Rectangle) : Point
      {
         return hit_LineRect(x0,y0,x1,y1,rect1.x,rect1.y,rect1.x + rect1.width,rect1.y + rect1.height);
      }
      
      public static function hit_LineRect(x0:Number, y0:Number, x1:Number, y1:Number, x2:Number, y2:Number, x3:Number, y3:Number) : Point
      {
         var hit_edge:int = 0;
         var px:Number = 0;
         var py:Number = 0;
         if(y1 == y0 && x1 == x0)
         {
            return null;
         }
         if(y1 == y0)
         {
            if(y0 >= y2 && y0 <= y3)
            {
               if(x0 < x2 && x1 < x2 || x0 > x3 && x1 > x3)
               {
                  hit_edge = 0;
               }
               else
               {
                  py = y0;
                  if(x0 < x2)
                  {
                     hit_edge = 2;
                     px = x2;
                  }
                  else if(x0 > x3)
                  {
                     hit_edge = 3;
                     px = x3;
                  }
                  else
                  {
                     hit_edge = 5;
                     px = x0;
                  }
               }
            }
            if(hit_edge == 0)
            {
               return null;
            }
            p0.x = px;
            p0.y = py;
            return p0;
         }
         if(x1 == x0)
         {
            if(x0 >= x2 && x0 <= x3)
            {
               if(y0 < y2 && y1 < y2 || y0 > y3 && y1 > y3)
               {
                  hit_edge = 0;
               }
               else
               {
                  px = x0;
                  if(y0 < y2)
                  {
                     hit_edge = 1;
                     py = y2;
                  }
                  else if(y0 > y3)
                  {
                     hit_edge = 4;
                     py = y3;
                  }
                  else
                  {
                     hit_edge = 5;
                     py = y0;
                  }
               }
            }
            if(hit_edge == 0)
            {
               return null;
            }
            p0.x = px;
            p0.y = py;
            return p0;
         }
         var cra0:Number = (x1 - x0) / (y1 - y0);
         var c22:Number = x2 - ((y2 - y0) * cra0 + x0);
         var c23:Number = x2 - ((y3 - y0) * cra0 + x0);
         var c32:Number = x3 - ((y2 - y0) * cra0 + x0);
         var c33:Number = x3 - ((y3 - y0) * cra0 + x0);
         if(y1 < y0)
         {
            c22 *= -1;
            c23 *= -1;
            c32 *= -1;
            c33 *= -1;
         }
         var c_all:Number = c22 * c23 * c32 * c33;
         if(c_all > 0)
         {
            if(c22 > 0 && c23 > 0 && c32 > 0 && c33 > 0 || c22 < 0 && c23 < 0 && c32 < 0 && c33 < 0)
            {
               return null;
            }
         }
         if(x0 < x2)
         {
            if(x1 >= x2)
            {
               if(c22 < 0 && y0 < y2)
               {
                  hit_edge = 1;
               }
               else if(c23 > 0 && y0 > y3)
               {
                  hit_edge = 4;
               }
               else
               {
                  hit_edge = 2;
               }
               if(x1 <= x3)
               {
                  if(y0 < y2 && y1 < y2)
                  {
                     hit_edge = 0;
                  }
                  else if(y0 > y3 && y1 > y3)
                  {
                     hit_edge = 0;
                  }
               }
            }
         }
         else if(x0 > x3)
         {
            if(x1 <= x3)
            {
               if(c32 > 0 && y0 < y2)
               {
                  hit_edge = 1;
               }
               else if(c33 < 0 && y0 > y3)
               {
                  hit_edge = 4;
               }
               else
               {
                  hit_edge = 3;
               }
               if(x1 >= x2)
               {
                  if(y0 < y2 && y1 < y2)
                  {
                     hit_edge = 0;
                  }
                  else if(y0 > y3 && y1 > y3)
                  {
                     hit_edge = 0;
                  }
               }
            }
         }
         else if(!(y0 < y2 && y1 < y2))
         {
            if(!(y0 > y3 && y1 > y3))
            {
               if(y0 < y2)
               {
                  hit_edge = 1;
               }
               else if(y0 > y3)
               {
                  hit_edge = 4;
               }
               else
               {
                  hit_edge = 5;
               }
            }
         }
         if(hit_edge == 0)
         {
            return null;
         }
         if(hit_edge == 1)
         {
            py = y2;
            px = (x1 - x0) / (y1 - y0) * (py - y0) + x0;
         }
         else if(hit_edge == 4)
         {
            py = y3;
            px = (x1 - x0) / (y1 - y0) * (py - y0) + x0;
         }
         else if(hit_edge == 2)
         {
            px = x2;
            py = (y1 - y0) / (x1 - x0) * (px - x0) + y0;
         }
         else if(hit_edge == 3)
         {
            px = x3;
            py = (y1 - y0) / (x1 - x0) * (px - x0) + y0;
         }
         else
         {
            px = x0;
            py = y0;
         }
         p0.x = px;
         p0.y = py;
         return p0;
      }
      
      public static function side_LinePoint(x0:Number, y0:Number, x1:Number, y1:Number, x2:Number, y2:Number) : int
      {
         var cx:Number = 0;
         if(y1 == y0 && x1 == x0)
         {
            cx = 0;
         }
         else if(y1 == y0)
         {
            cx = y2 - y0;
            if(x1 > x0)
            {
               cx *= -1;
            }
         }
         else if(x1 == x0)
         {
            cx = x2 - x0;
            if(y1 < y0)
            {
               cx *= -1;
            }
         }
         else
         {
            cx = x2 - ((y2 - y0) * (x1 - x0) / (y1 - y0) + x0);
            if(y1 < y0)
            {
               cx *= -1;
            }
         }
         if(cx == 0)
         {
            return 0;
         }
         if(cx > 0)
         {
            return 1;
         }
         return -1;
      }
   }
}


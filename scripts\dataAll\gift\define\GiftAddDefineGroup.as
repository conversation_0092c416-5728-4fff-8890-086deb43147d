package dataAll.gift.define
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.must.define.MustDefine;
   import dataAll.must.define.MustSimpleThingsDefine;
   
   public class GiftAddDefineGroup
   {
      private static var pro_arr:Array = ["father","name","cnName","mustLevel","randomB","info"];
      
      private static const weekendDayArr:Array = [0,5,6];
      
      public var father:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var arr:Array = [];
      
      private var _mustLevel:String = "";
      
      public var randomB:Boolean = false;
      
      public var info:String = "";
      
      private var extraArr:Array = null;
      
      public function GiftAddDefineGroup()
      {
         super();
         this.mustLevel = 0;
      }
      
      public static function getDescriptionByArr(darr0:Array, num0:int = 2, colorB0:Boolean = true, proShowB0:Boolean = false) : String
      {
         var d0:GiftAddDefine = null;
         var s0:String = null;
         var arr0:Array = [];
         var allPro0:Number = 0;
         if(proShowB0)
         {
            allPro0 = getAllProByArr(darr0);
         }
         for each(d0 in darr0)
         {
            if(d0.num > 0)
            {
               s0 = d0.getDescription(colorB0,allPro0);
               arr0.push(s0);
            }
         }
         if(arr0.length == 0)
         {
            return "无";
         }
         return TextWay.mixedStringArr(arr0,num0);
      }
      
      private static function getAllProByArr(darr0:Array) : Number
      {
         var d0:GiftAddDefine = null;
         var all0:Number = 0;
         for each(d0 in darr0)
         {
            if(d0.num > 0)
            {
               all0 += d0.pro;
            }
         }
         return all0;
      }
      
      public function set mustLevel(v0:Number) : void
      {
         this._mustLevel = Sounto64.encode(String(v0));
      }
      
      public function get mustLevel() : Number
      {
         return Number(Sounto64.decode(this._mustLevel));
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.arr = ClassProperty.copySaveArray(obj0["arr"],GiftAddDefine);
      }
      
      public function inData_byXML(fatherXML0:XMLList) : void
      {
         var i:* = undefined;
         var str0:String = null;
         if(!fatherXML0)
         {
            return;
         }
         for(i in fatherXML0)
         {
            str0 = String(fatherXML0[i]);
            if(str0 != "")
            {
               this.addGiftByStr(str0);
            }
         }
      }
      
      public function inAllData_byXML(fatherXML0:XMLList, randomB0:Boolean) : void
      {
         var i:* = undefined;
         var str0:String = null;
         var d0:GiftAddDefine = null;
         this.randomB = randomB0;
         if(!fatherXML0)
         {
            return;
         }
         for(i in fatherXML0)
         {
            str0 = String(fatherXML0[i]);
            if(str0 != "")
            {
               d0 = new GiftAddDefine();
               d0.inData_byXML(fatherXML0[i]);
               this.arr.push(d0);
            }
         }
      }
      
      public function inData_byOneXML(xml0:XML, father0:String) : void
      {
         this.father = father0;
         if(!xml0)
         {
            return;
         }
         this.name = xml0.@name;
         this.cnName = xml0.@cnName;
         this.info = String(xml0.info);
         this.mustLevel = int(xml0.@mustLevel);
         this.randomB = Boolean(int(xml0.@randomB));
         var fatherXML0:XMLList = xml0.gift;
         this.inAllData_byXML(fatherXML0,this.randomB);
      }
      
      public function setExtraArr(arr0:Array) : void
      {
         this.extraArr = arr0;
      }
      
      public function getExtraArr() : Array
      {
         return this.extraArr;
      }
      
      public function haveDataB() : Boolean
      {
         return this.arr.length > 0;
      }
      
      public function clearAll() : void
      {
         this.arr.length = 0;
      }
      
      public function addThings(name0:String, num0:int) : void
      {
         if(num0 > 0)
         {
            this.addGiftByStr("things;" + name0 + ";" + num0);
         }
      }
      
      public function addGiftByStr(str0:String) : GiftAddDefine
      {
         var d0:GiftAddDefine = new GiftAddDefine();
         d0.inData_byStr(str0);
         this.arr.push(d0);
         return d0;
      }
      
      public function mergeGiftByStr(str0:String) : GiftAddDefine
      {
         var d0:GiftAddDefine = new GiftAddDefine();
         d0.inData_byStr(str0);
         this.mergeOne(d0);
         return d0;
      }
      
      public function addGiftByCnStr(str0:String) : String
      {
         var d0:GiftAddDefine = null;
         var error0:String = "";
         if(str0 != "" && str0 is String)
         {
            d0 = new GiftAddDefine();
            error0 = d0.inData_byCnStr(str0);
            if(error0 == "")
            {
               this.arr.push(d0);
            }
         }
         return error0;
      }
      
      public function addGift(d0:GiftAddDefine) : GiftAddDefine
      {
         if(Boolean(d0))
         {
            this.arr.push(d0);
         }
         return d0;
      }
      
      public function removeGiftIndex(n:int) : GiftAddDefine
      {
         var d0:GiftAddDefine = this.arr[n];
         if(d0 is GiftAddDefine)
         {
            this.arr.splice(n,1);
            return d0;
         }
         return null;
      }
      
      public function removeGift(d0:GiftAddDefine) : void
      {
         var index0:int = int(this.arr.indexOf(d0));
         if(index0 != -1)
         {
            this.removeGiftIndex(index0);
         }
      }
      
      public function getNumByType(type0:String) : int
      {
         var d0:GiftAddDefine = null;
         var num0:int = 0;
         for each(d0 in this.arr)
         {
            if(d0.type == type0)
            {
               num0 += d0.num;
            }
         }
         return num0;
      }
      
      public function getAllNum() : int
      {
         var d0:GiftAddDefine = null;
         var num0:int = 0;
         for each(d0 in this.arr)
         {
            num0 += d0.num;
         }
         return num0;
      }
      
      public function getGiftByName(name0:String) : GiftAddDefine
      {
         var d0:GiftAddDefine = null;
         for each(d0 in this.arr)
         {
            if(d0.name == name0)
            {
               return d0;
            }
         }
         return null;
      }
      
      public function getTipB() : Boolean
      {
         var d0:GiftAddDefine = null;
         for each(d0 in this.arr)
         {
            if(d0.tipB)
            {
               return true;
            }
         }
         return false;
      }
      
      public function addNumMul(mul0:Number, ceilB0:Boolean = true, roundB0:Boolean = false) : void
      {
         var n:* = undefined;
         var d0:GiftAddDefine = null;
         for(n in this.arr)
         {
            d0 = this.arr[n];
            if(ceilB0)
            {
               d0.num = Math.ceil(d0.num * mul0);
            }
            else if(roundB0)
            {
               d0.num = Math.round(d0.num * mul0);
            }
            else
            {
               d0.num = int(d0.num * mul0);
            }
         }
      }
      
      public function addNumMulClear(mul0:Number) : void
      {
         var n:* = undefined;
         var d0:GiftAddDefine = null;
         var newArr0:Array = [];
         for(n in this.arr)
         {
            d0 = this.arr[n];
            d0.num = Math.round(d0.num * mul0);
            if(d0.num >= 1)
            {
               newArr0.push(d0);
            }
         }
         this.arr = newArr0;
      }
      
      public function getRandomIndex() : int
      {
         var n:* = undefined;
         var d0:GiftAddDefine = null;
         var pro0:Number = NaN;
         var equipD0:EquipDefine = null;
         var proArr0:Array = [];
         for(n in this.arr)
         {
            d0 = this.arr[n];
            pro0 = d0.pro;
            if(this.randomB)
            {
               if(d0.type == "arms")
               {
                  if(Boolean(Gaming.PG.da.findArenaGiftItemsData(d0.name)))
                  {
                     pro0 = 0;
                  }
               }
               else if(d0.type == "equip")
               {
                  equipD0 = Gaming.defineGroup.equip.getDefine(d0.name);
                  if(Boolean(equipD0))
                  {
                     if(equipD0.type == EquipType.FASHION)
                     {
                        if(Boolean(Gaming.PG.da.findArenaGiftItemsData(d0.name)))
                        {
                           pro0 = 0;
                        }
                     }
                  }
               }
            }
            proArr0.push(pro0);
         }
         return ComMethod.getPro_byArrSum(proArr0);
      }
      
      public function getProByIndex(index0:int) : Number
      {
         var sum0:Number = NaN;
         var d0:GiftAddDefine = null;
         var first0:GiftAddDefine = null;
         if(this.arr.length > 0 && index0 >= 0 && index0 <= this.arr.length - 1)
         {
            sum0 = 0;
            for each(d0 in this.arr)
            {
               sum0 += d0.pro;
            }
            first0 = this.arr[index0];
            return first0.pro / sum0;
         }
         return 0;
      }
      
      private function getProSum() : Number
      {
         var d0:GiftAddDefine = null;
         var sum0:Number = 0;
         for each(d0 in this.arr)
         {
            sum0 += d0.pro;
         }
         return sum0;
      }
      
      public function setProByIndex(index0:int, pro0:Number) : Number
      {
         var sum0:Number = NaN;
         var first0:GiftAddDefine = null;
         if(this.arr.length > 0 && index0 >= 0 && index0 <= this.arr.length - 1)
         {
            sum0 = this.getProSum();
            first0 = this.arr[index0];
            first0.pro = pro0 * (sum0 - first0.pro) / (1 - pro0);
            return first0.pro;
         }
         return 0;
      }
      
      public function getRandomDefine(dropEnsureFun0:Function = null) : GiftAddDefine
      {
         var i0:int = this.arr.length - 1;
         if(dropEnsureFun0 is Function)
         {
            i0 = dropEnsureFun0(this);
         }
         else
         {
            i0 = this.getRandomIndex();
         }
         return this.arr[i0];
      }
      
      public function haveTwoBagType() : Boolean
      {
         var n:* = undefined;
         var d0:GiftAddDefine = null;
         var all_arr0:Array = ["parts","things","arms","equip","more","gene"];
         var arr0:Array = [];
         for(n in this.arr)
         {
            d0 = this.arr[n];
            if(all_arr0.indexOf(d0.type) >= 0)
            {
               if(arr0.indexOf(d0.type) == -1)
               {
                  arr0.push(d0.type);
               }
            }
         }
         return arr0.length > 1;
      }
      
      public function getBagSpaceMust(allNum0:int = 1) : BagSpaceMust
      {
         var d0:GiftAddDefine = null;
         var type0:* = undefined;
         var num0:int = 0;
         var must0:BagSpaceMust = new BagSpaceMust();
         var obj0:Object = {};
         var index0:int = 0;
         for each(d0 in this.arr)
         {
            index0++;
            if(!obj0[d0.type])
            {
               obj0[d0.type] = {};
            }
            num0 = 0;
            if(d0.canOverlayB())
            {
               num0 = 1;
               obj0[d0.type][d0.name] = num0;
            }
            else
            {
               num0 = d0.num * allNum0;
               obj0[d0.type][d0.name + index0] = num0;
            }
         }
         for(type0 in obj0)
         {
            if(must0.hasOwnProperty(type0))
            {
               must0[type0] = this.countMustByOne(obj0[type0],this.randomB,allNum0);
            }
         }
         return must0;
      }
      
      private function countMustByOne(obj0:Object, randomB0:Boolean, allNum0:int) : int
      {
         var objNum0:int = 0;
         var max0:int = 0;
         var num3:int = 0;
         var num0:int = 0;
         if(randomB0)
         {
            objNum0 = int(ComMethod.objToArr(obj0).length);
            max0 = this.getMaxInObj(obj0);
            num0 = allNum0 < objNum0 ? allNum0 : objNum0;
            if(num0 < max0)
            {
               num0 = max0;
            }
         }
         else
         {
            for each(num3 in obj0)
            {
               num0 += num3;
            }
         }
         return num0;
      }
      
      private function getMaxInObj(obj0:Object) : int
      {
         var num3:int = 0;
         var max0:int = 0;
         for each(num3 in obj0)
         {
            if(num3 > max0)
            {
               max0 = num3;
            }
         }
         return max0;
      }
      
      public function clone() : GiftAddDefineGroup
      {
         var d0:GiftAddDefine = null;
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var arr_len0:int = int(this.arr.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            d0 = this.arr[i];
            g0.arr.push(d0.clone());
         }
         return g0;
      }
      
      public function getConverToTrue(lv0:int) : GiftAddDefineGroup
      {
         var d0:GiftAddDefine = null;
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var arr_len0:int = int(this.arr.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            d0 = this.arr[i].clone();
            d0.converToTrue(lv0);
            g0.arr.push(d0);
         }
         return g0;
      }
      
      public function getDescription(num0:int = 2, colorB0:Boolean = true, proShowB0:Boolean = false) : String
      {
         if(!this.randomB)
         {
            proShowB0 = false;
         }
         return getDescriptionByArr(this.arr,num0,colorB0,proShowB0);
      }
      
      public function getDescriptionAndPro(num0:int = 2, colorB0:Boolean = true) : String
      {
         return getDescriptionByArr(this.arr,num0,colorB0,true);
      }
      
      public function converByShowLv(nowLv0:int, readTimeData0:StringDate) : void
      {
         var d0:GiftAddDefine = null;
         var addB0:Boolean = false;
         var date0:Date = null;
         var arr0:Array = [];
         for each(d0 in this.arr)
         {
            addB0 = true;
            if(d0.showLv > nowLv0)
            {
               addB0 = false;
            }
            if(d0.tipB)
            {
               date0 = readTimeData0.getDateClass();
               if(weekendDayArr.indexOf(date0.day) == -1)
               {
                  addB0 = false;
               }
            }
            if(addB0)
            {
               arr0.push(d0);
            }
         }
         this.arr = arr0;
      }
      
      public function getXMLString() : String
      {
         var d0:GiftAddDefine = null;
         var str0:String = "";
         for each(d0 in this.arr)
         {
            str0 += d0.getXMLString() + "\n";
         }
         return str0;
      }
      
      public function mergeMe() : void
      {
         var arr0:Array = this.arr;
         this.arr = [];
         this.mergeArr(arr0);
      }
      
      public function merge(gg0:GiftAddDefineGroup) : void
      {
         this.mergeArr(gg0.arr);
      }
      
      private function mergeArr(arr0:Array) : void
      {
         var d0:GiftAddDefine = null;
         for each(d0 in arr0)
         {
            this.mergeOne(d0);
         }
      }
      
      public function mergeOne(d0:GiftAddDefine) : void
      {
         var d2:GiftAddDefine = null;
         if(!d0)
         {
            return;
         }
         for each(d2 in this.arr)
         {
            if(d2.samePan(d0))
            {
               d2.num += d0.num;
               return;
            }
         }
         this.addGift(d0.clone());
      }
      
      public function deduct(gg0:GiftAddDefineGroup) : void
      {
         var d0:GiftAddDefine = null;
         for each(d0 in gg0.arr)
         {
            this.deductOne(d0);
         }
      }
      
      public function deductOne(d0:GiftAddDefine) : void
      {
         var d2:GiftAddDefine = null;
         var delB0:Boolean = false;
         var newArr0:Array = [];
         for each(d2 in this.arr)
         {
            delB0 = false;
            if(d2.samePan(d0))
            {
               d2.num -= d0.num;
               if(d2.num <= 0)
               {
                  delB0 = true;
               }
            }
            if(!delB0)
            {
               newArr0.push(d2);
            }
         }
         this.arr = newArr0;
      }
      
      public function getMustDefine() : MustDefine
      {
         var gd0:GiftAddDefine = null;
         var str0:String = null;
         var d0:MustDefine = new MustDefine();
         var strArr0:Array = [];
         for each(gd0 in this.arr)
         {
            str0 = "";
            if(gd0.type == "things")
            {
               str0 = gd0.name + ";" + gd0.num;
               strArr0.push(str0);
            }
            else if(gd0.type == "base")
            {
               if(gd0.name == "coin")
               {
                  d0.coin = gd0.num;
               }
            }
         }
         d0.inThingsDataByArr(strArr0);
         return d0;
      }
      
      public function inMustDefineOnlyThings(d0:MustDefine) : void
      {
         var td0:MustSimpleThingsDefine = null;
         var thingArr0:Array = d0.getThingsArr();
         var first0:String = d0.thingsType;
         for each(td0 in thingArr0)
         {
            this.addGiftByStr(first0 + ";" + td0.name + ";" + td0.num);
         }
      }
   }
}


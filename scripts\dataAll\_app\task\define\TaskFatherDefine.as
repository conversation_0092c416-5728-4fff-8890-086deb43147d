package dataAll._app.task.define
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.utils.ClassProperty;
   
   public class TaskFatherDefine
   {
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      private var _dayNum:String = "";
      
      private var _buyNum:String = "";
      
      private var _weekNum:String = "";
      
      public var maxLvLimitB:Boolean = false;
      
      public var clearCompleteAfterBuyNumB:Boolean = false;
      
      public var buyMustDefineName:String = "buyTaskNum";
      
      public var tipText:String = "";
      
      public var autoUnlockByLevelB:Boolean = false;
      
      public function TaskFatherDefine()
      {
         super();
         this.dayNum = -1;
         this.weekNum = -1;
         this.buyNum = -1;
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function getTipText() : String
      {
         var str0:String = null;
         if(this.tipText == "")
         {
            return this.tipText;
         }
         str0 = this.tipText;
         if(this.name != "treasure")
         {
            if(this.name == "king")
            {
               str0 += "\n" + ComMethod.color("注意：" + this.cnName + "任务中的首领，拥有超难难度的掉落概率，但只有简单难度的战斗力与生命值。","#00FFFF");
            }
            else if(this.name == "extra")
            {
               str0 += "\n" + ComMethod.color("注意：在" + this.cnName + "任务中，消灭51级以下的首领，将必掉落橙色武器或装备，消灭51级以上的首领，将很有大概率掉落红色武器或装备。","#00FFFF");
            }
         }
         return str0;
      }
      
      public function set dayNum(v0:Number) : void
      {
         this._dayNum = Sounto64.encode(String(v0));
      }
      
      public function get dayNum() : Number
      {
         return Number(Sounto64.decode(this._dayNum));
      }
      
      public function set buyNum(v0:Number) : void
      {
         this._buyNum = Sounto64.encode(String(v0));
      }
      
      public function get buyNum() : Number
      {
         return Number(Sounto64.decode(this._buyNum));
      }
      
      public function set weekNum(v0:Number) : void
      {
         this._weekNum = Sounto64.encode(String(v0));
      }
      
      public function get weekNum() : Number
      {
         return Number(Sounto64.decode(this._weekNum));
      }
   }
}


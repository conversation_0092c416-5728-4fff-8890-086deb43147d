package dataAll._app.edit.arms
{
   import com.sounto.utils.ClassProperty;
   import dataAll._app.edit.TorSave;
   import dataAll._base.OneSaveGroup;
   
   public class ArmsTorSaveGroup extends OneSaveGroup
   {
      public static var pro_arr:Array = null;
      
      public static var mePro_arr:Array = null;
      
      public var mId:String = "";
      
      public function ArmsTorSaveGroup()
      {
         super();
         saveClass = TorSave;
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObj(obj0);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
      }
   }
}


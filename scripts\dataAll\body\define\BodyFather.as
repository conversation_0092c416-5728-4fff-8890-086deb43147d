package dataAll.body.define
{
   public class BodyFather
   {
      public static const enemy:String = "enemy";
      
      public static const we:String = "we";
      
      public static const wilder:String = "wilder";
      
      public static const other:String = "other";
      
      public static const pet:String = "pet";
      
      public static const vehicle:String = "vehicle";
      
      public static const friable:String = "friable";
      
      public static const craft:String = "craft";
      
      public static const space:String = "space";
      
      public static const snake:String = "snake";
      
      public static const cnObj:Object = {
         "enemy":"敌人",
         "we":"我方",
         "wilder":"秘境",
         "other":"其他",
         "pet":"尸宠",
         "vehicle":"载具",
         "friable":"破坏物",
         "craft":"飞船",
         "space":"太空",
         "snake":"血蟒"
      };
      
      public static const eleArr:Array = [enemy,we,wilder];
      
      public static const eleColorArr:Array = ["#FF9900","#66FFFF","#33CC00"];
      
      public static const editArr:Array = [enemy,wilder,we,vehicle];
      
      public static const sumArr:Array = [enemy,wilder];
      
      public static const noSumNameArr:Array = ["Madbos<PERSON>"];
      
      public static const noSumCnStr:String = "战神、尸宠、载具、我方单位";
      
      public static const ranStandArr:Array = ["OreBomb","OreBall","WorldSnakeTail"];
      
      public static const headSameHurtArr:Array = [friable,craft,space];
      
      public static const bosseditLocalArr:Array = [snake,pet,other,space,craft,friable];
      
      public function BodyFather()
      {
         super();
      }
      
      public static function getEleColor(father0:String) : String
      {
         var f0:int = int(eleArr.indexOf(father0));
         if(f0 >= 0)
         {
            return eleColorArr[f0];
         }
         return "";
      }
      
      public static function getCn(name0:String) : String
      {
         return cnObj[name0];
      }
      
      public static function getCnArrByArr(nameArr0:Array) : Array
      {
         var name0:* = null;
         var cnArr0:Array = [];
         for each(name0 in nameArr0)
         {
            cnArr0.push(getCn(name0));
         }
         return cnArr0;
      }
   }
}


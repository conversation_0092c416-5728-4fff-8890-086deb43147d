package dataAll.image
{
   public class BoomShakeDefine
   {
      public var num:Number = 2;
      
      public var time:Number = 0.3;
      
      public var range:Number = 20;
      
      public var ra:Number = -1000;
      
      public var type:String = "";
      
      public function BoomShakeDefine()
      {
         super();
      }
      
      public function inData(str0:String) : void
      {
         var arr0:Array = str0.split(",");
         this.num = Number(arr0[0]);
         this.time = Number(arr0[1]);
         this.range = Number(arr0[2]);
         if(Boolean(arr0[3]))
         {
            this.ra = Number(arr0[3]) / 180 * Math.PI;
         }
         if(<PERSON><PERSON><PERSON>(arr0[4]))
         {
            this.type = String(arr0[4]);
         }
      }
   }
}


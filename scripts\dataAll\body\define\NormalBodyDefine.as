package dataAll.body.define
{
   import com.common.text.TextWay;
   import com.sounto.math.Maths;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._base.IO_Define;
   import dataAll._player.role.RoleName;
   import dataAll.body.attack.BodyAttackDefine;
   import dataAll.body.attack.ElementShell;
   import dataAll.body.extra.BodyExtraDefineGroup;
   import dataAll.body.img.BodyImageLabel;
   import dataAll.body.movie.MovieDefine;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.vehicle.VehicleBulletDefine;
   import dataAll.image.ImageUrlDefine;
   import dataAll.level.define.unit.UnitType;
   import dataAll.skill.define.SkillDefine;
   import dataAll.skill.define.SkillDescrip;
   import flash.geom.Rectangle;
   import gameAll.body.motion.GroundMotionState;
   
   public class NormalBodyDefine implements IO_Define
   {
      public static const TYPE_NORMAL:String = "normal";
      
      public static const TYPE_HERO:String = "hero";
      
      public static const RACE_HUMAN:String = "human";
      
      public static const RACE_ZOMBIES:String = "zombies";
      
      public static const RACE_ROBOT:String = "robot";
      
      public static const RACE_SNOW:String = "snow";
      
      public static const RACE_SHIP:String = "ship";
      
      public static const RACE_STONE:String = "stone";
      
      private static const raceCnObj:Object = {
         "human":"人类",
         "zombies":"僵尸",
         "robot":"机械体",
         "snow":"雪人",
         "ship":"飞船",
         "stone":"石头"
      };
      
      public static const DEFAULT_WARNING_RANGE:int = 1400;
      
      public static const DEFAULT_OUT_WARNING_TIME:Number = 8;
      
      public static var pro_arr:Array = [];
      
      protected var xmlBat:XML = null;
      
      public var index:int = 0;
      
      public var father:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var noCnIdB:Boolean = false;
      
      public var systemType:String = "normal";
      
      public var raceType:String = "human";
      
      public var sex:String = BodySex.MALE;
      
      public var swfUrl:String = "";
      
      public var swfName:String = "";
      
      public var description:String = "";
      
      public var shell:String = "variation";
      
      public var lifeRatio:Number = 1;
      
      public var rosRatio:Number = 0.2;
      
      public var defenceRatio:Number = 0;
      
      public var bulletDedutRatio:Number = 0;
      
      public var headHurtMul:Number = 2;
      
      public var superDpsAdd:Number = 0;
      
      public var showLevel:int = 0;
      
      public var imgType:String = "bmp";
      
      public var imgClass:String = "";
      
      public var lockLeftB:Boolean = false;
      
      public var doubleLifeBarB:Boolean = false;
      
      public var flipCtrlBy:String = "";
      
      public var imgArr:Array = [];
      
      public var bossImgArr:Array = [];
      
      public var normalScaleX:Number = 1;
      
      public var headIconUrl:String = "";
      
      public var bmpUrl:String = "";
      
      public var lifeBarExtraHeight:int = 0;
      
      public var lowerImgArr:Array = null;
      
      public var handAddRa:Number = 0;
      
      public var dieEffectPartName:String = "head";
      
      public var rotateBySlopeB:Boolean = false;
      
      public var dieImg:ImageUrlDefine = new ImageUrlDefine();
      
      public var dieJumpMul:Number = 1;
      
      public var hitRect:Rectangle = new Rectangle();
      
      public var hurtRectArr:Array = [];
      
      public var motionClass:String = "";
      
      public var maxJumpNum:int = 1;
      
      public var maxVx:Number = 5;
      
      public var maxVy:Number = 27;
      
      public var runStartVx:Number = 8;
      
      public var motionState:String = "stand";
      
      public var flyType:String = "normal";
      
      public var flyUseSpiderB:Boolean = false;
      
      public var motionD:BodyMotionDefine = new BodyMotionDefine();
      
      public var defaultAiOrder:String = "";
      
      public var shootLenMul:Number = 1;
      
      public var attackAIClass:String = "";
      
      public var keyClass:String = "";
      
      public var bulletLauncherClass:String = "";
      
      public var attackMustCanShootB:Boolean = false;
      
      public var nextAttackTime:Number = 0;
      
      public var warningRange:Number = 1400;
      
      public var outWarningTime:Number = 8;
      
      public var extraAIClassLabel:String = "";
      
      public var oneAiLabel:String = "";
      
      public var extraG:BodyExtraDefineGroup = null;
      
      public var skillArr:Array = [];
      
      public var bossSkillArr:Array = [];
      
      private var editSkillArr:Array = null;
      
      public var demSkillArr:Array = [];
      
      public var demBossSkillArr:Array = [];
      
      public var wilderSkillArr:Array = [];
      
      public var uiSkillArr:Array = [];
      
      public var canBossB:Boolean = true;
      
      public var extraDropArmsB:Boolean = false;
      
      public var dropD:BodyDropDefine = new BodyDropDefine();
      
      public var avtiveSkillCdOverT:Number = 2;
      
      public var vBulletArr:Array = null;
      
      public var hurtObj:Object = new Object();
      
      public var hurtArr:Array = [];
      
      public var movieDefineArr:Array = [];
      
      public var otherUnitCnNameArr:Array = [];
      
      public var preBulletArr:Array = [];
      
      public var moreD:BodyMoreDefine = new BodyMoreDefine();
      
      public var map:String = "";
      
      private var editD:BodyEditDefine = null;
      
      public function NormalBodyDefine()
      {
         super();
      }
      
      public static function getRaceCn(race0:String) : String
      {
         return raceCnObj[race0];
      }
      
      public function inData_byXML(xml0:XML, systemType0:String, father0:String) : void
      {
         var hurtXML:XMLList = null;
         var i:* = undefined;
         var hurtDefine0:BodyAttackDefine = null;
         var dg:int = 0;
         this.xmlBat = xml0;
         this.father = father0;
         this.systemType = systemType0;
         this.name = String(xml0.name);
         this.cnName = String(xml0.cnName);
         if(String(xml0.@shell) != "")
         {
            this.shell = String(xml0.@shell);
         }
         if(String(xml0.@map) != "")
         {
            this.map = String(xml0.@map);
         }
         this.noCnIdB = Boolean(int(String(xml0.noCnIdB)));
         this.lockLeftB = Boolean(int(String(xml0.lockLeftB)));
         this.doubleLifeBarB = Boolean(int(String(xml0.doubleLifeBarB)));
         if(String(xml0.sex) != "")
         {
            this.sex = String(xml0.sex);
         }
         this.raceType = String(xml0.raceType);
         this.swfUrl = String(xml0.swfUrl);
         this.swfName = String(xml0.swfName);
         this.description = String(xml0.description);
         if(xml0.lifeRatio.length() > 0)
         {
            this.lifeRatio = Number(xml0.lifeRatio);
         }
         if(xml0.rosRatio.length() > 0)
         {
            this.rosRatio = Number(xml0.rosRatio);
         }
         else if(BodyFather.headSameHurtArr.indexOf(this.father) >= 0)
         {
            this.rosRatio = 1;
         }
         if(xml0.headHurtMul.length() > 0)
         {
            this.headHurtMul = Number(xml0.headHurtMul);
         }
         else if(BodyFather.headSameHurtArr.indexOf(this.father) >= 0)
         {
            this.headHurtMul = 1;
         }
         if(xml0.defenceRatio.length() > 0)
         {
            this.defenceRatio = Number(xml0.defenceRatio);
         }
         if(xml0.bulletDedutRatio.length() > 0)
         {
            this.bulletDedutRatio = Number(xml0.bulletDedutRatio);
         }
         this.superDpsAdd = Number(xml0.superDpsAdd);
         this.showLevel = int(xml0.showLevel);
         if(String(xml0.imgType) != "")
         {
            this.imgType = xml0.imgType;
         }
         this.imgClass = xml0.imgClass;
         this.imgArr = String(TextWay.toHan2(xml0.imgArr)).split(",");
         this.bossImgArr = String(TextWay.toHan2(xml0.bossImgArr)).split(",");
         this.flipCtrlBy = String(xml0.flipCtrlBy);
         this.normalScaleX = Number(xml0.normalScaleX);
         if(this.normalScaleX == 0)
         {
            this.normalScaleX = 1;
         }
         this.headIconUrl = String(xml0.headIconUrl);
         this.fleshIconUrl();
         if(this.bmpUrl == "")
         {
            this.bmpUrl = "BodyImg/" + this.name;
         }
         this.lifeBarExtraHeight = int(xml0.lifeBarExtraHeight);
         this.handAddRa = Number(xml0.handAddRa) / 180 * Math.PI;
         if(xml0.lowerImgArr.length() == 0)
         {
            this.lowerImgArr = EquipDefine.lowerImgArr;
         }
         else
         {
            this.lowerImgArr = String(TextWay.toHan2(xml0.lowerImgArr)).split(",");
         }
         this.dieEffectPartName = xml0.dieEffectPartName;
         if(this.dieEffectPartName == "")
         {
            this.dieEffectPartName = "head";
         }
         this.rotateBySlopeB = Boolean(int(xml0.rotateBySlopeB));
         this.dieImg.inData_byXML(xml0.dieImg[0]);
         if(String(xml0.dieJumpMul) != "")
         {
            this.dieJumpMul = Number(xml0.dieJumpMul);
         }
         this.hitRect = ComMethod.getRect(xml0.hitRect);
         this.hurtRectArr = ComMethod.xmlToIDRectArr(xml0.hurtRectArr);
         this.motionClass = xml0.motionClass;
         if(String(xml0.maxJumpNum) != "")
         {
            this.maxJumpNum = int(xml0.maxJumpNum);
         }
         if(String(xml0.maxVx) != "")
         {
            this.maxVx = Number(xml0.maxVx);
         }
         if(String(xml0.maxVy) != "")
         {
            this.maxVy = Number(xml0.maxVy);
         }
         if(String(xml0.runStartVx) != "")
         {
            this.runStartVx = Number(xml0.runStartVx);
         }
         if(String(xml0.motionState) != "")
         {
            this.motionState = String(xml0.motionState);
         }
         if(String(xml0.flyType) != "")
         {
            this.flyType = String(xml0.flyType);
         }
         this.flyUseSpiderB = Boolean(int(xml0.flyUseSpiderB));
         this.motionD.inData_byXML(xml0.motionD[0]);
         this.shootLenMul = Number(xml0.shootLenMul);
         if(this.shootLenMul == 0)
         {
            this.shootLenMul = 1;
         }
         this.defaultAiOrder = String(xml0.defaultAiOrder);
         this.attackAIClass = String(xml0.attackAIClass);
         this.keyClass = String(xml0.keyClass);
         this.bulletLauncherClass = String(xml0.bulletLauncherClass);
         this.attackMustCanShootB = Boolean(int(String(this.attackMustCanShootB)));
         this.nextAttackTime = Number(xml0.nextAttackTime);
         if(xml0.warningRange.length() > 0)
         {
            this.warningRange = Number(xml0.warningRange);
         }
         if(xml0.outWarningTime.length() > 0)
         {
            this.outWarningTime = Number(xml0.outWarningTime);
         }
         this.extraAIClassLabel = String(xml0.extraAIClassLabel);
         this.oneAiLabel = String(xml0.oneAiLabel);
         if(xml0.extraG.length() > 0)
         {
            this.extraG = new BodyExtraDefineGroup();
            this.extraG.inData_byXML(xml0.extraG[0],this);
         }
         if(String(xml0.skillArr) != "")
         {
            this.skillArr = String(xml0.skillArr).split(",");
         }
         if(String(xml0.preBulletArr) != "")
         {
            this.preBulletArr = String(xml0.preBulletArr).split(",");
         }
         if(String(xml0.bossSkillArr) != "")
         {
            this.bossSkillArr = String(xml0.bossSkillArr).split(",");
         }
         if(this.bossSkillArr[0] == "")
         {
            this.bossSkillArr = [];
         }
         if(String(xml0.demSkillArr) != "")
         {
            this.demSkillArr = String(xml0.demSkillArr).split(",");
         }
         if(String(xml0.demBossSkillArr) != "")
         {
            this.demBossSkillArr = String(xml0.demBossSkillArr).split(",");
         }
         if(String(xml0.uiSkillArr) != "")
         {
            this.uiSkillArr = String(xml0.uiSkillArr).split(",");
         }
         if(this.uiSkillArr[0] == "")
         {
            this.uiSkillArr = [];
         }
         if(String(xml0.wilderSkillArr) != "")
         {
            this.wilderSkillArr = String(xml0.wilderSkillArr).split(",");
         }
         if(this.wilderSkillArr[0] == "")
         {
            this.wilderSkillArr = [];
         }
         if(String(xml0.canBossB) != "")
         {
            this.canBossB = Boolean(int(xml0.canBossB));
         }
         if(String(xml0.extraDropArmsB) != "")
         {
            this.extraDropArmsB = Boolean(int(xml0.extraDropArmsB));
         }
         this.dropD.inData_byXML(xml0.dropD[0]);
         if(String(xml0.otherUnitCnNameArr) != "")
         {
            this.otherUnitCnNameArr = String(xml0.otherUnitCnNameArr).split(",");
         }
         if(String(xml0.avtiveSkillCdOverT) != "")
         {
            this.avtiveSkillCdOverT = Number(xml0.avtiveSkillCdOverT);
         }
         this.moreD.inData_byXML(xml0.moreD[0]);
         if(Boolean(xml0.editD[0]))
         {
            this.getEditDNew().inData_byXML(xml0.editD[0]);
         }
         if(xml0.vBullet.length() > 0)
         {
            this.vBulletArr = VehicleBulletDefine.getDefArrByXml(xml0.vBullet);
         }
         if(xml0.hurtArr.length() > 0)
         {
            hurtXML = xml0.hurtArr.hurt;
            for(i in hurtXML)
            {
               hurtDefine0 = new BodyAttackDefine();
               hurtDefine0.inData_byXML(hurtXML[i]);
               this.hurtObj[hurtDefine0.imgLabel] = hurtDefine0;
               this.hurtArr.push(hurtDefine0);
            }
         }
         if(this.motionState == GroundMotionState.fly)
         {
            if(!this.flyUseSpiderB)
            {
               dg = 0;
            }
         }
         if(this.raceType == "")
         {
            INIT.tempTrace(this.cnName);
         }
      }
      
      public function inFastData_byXML(xml0:XML, systemType0:String, father0:String) : void
      {
         this.xmlBat = xml0;
         this.father = father0;
         this.systemType = systemType0;
         ClassProperty.inData_byXML(this,xml0,pro_arr);
      }
      
      public function inData_byObj(obj:Object) : void
      {
         ClassProperty.inData(this,obj,pro_arr);
      }
      
      public function clone() : NormalBodyDefine
      {
         var d0:NormalBodyDefine = new NormalBodyDefine();
         d0.inData_byObj(this);
         return d0;
      }
      
      public function cloneByXml() : NormalBodyDefine
      {
         var d0:NormalBodyDefine = new NormalBodyDefine();
         d0.inData_byXML(this.xmlBat,this.systemType,this.father);
         return d0;
      }
      
      private function fleshIconUrl() : void
      {
         if(this.headIconUrl == "")
         {
            if(this.father == BodyFather.vehicle)
            {
               this.headIconUrl = "ThingsIcon/" + this.name;
            }
            else
            {
               this.headIconUrl = "IconGather/" + this.name;
            }
         }
      }
      
      public function getSwfName() : String
      {
         if(this.swfName == "")
         {
            return this.name;
         }
         return this.swfName;
      }
      
      public function lifeBarHaveLvB() : Boolean
      {
         return this.hitRect.width > 4;
      }
      
      public function haveStandRanB() : Boolean
      {
         if(this.father == BodyFather.friable || BodyFather.ranStandArr.indexOf(this.name) >= 0)
         {
            return true;
         }
         return false;
      }
      
      public function getStandImgArr() : Array
      {
         var n0:* = null;
         var arr0:Array = [];
         for each(n0 in this.imgArr)
         {
            if(n0.indexOf(BodyImageLabel.stand) == 0)
            {
               arr0.push(n0);
            }
         }
         return arr0;
      }
      
      public function isMan() : Boolean
      {
         return this.sex == BodySex.MALE;
      }
      
      public function isLandBody() : Boolean
      {
         return this.motionState == GroundMotionState.stand;
      }
      
      public function isSpecialBoss() : Boolean
      {
         return this.extraDropArmsB || this.showLevel >= 999;
      }
      
      public function getSayTitle() : String
      {
         return this.getRoleCn();
      }
      
      public function getBossSkillArr(extraTaskB0:Boolean, demonB0:Boolean) : Array
      {
         if(extraTaskB0 && Boolean(this.extraG))
         {
            return this.extraG.bossSkillArr.concat(this.extraG.stateArr);
         }
         if(demonB0 && this.demBossSkillArr.length > 0)
         {
            return this.demBossSkillArr;
         }
         return this.bossSkillArr;
      }
      
      public function getBossSkillTip(titleColor0:String = "") : String
      {
         var skillArr0:Array = this.skillArr.concat(this.getBossSkillArr(false,false)).concat(this.getAttackSkillArr()).concat(this.uiSkillArr);
         return SkillDescrip.getSkillArrGather(skillArr0,titleColor0);
      }
      
      public function getEditSkillArr() : Array
      {
         var arr0:Array = null;
         var name0:* = null;
         var skillD0:SkillDefine = null;
         if(Boolean(this.editSkillArr))
         {
            return this.editSkillArr;
         }
         this.editSkillArr = [];
         arr0 = this.skillArr.concat(this.bossSkillArr);
         for each(name0 in arr0)
         {
            skillD0 = Gaming.defineGroup.skill.getDefine(name0);
            if(Boolean(skillD0))
            {
               if(skillD0.wantDescripB || skillD0.showInLifeBarB)
               {
                  this.editSkillArr.push(name0);
               }
            }
         }
         return this.editSkillArr;
      }
      
      public function getEditSkillTip() : String
      {
         var skillArr0:Array = this.getEditSkillArr();
         return SkillDescrip.getSkillArrGather(skillArr0,"#00FFFF",false,false,true);
      }
      
      public function getAttackSkillArr() : Array
      {
         var h0:BodyAttackDefine = null;
         var arr0:Array = [];
         for each(h0 in this.hurtObj)
         {
            ArrayMethod.addNoRepeatArrInArr(arr0,h0.skillArr);
         }
         return arr0;
      }
      
      public function getWilderTipSkillArr() : Array
      {
         return this.skillArr.concat(this.getBossSkillArr(false,false)).concat(this.getAttackSkillArr()).concat(this.uiSkillArr).concat(this.wilderSkillArr);
      }
      
      public function getWilderSkillTip(titleColor0:String = "") : String
      {
         var skillArr0:Array = this.getWilderTipSkillArr();
         return SkillDescrip.getSkillArrGather(skillArr0,titleColor0);
      }
      
      public function getOtherUnitNameArr() : Array
      {
         var arr0:Array = null;
         var cnName0:* = null;
         var d0:NormalBodyDefine = null;
         if(this.otherUnitCnNameArr.length > 0)
         {
            arr0 = [];
            for each(cnName0 in this.otherUnitCnNameArr)
            {
               d0 = Gaming.defineGroup.body.getCnDefine(cnName0);
               arr0.push(d0.name);
            }
            return arr0;
         }
         return [];
      }
      
      public function getAttackDefine(label0:String) : BodyAttackDefine
      {
         return this.hurtObj[label0];
      }
      
      public function imgFollowPan(label0:String) : Boolean
      {
         var ad0:BodyAttackDefine = this.getAttackDefine(label0);
         if(Boolean(ad0))
         {
            return ad0.ingfollowB;
         }
         return false;
      }
      
      public function affterGetMovieDefine() : void
      {
         var n:* = undefined;
         var md0:MovieDefine = null;
         var d0:BodyAttackDefine = null;
         var r0:Rectangle = null;
         var max0:int = 0;
         var min0:int = 0;
         for(n in this.movieDefineArr)
         {
            md0 = this.movieDefineArr[n];
            md0.dealSoundUrl(this.name);
            d0 = this.hurtObj[md0.label];
            md0.nowAttackDefine = this.hurtObj[md0.label];
            if(Boolean(d0))
            {
               r0 = d0.grapRect;
               if(!(r0 is Rectangle))
               {
                  r0 = md0.attackMergeRect;
                  d0.grapRect = r0;
               }
               if(!r0)
               {
                  throw new Error("单位：" + this.cnName + "的攻击伤害定义" + md0.label + "没有grapRect。");
               }
               max0 = Maths.Long(r0.x - (this.hitRect.x + this.hitRect.width / 2),r0.y + r0.height / 2 - this.hitRect.y - this.hitRect.height / 2);
               min0 = Maths.Long(r0.x + r0.width - this.hitRect.x - this.hitRect.width / 2,r0.y + r0.height / 2 - this.hitRect.y - this.hitRect.height / 2);
               if(this.normalScaleX == 1)
               {
                  if(d0.grapMaxLen == -1)
                  {
                     d0.grapMaxLen = max0;
                  }
                  if(d0.grapMinLen == -1)
                  {
                     d0.grapMinLen = min0;
                  }
               }
               else
               {
                  if(d0.grapMaxLen == -1)
                  {
                     d0.grapMaxLen = min0;
                  }
                  if(d0.grapMinLen == -1)
                  {
                     d0.grapMinLen = max0;
                  }
               }
               if(r0.x < 0 && r0.x + r0.width > 0)
               {
                  d0.grapMinLen = 0;
               }
            }
         }
      }
      
      public function panCanActiveSkill(label0:String) : Boolean
      {
         var d0:BodyAttackDefine = this.hurtObj[label0];
         if(Boolean(d0))
         {
            return !d0.noUseOtherSkillB;
         }
         return true;
      }
      
      public function getGatherTip() : String
      {
         return "";
      }
      
      public function getImgTypeByLabel(imgType0:String, label0:String) : String
      {
         if(this.bossImgArr.indexOf(label0) >= 0)
         {
            return "normal";
         }
         return imgType0;
      }
      
      public function getBmpBByLabel(bmpB0:Boolean, label0:String) : Boolean
      {
         if(this.bossImgArr.indexOf(label0) >= 0)
         {
            return false;
         }
         return bmpB0;
      }
      
      public function getImgArrByUnitType(type0:String) : Array
      {
         if(type0 != UnitType.BOSS)
         {
            return ComMethod.deductArr(this.imgArr,this.bossImgArr);
         }
         return this.imgArr;
      }
      
      public function getAllImageUrl() : Array
      {
         var h0:BodyAttackDefine = null;
         var skillNameArr0:Array = null;
         var skillName0:* = null;
         var skillDefine0:SkillDefine = null;
         var arr0:Array = [];
         for each(h0 in this.hurtObj)
         {
            ComMethod.addNoRepeatArrInArr(arr0,h0.getAllImageUrl());
         }
         skillNameArr0 = this.skillArr.concat(this.bossSkillArr).concat(this.wilderSkillArr);
         if(Boolean(this.extraG))
         {
            skillNameArr0 = skillNameArr0.concat(this.extraG.bossSkillArr).concat(this.extraG.stateArr);
         }
         for each(skillName0 in skillNameArr0)
         {
            skillDefine0 = Gaming.defineGroup.skill.getDefine(skillName0);
            ComMethod.addNoRepeatArrInArr(arr0,skillDefine0.getAllImageUrl());
         }
         return arr0;
      }
      
      public function canSayFilterB() : Boolean
      {
         return this.shell != ElementShell.other && this.raceType != RACE_ROBOT;
      }
      
      public function haveDieBoomEffectB() : Boolean
      {
         if(this.raceType == RACE_SHIP || this.raceType == RACE_STONE)
         {
            return true;
         }
         if(this.father == BodyFather.snake)
         {
            if(this.dieJumpMul == 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getEditDNull() : BodyEditDefine
      {
         return this.editD;
      }
      
      public function getEditDNew() : BodyEditDefine
      {
         if(!this.editD)
         {
            this.editD = new BodyEditDefine();
         }
         return this.editD;
      }
      
      public function getName() : String
      {
         return this.name;
      }
      
      public function getCnName() : String
      {
         return this.cnName;
      }
      
      public function getRoleCn() : String
      {
         if(this.name == RoleName.Striker || this.name == "Striker2")
         {
            return "小白";
         }
         if(this.name == RoleName.WenJie)
         {
            return "文杰";
         }
         if(this.name == RoleName.ZangShi)
         {
            return "藏师";
         }
         if(this.name == RoleName.XiaoMei)
         {
            return "小美";
         }
         return this.cnName;
      }
      
      public function getLoveCn(p1B0:Boolean = false) : String
      {
         if(p1B0)
         {
            return "自信度";
         }
         if(this.sex == BodySex.FEMALE)
         {
            return "好感度";
         }
         return "忠诚度";
      }
   }
}


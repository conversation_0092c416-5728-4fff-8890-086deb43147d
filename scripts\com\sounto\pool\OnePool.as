package com.sounto.pool
{
   public class OnePool
   {
      private var arr:Array = [];
      
      public var maxNum:int = 1;
      
      public function OnePool(maxNum0:int = 1)
      {
         super();
         this.maxNum = maxNum0;
      }
      
      public function addObject(b0:Object) : Boolean
      {
         var arr0:Array = null;
         var max0:int = this.maxNum;
         if(max0 > 0)
         {
            arr0 = this.arr;
            if(arr0.length < max0)
            {
               arr0.push(b0);
               return true;
            }
         }
         return false;
      }
      
      public function getObject() : Object
      {
         var b0:Object = null;
         var len0:int = int(this.arr.length);
         if(len0 > 0)
         {
            b0 = this.arr[len0 - 1];
            this.arr.pop();
            return b0;
         }
         return null;
      }
      
      public function clearBigQuote() : void
      {
         var obj0:Object = null;
         for each(obj0 in this.arr)
         {
            if(obj0.hasOwnProperty("clearBigQuote"))
            {
               obj0["clearBigQuote"]();
            }
         }
      }
      
      public function doChildFun(funName0:String) : void
      {
         var obj0:Object = null;
         for each(obj0 in this.arr)
         {
            obj0[funName0]();
         }
      }
      
      public function clear() : void
      {
         this.arr.length = 0;
      }
   }
}


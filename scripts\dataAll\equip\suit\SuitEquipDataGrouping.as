package dataAll.equip.suit
{
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipFatherDefine;
   
   public class SuitEquipDataGrouping
   {
      public var partObj:Object = {};
      
      public var fatherDefine:EquipFatherDefine;
      
      public var name:String = "";
      
      public var index:int = 0;
      
      public var suitPartNum:int = 0;
      
      public function SuitEquipDataGrouping()
      {
         super();
      }
      
      public function inData(f0:EquipFatherDefine, index0:int) : void
      {
         this.name = f0.name;
         this.fatherDefine = f0;
         this.index = index0;
      }
      
      public function addEquipData(da0:EquipData) : void
      {
         var type0:String = da0.save.partType;
         if(!this.partObj.hasOwnProperty(type0))
         {
            ++this.suitPartNum;
         }
         this.partObj[type0] = da0;
      }
   }
}


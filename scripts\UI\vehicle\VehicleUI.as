package UI.vehicle
{
   import UI.UIShow;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.bag.ItemsEditCtrl;
   import UI.base.AppNormalUI;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import dataAll._player.supple.PlayerDataSupple;
   import dataAll.equip.EquipData;
   import dataAll.equip.vehicle.VehicleData;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class VehicleUI extends AppNormalUI
   {
      public var labelBox:LabelBox = new LabelBox();
      
      private var labelTag:Sprite = null;
      
      private var gripTag:Sprite = null;
      
      private var closeBtn:SimpleButton;
      
      private var tipBtn:SimpleButton;
      
      private var pageTag:Sprite;
      
      private var labelArr:Array = ["compose","upgrade","evolution","strengthen","skill"];
      
      private var listTxt:TextField;
      
      public var itemsBox:ItemsGripBox = new ItemsGripBox();
      
      private var composeBoard:VehicleComposeBoard = new VehicleComposeBoard();
      
      private var upgradeBoard:VehicleUpgradeBoard = new VehicleUpgradeBoard();
      
      private var evolutionBoard:VehicleEvolutionBoard = new VehicleEvolutionBoard();
      
      private var strengthenBoard:VehicleStrengthenBoard = new VehicleStrengthenBoard();
      
      private var skillBoard:VehicleSkillBoard = new VehicleSkillBoard();
      
      private var boxArr:Array = [this.composeBoard,this.upgradeBoard,this.evolutionBoard,this.strengthenBoard,this.skillBoard];
      
      private var nowBoard:NormalUI;

      // 编辑按钮
      private var editButton:NormalBtn = null;

      public function VehicleUI()
      {
         super();
         UICn = "载具";
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var label0:* = null;
         var name0:String = null;
         var box0:NormalUI = null;
         elementNameArr = ["listTxt","labelTag","pageTag","tipBtn","gripTag","closeBtn","listTxt"];
         super.setImg(img0);
         this.labelBox.arg.init(7,1,-6,0);
         addChild(this.labelBox);
         this.labelBox.inData("midLabelBtn",this.labelArr,["合成","升级","进化","强化","技能"]);
         this.labelBox.setChoose_byIndex(0);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.labelBox.x = this.labelTag.x;
         this.labelBox.y = this.labelTag.y;
         addChild(this.itemsBox);
         this.itemsBox.setIconPro("equipGrip",50,50);
         this.itemsBox.arg.init(3,5,8,8);
         this.itemsBox.evt.setWantEvent(true,false,false,true,true);
         this.itemsBox.x = this.gripTag.x;
         this.itemsBox.y = this.gripTag.y;
         this.itemsBox.pageBox.setToNormalBtn();
         this.itemsBox.pageBox.setXY_bySp(this.pageTag,this.itemsBox);
         this.itemsBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.itemsBox);
         ItemsEditCtrl.addEvent_byItemsGripBox(this.itemsBox);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OVER,this.tipOver);
         this.tipBtn.addEventListener(MouseEvent.MOUSE_OUT,this.tipOut);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         for each(label0 in this.labelArr)
         {
            name0 = label0 + "Board";
            box0 = this[name0];
            box0.setImg(Gaming.swfLoaderManager.getResource("VehicleUI",name0));
            addChild(box0);
            box0.hide();
            box0["vehicleUI"] = this;
         }

         // 创建编辑按钮
         this.createEditButton();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }

      // 创建编辑按钮
      private function createEditButton() : void
      {
         if(!this.editButton)
         {
            this.editButton = new NormalBtn();
            this.editButton.addEventListener(MouseEvent.CLICK, this.editButtonClick);

            addChild(this.editButton);

            try
            {
               this.editButton.setName("编辑");
               this.editButton.width = 40;
               this.editButton.height = 20;

               this.editButton.graphics.clear();
               this.editButton.graphics.beginFill(0xFF6600, 0.9);
               this.editButton.graphics.drawRoundRect(0, 0, 40, 20, 5, 5);
               this.editButton.graphics.endFill();

               if(this.editButton.textField)
               {
                  this.editButton.textField.textColor = 0xFFFFFF;
                  this.editButton.textField.size = 10;
                  this.editButton.textField.text = "编辑";
               }
            }
            catch(styleError:Error)
            {
               this.editButton.setName("编辑");
            }

            // 设置按钮位置
            this.editButton.x = 650;
            this.editButton.y = 50;
         }
      }

      // 编辑按钮点击事件
      private function editButtonClick(e:MouseEvent) : void
      {
         Gaming.uiGroup.alertBox.showInfo("载具编辑功能已激活！");
      }
      
      public function get nowData() : VehicleData
      {
         return this.upgradeBoard.nowData;
      }
      
      public function set nowData(v0:VehicleData) : void
      {
         this.upgradeBoard.nowData = v0;
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshBag();
         this.showBox(this.labelBox.nowLabel);
         PlayerDataSupple.vehicleSkillBack();
      }
      
      override public function hide() : void
      {
         super.hide();
         this.composeBoard.hide();
      }
      
      public function outLoginEvent() : void
      {
         var ui0:NormalUI = null;
         for each(ui0 in this.boxArr)
         {
            if(ui0.hasOwnProperty("outLoginEvent"))
            {
               ui0["outLoginEvent"]();
            }
         }
      }
      
      public function fleshBag() : void
      {
         var da0:EquipData = null;
         var arr0:Array = Gaming.PG.da.getEquipDataArr(true,true,true,false);
         var arr2:Array = [];
         for each(da0 in arr0)
         {
            if(da0 is VehicleData)
            {
               arr2.push(da0);
            }
         }
         this.itemsBox.inData_byArr(arr2,"inData_equip");
      }
      
      public function setBagText(str0:String) : void
      {
         this.listTxt.htmlText = str0;
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         var n:* = undefined;
         if(label0 == "")
         {
            label0 = this.labelArr[0];
         }
         this.labelBox.setChoose(label0);
         for(n in this.boxArr)
         {
            this.boxArr[n].hide();
         }
         this.nowBoard = this[label0 + "Board"];
         if(Boolean(this.nowBoard))
         {
            this.nowBoard.show();
         }
      }
      
      public function gotoUpgradeOne(da0:VehicleData) : void
      {
         UIShow.showByLabel("vehicle");
         this.nowData = da0;
         this.showBox("upgrade");
      }
      
      public function gotoStrengthenOne(da0:VehicleData) : void
      {
         UIShow.showByLabel("vehicle");
         this.nowData = da0;
         this.showBox("strengthen");
      }
      
      public function gotoSkillOne(da0:VehicleData) : void
      {
         UIShow.showByLabel("vehicle");
         this.nowData = da0;
         this.skillBoard.clearAll();
         this.showBox("skill");
      }
      
      public function gotoEvolutionOne(da0:VehicleData) : void
      {
         UIShow.showByLabel("vehicle");
         this.nowData = da0;
         this.showBox("evolution");
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         if(this.nowBoard.hasOwnProperty("gripClick"))
         {
            this.nowBoard["gripClick"](e);
         }
      }
      
      private function tipOver(e:MouseEvent) : void
      {
         var str0:String = "";
         str0 += "战车载具拥有3种攻击方式：";
         str0 += "\n1、主炮攻击：伤害与你当前已装备火炮的战斗力有关系。";
         str0 += "\n2、机枪攻击：伤害与你当前已装备步枪的战斗力有关系。";
         str0 += "\n3、碾压攻击：伤害与你当前已装备狙击的战斗力有关系。";
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.setText(str0);
            Gaming.uiGroup.tipBox.textTip.show();
            Gaming.uiGroup.tipBox.followMouseB = true;
         }
         else
         {
            Gaming.uiGroup.tipBox.hide();
         }
      }
      
      private function tipOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      public function FTimer() : void
      {
      }
   }
}


package dataAll.bullet
{
   import com.sounto.utils.ClassProperty;
   
   public class BulletLineDefine
   {
      public static var pro_arr:Array = [];
      
      public var color:String = "0xFFFFFF";
      
      public var lightColor:String = "0xFFFF00";
      
      public var size:Number = 1;
      
      public var lightSize:Number = 4;
      
      public var blendMode:String = "normal";
      
      public var type:String = "";
      
      public var editB:Boolean = false;
      
      public function BulletLineDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
   }
}


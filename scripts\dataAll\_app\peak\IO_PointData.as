package dataAll._app.peak
{
   public interface IO_PointData
   {
      function usePoint(param1:String) : void;
      
      function delPoint(param1:String) : void;
      
      function resetPoint() : void;
      
      function setPoint(param1:String, param2:int) : void;
      
      function getAllPoint() : int;
      
      function getSurplusPoint() : int;
      
      function getUsePoint() : int;
      
      function getUsePointOne(param1:String) : Number;
      
      function getMustPointOne(param1:String) : Number;
      
      function getOneLv(param1:String) : int;
   }
}


package dataAll._app.union.building.define
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   
   public class UnionBuildingDefine
   {
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var lvName:String = "";
      
      public var iconUrl:String = "";
      
      public var fleshAddDataB:Boolean = false;
      
      public var hideB:Boolean = false;
      
      public function UnionBuildingDefine()
      {
         super();
         this.maxLv = 0;
      }
      
      public function get maxLv() : Number
      {
         return this.CF.getAttribute("maxLv");
      }
      
      public function set maxLv(v0:Number) : void
      {
         this.CF.setAttribute("maxLv",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         if(this.iconUrl == "")
         {
            this.iconUrl = "UnionUI/" + this.name + "Icon";
         }
         if(this.name == "geology")
         {
            if(this.maxLv < 22)
            {
               this.maxLv = 22;
            }
         }
      }
   }
}


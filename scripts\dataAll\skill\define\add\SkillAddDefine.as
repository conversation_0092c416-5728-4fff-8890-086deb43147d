package dataAll.skill.define.add
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   
   public class SkillAddDefine
   {
      public static var pro_arr:Array = [];
      
      public var pro:String = "";
      
      public var range:Array = [];
      
      public var min:Number = 0;
      
      public var max:Number = 0;
      
      public var info:String = "";
      
      public var fixedNum:int = 0;
      
      public var openLv:int = 0;
      
      public function SkillAddDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         if(this.range.length >= 2)
         {
            this.min = Number(this.range[0]);
            this.max = Number(this.range[1]);
            if(String(xml0.@fixedNum) == "")
            {
               this.fixedNum = ComMethod.haveDecimalNum(this.min);
               if(this.pro == "mul")
               {
                  this.fixedNum -= 2;
               }
               if(this.fixedNum < 0)
               {
                  this.fixedNum = 0;
               }
            }
         }
      }
      
      public function getRandomValue() : Number
      {
         return this.min + (this.max - this.min) * Math.random();
      }
      
      public function getValueString(value0:Number) : String
      {
         var first0:String = value0 > 0 ? "+" : "";
         return this.getValueStringNoFirst(value0);
      }
      
      public function getValueStringNoFirst(value0:Number) : String
      {
         if(this.pro == "mul")
         {
            return Number(value0 * 100).toFixed(this.fixedNum) + "%";
         }
         return Number(value0.toFixed(this.fixedNum)) + "";
      }
      
      public function getRangeStr() : String
      {
         return this.getValueStringNoFirst(this.min) + "~" + this.getValueStringNoFirst(this.max);
      }
   }
}


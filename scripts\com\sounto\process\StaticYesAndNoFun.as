package com.sounto.process
{
   public class StaticYesAndNoFun
   {
      protected static var yes_fun:Function = null;
      
      protected static var yesFunObj:Object = {};
      
      protected static var no_fun:Function = null;
      
      protected static var noFunObj:Object = {};
      
      public function StaticYesAndNoFun()
      {
         super();
      }
      
      public static function setFun(yesFun0:Function, noFun0:Function = null, target0:String = "") : void
      {
         if(target0 == "")
         {
            yes_fun = yesFun0;
            no_fun = noFun0;
         }
         else
         {
            yesFunObj[target0] = yesFun0;
            noFunObj[target0] = noFun0;
         }
      }
      
      protected static function doYesFun(data0:*, target0:String = "") : void
      {
         var fun0:Function = null;
         if(target0 == "")
         {
            fun0 = yes_fun;
            yes_fun = null;
         }
         else
         {
            fun0 = yesFunObj[target0];
            yesFunObj[target0] = null;
         }
         if(fun0 is Function)
         {
            fun0(data0);
         }
      }
      
      protected static function doNoFun(data0:*, target0:String = "") : void
      {
         var fun0:Function = null;
         if(target0 == "")
         {
            fun0 = no_fun;
            no_fun = null;
         }
         else
         {
            fun0 = noFunObj[target0];
            noFunObj[target0] = null;
         }
         if(fun0 is Function)
         {
            fun0(data0);
         }
      }
      
      public static function clear() : void
      {
         yes_fun = null;
         yesFunObj = {};
         no_fun = null;
         noFunObj = {};
      }
   }
}


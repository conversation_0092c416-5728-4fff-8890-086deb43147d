package dataAll._app.union.define
{
   import com.sounto.cf.NiuBiCF;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.must.define.MustDefine;
   
   public class DonationDefine
   {
      public static const MONEY:String = "money";
      
      public static const OTHER:String = "other";
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var must:MustDefine = new MustDefine();
      
      public var gift:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      public function DonationDefine()
      {
         super();
         this.contribution = 0;
         this.dayNum = 0;
      }
      
      public function get contribution() : Number
      {
         return this.CF.getAttribute("contribution");
      }
      
      public function set contribution(v0:Number) : void
      {
         this.CF.setAttribute("contribution",v0);
      }
      
      public function get dayNum() : Number
      {
         return this.CF.getAttribute("dayNum");
      }
      
      public function set dayNum(v0:Number) : void
      {
         this.CF.setAttribute("dayNum",v0);
      }
   }
}


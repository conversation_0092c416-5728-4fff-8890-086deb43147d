package dataAll._app.partner.ability
{
   import dataAll._base.NormalDefine;
   
   public class PartnerAbilityDefine extends NormalDefine
   {
      public static var pro_arr:Array = null;
      
      public function PartnerAbilityDefine()
      {
         super();
      }
      
      override public function inData_byXML(xml0:XML, father0:String = "") : void
      {
         super.inData_byXML(xml0,father0);
      }
      
      override protected function getBaseClassProArr() : Array
      {
         return pro_arr;
      }
      
      override protected function getFinalProArr() : Array
      {
         return pro_arr;
      }
   }
}


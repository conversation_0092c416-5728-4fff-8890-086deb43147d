package dataAll._app.union.building.geology
{
   import dataAll._app.union.building.define.UnionGeologyThingsDefine;
   import dataAll.things.define.ThingsDefine;
   
   public class UnionGeologyThingsData
   {
      public var save:UnionGeologyThingsSave = null;
      
      public var def:UnionGeologyThingsDefine = null;
      
      public var thingsDefine:ThingsDefine = null;
      
      public function UnionGeologyThingsData()
      {
         super();
      }
      
      public function inData_bySave(s0:UnionGeologyThingsSave, def0:UnionGeologyThingsDefine) : void
      {
         this.save = s0;
         this.def = def0;
         this.setDefine(def0);
      }
      
      public function setDefine(def0:UnionGeologyThingsDefine) : void
      {
         this.def = def0;
         this.thingsDefine = Gaming.defineGroup.things.getDefine(def0.name);
      }
      
      public function newDayCtrl() : void
      {
         this.save.now = 0;
      }
      
      public function getOreBodyName() : String
      {
         if(this.def.orePro > 0 && Boolean(this.thingsDefine))
         {
            return this.thingsDefine.name + "Orc";
         }
         return "";
      }
      
      public function isOverB() : Boolean
      {
         var max0:Number = this.def.max;
         if(Boolean(this.thingsDefine))
         {
            return this.save.now >= max0;
         }
         return false;
      }
   }
}


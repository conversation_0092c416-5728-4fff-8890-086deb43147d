package dataAll.pet.skill
{
   import dataAll._player.PlayerData;
   import dataAll.pet.gene.GeneData;
   import dataAll.skill.HeroSkillDataGroup;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.skill.save.HeroSkillSaveGroup;
   
   public class PetSkillDataGroup extends HeroSkillDataGroup
   {
      public function PetSkillDataGroup()
      {
         super();
      }
      
      public function initData_byGeneData(geneDa0:GeneData, pd0:PlayerData) : void
      {
         saveGroup = new HeroSkillSaveGroup();
         this.initGripMaxNum();
      }
      
      public function initGripMaxNum() : void
      {
         saveGroup.gripMaxNum = 100;
         saveGroup.unlockTo(99);
      }
      
      public function getStudyMustLv() : int
      {
         return HeroSkillDefine.getStudyMustLv(dataArr.length);
      }
   }
}


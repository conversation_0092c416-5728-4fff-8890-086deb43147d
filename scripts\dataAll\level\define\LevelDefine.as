package dataAll.level.define
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.level.define._fixed.LevelDefineFixedDefine;
   import dataAll.level.define.drop.LevelDropDefine;
   import dataAll.level.define.event.LevelEventDefine;
   import dataAll.level.define.event.LevelEventDefineGroup;
   import dataAll.level.define.event.LevelEventOrderDefine;
   import dataAll.level.define.info.LevelInfoDefine;
   import dataAll.level.define.mapRect.MapRectGroup;
   import dataAll.level.define.unit.OneUnitOrderDefine;
   import dataAll.level.define.unit.UnitOrderDefine;
   import dataAll.level.define.unit.UnitOrderDefineGroup;
   
   public class LevelDefine
   {
      public static var pro_arr:Array = [];
      
      public var originalXml:XML;
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var father:String = "";
      
      public var sceneLabel:String = "";
      
      public var drop:LevelDropDefine = new LevelDropDefine();
      
      public var info:LevelInfoDefine = new LevelInfoDefine();
      
      public var fixed:LevelDefineFixedDefine = new LevelDefineFixedDefine();
      
      public var unitG:UnitOrderDefineGroup = new UnitOrderDefineGroup();
      
      public var rectG:MapRectGroup = new MapRectGroup();
      
      public var eventG:LevelEventDefineGroup = new LevelEventDefineGroup();
      
      public function LevelDefine()
      {
         super();
      }
      
      public static function checkRepeatByName(name0:String) : Boolean
      {
         var n0:String = null;
         var num0:int = 0;
         var index0:int = int(name0.indexOf("_"));
         if(index0 > 0)
         {
            n0 = name0.substring(index0 + 1);
            num0 = int(n0);
            return num0 > 1;
         }
         return false;
      }
      
      public function inData_byXML(xml0:XML, father0:String) : void
      {
         this.originalXml = xml0;
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         this.name = xml0.@name;
         this.cnName = xml0.@cnName;
         this.father = father0;
         this.info.inData_byXML(xml0.info[0]);
         this.drop.inData_byXML(xml0.drop[0]);
         this.fixed.inData_byXML(xml0.fixed[0]);
         this.unitG.inData_byXML(xml0.unitG[0]);
         var lastEvt0:LevelEventOrderDefine = this.eventG.inData_byXML(xml0.eventG[0]);
         this.rectG.inData_byXML(xml0.rectG[0]);
         this.dealLastEvent(lastEvt0);
         this.dealMusicByWorldMapName();
      }
      
      private function dealLastEvent(e0:LevelEventOrderDefine) : void
      {
         var id0:String = null;
         var unitD0:UnitOrderDefine = null;
         var ud0:OneUnitOrderDefine = null;
         if(Boolean(e0))
         {
            id0 = e0.property;
            unitD0 = this.unitG.getUnitOrderDefine(id0);
            if(Boolean(unitD0))
            {
               if(!unitD0.haveBossB)
               {
                  for each(ud0 in unitD0.arr)
                  {
                     ud0.lastB = true;
                  }
               }
            }
         }
      }
      
      public function fleshDataByOutChange() : void
      {
         this.unitG.fleshBodyName();
         this.unitG.fleshSuperAndBoss();
         this.dealMusicByWorldMapName();
      }
      
      private function dealMusicByWorldMapName() : void
      {
         var w_name0:String = null;
         var w_d0:WorldMapDefine = null;
         if(this.info.music == "")
         {
            w_name0 = this.getWorldMapName();
            w_d0 = Gaming.defineGroup.worldMap.getDefine(w_name0);
            if(Boolean(w_d0))
            {
               this.info.music = "music_" + w_d0.geogType;
            }
         }
      }
      
      public function getWorldMapName() : String
      {
         return this.sceneLabel.split("_")[0];
      }
      
      public function getDropPro(type0:String) : Number
      {
         return this.drop.getDropPro(type0);
      }
      
      public function isWilderB() : Boolean
      {
         return this.name == "wilder";
      }
      
      public function isOtherFatherB() : Boolean
      {
         return this.father == "other";
      }
      
      public function clone() : LevelDefine
      {
         var d0:LevelDefine = new LevelDefine();
         d0.inData_byXML(this.originalXml,this.father);
         return d0;
      }
      
      public function getCloneRectG() : MapRectGroup
      {
         var r0:MapRectGroup = new MapRectGroup();
         r0.inData_byXML(this.originalXml.rectG[0]);
         return r0;
      }
      
      public function fixedBy(d0:LevelDefine, fixedUseMeB0:Boolean = false) : void
      {
         var f0:LevelDefineFixedDefine = d0.fixed;
         if(fixedUseMeB0)
         {
            f0 = this.fixed;
         }
         this.info.fixedBy(d0.info,f0.info);
         this.dealMusicByWorldMapName();
         if(f0.drop == "all")
         {
            this.drop = d0.drop;
         }
         this.rectG.fixedBy(d0.rectG,f0.rectG);
         this.unitG.fixedBy(d0.unitG,f0.unitG);
         this.eventG.fixedBy(d0.eventG,f0.eventG);
      }
      
      public function getSimulationArr() : Array
      {
         var eventArr2:Array = null;
         var e0:LevelEventDefine = null;
         var num0:int = 0;
         var i:int = 0;
         var orderD0:LevelEventOrderDefine = null;
         var arr0:Array = [];
         var eventArr0:Array = this.eventG.arr;
         for each(eventArr2 in eventArr0)
         {
            for each(e0 in eventArr2)
            {
               num0 = e0.condtion.doNumber;
               for(i = 0; i < num0; i++)
               {
                  for each(orderD0 in e0.orderArr)
                  {
                     if(orderD0.order == "createUnit")
                     {
                        arr0 = arr0.concat(this.simulationCreateUnit(orderD0));
                        if(e0.condtion.isDoOneOrderB())
                        {
                           break;
                        }
                     }
                  }
               }
            }
         }
         return arr0;
      }
      
      private function simulationCreateUnit(od0:LevelEventOrderDefine) : Array
      {
         var proArr0:Array = null;
         var num0:int = 0;
         var i:int = 0;
         var index0:int = 0;
         var one_d0:OneUnitOrderDefine = null;
         var arr0:Array = [];
         var d0:UnitOrderDefine = this.unitG.getUnitOrderDefine(od0.property);
         if(Boolean(d0))
         {
            if(d0.camp == "enemy")
            {
               proArr0 = d0.getNumArr();
               num0 = d0.getNumSum();
               for(i = 0; i < num0; i++)
               {
                  index0 = ComMethod.getPro_byArrSum(proArr0);
                  if(d0.numberType == "number")
                  {
                     --proArr0[index0];
                     if(proArr0[index0] < 0)
                     {
                        proArr0[index0] = 0;
                     }
                  }
                  one_d0 = d0.arr[index0];
                  if(one_d0.camp == "enemy")
                  {
                     arr0.push(one_d0);
                  }
               }
            }
         }
         return arr0;
      }
   }
}


package UI.bag
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import dataAll.arms.ArmsData;
   import dataAll.equip.EquipData;
   import dataAll.skill.HeroSkillData;
   import dataAll.vehicle.VehicleData;
   import dataAll._app.edit.card.BossCardData;
   import flash.display.DisplayObjectContainer;
   import flash.display.Graphics;
   import flash.events.MouseEvent;
   import flash.utils.setTimeout;
   
   /**
    * 通用物品编辑控制器
    * 为装备、武器、载具、魂卡等添加编辑功能
    */
   public class ItemsEditCtrl
   {
      private static var editButton:NormalBtn = null;
      private static var currentEditGrip:ItemsGrid = null;
      private static var currentParent:DisplayObjectContainer = null;
      
      public function ItemsEditCtrl()
      {
      }
      
      /**
       * 为ItemsGripBox添加编辑事件
       */
      public static function addEvent_byItemsGripBox(box:ItemsGripBox):void
      {
         // 性能优化：延迟添加事件监听器
         if(box)
         {
            // 使用弱引用避免内存泄漏
            box.addEventListener(ClickEvent.ON_CLICK, onGripClick, false, 0, true);
         }
      }
      
      /**
       * 移除ItemsGripBox的编辑事件
       */
      public static function removeEvent_byItemsGripBox(box:ItemsGripBox):void
      {
         if(box)
         {
            box.removeEventListener(ClickEvent.ON_CLICK, onGripClick);
         }
      }
      
      /**
       * 格子点击事件处理
       */
      private static function onGripClick(e:ClickEvent):void
      {
         // 性能优化：快速检查避免不必要的处理
         if(!e || !e.child) {
            hideEditButton();
            return;
         }

         var grip:ItemsGrid = e.child as ItemsGrid;
         if(grip && grip.itemsData && isEditableItem(grip.itemsData))
         {
            showEditButton(grip);
         }
         else
         {
            hideEditButton();
         }
      }
      
      /**
       * 检查物品是否可编辑
       */
      private static function isEditableItem(itemData:*):Boolean
      {
         return (itemData is EquipData) || 
                (itemData is ArmsData) || 
                (itemData is VehicleData) || 
                (itemData is HeroSkillData) || 
                (itemData is BossCardData);
      }
      
      /**
       * 显示编辑按钮
       */
      private static function showEditButton(grip:ItemsGrid):void
      {
         if(!grip || !grip.itemsData || !grip.parent)
         {
            hideEditButton();
            return;
         }
         
         currentEditGrip = grip;
         currentParent = grip.parent as DisplayObjectContainer;
         
         // 创建编辑按钮（如果不存在）
         if(!editButton)
         {
            editButton = new NormalBtn();
            editButton.setName("编辑");
            editButton.addEventListener(MouseEvent.CLICK, onEditButtonClick, false, 0, true);

            // 简化按钮样式以提高性能
            try
            {
               editButton.graphics.clear();
               editButton.graphics.beginFill(0xFF6600, 0.8); // 简化的橙色背景
               editButton.graphics.drawRect(0, 0, 40, 20); // 使用简单矩形
               editButton.graphics.endFill();

               // 设置文字样式
               if(editButton.textField)
               {
                  editButton.textField.textColor = 0xFFFFFF;
                  editButton.textField.size = 10;
               }
            }
            catch(styleError:Error)
            {
               // 样式设置失败时使用默认样式
            }
         }
         
         // 设置按钮位置（在物品图标右侧）
         var targetX:Number = grip.x + grip.width + 8;
         var targetY:Number = grip.y + (grip.height - 22) / 2;
         
         // 边界检查
         if(currentParent.width > 0 && targetX + 45 > currentParent.width)
         {
            targetX = grip.x - 53; // 放在左侧
         }
         if(targetY < 0) targetY = grip.y + 5;
         
         editButton.x = targetX;
         editButton.y = targetY;
         editButton.visible = true;
         
         // 添加到父容器
         if(editButton.parent != currentParent)
         {
            if(editButton.parent) editButton.parent.removeChild(editButton);
            currentParent.addChild(editButton);
         }
         
         // 置于最前面
         currentParent.setChildIndex(editButton, currentParent.numChildren - 1);
      }
      
      /**
       * 隐藏编辑按钮
       */
      public static function hideEditButton():void
      {
         if(editButton)
         {
            editButton.visible = false;
         }
         currentEditGrip = null;
         currentParent = null;
      }
      
      /**
       * 编辑按钮点击事件
       */
      private static function onEditButtonClick(e:MouseEvent):void
      {
         if(!currentEditGrip || !currentEditGrip.itemsData)
         {
            return;
         }
         
         var itemData:* = currentEditGrip.itemsData;
         
         if(itemData is EquipData)
         {
            showEquipEditDialog(itemData as EquipData);
         }
         else if(itemData is ArmsData)
         {
            showArmsEditDialog(itemData as ArmsData);
         }
         else if(itemData is VehicleData)
         {
            showVehicleEditDialog(itemData as VehicleData);
         }
         else if(itemData is HeroSkillData)
         {
            showSkillEditDialog(itemData as HeroSkillData);
         }
         else if(itemData is BossCardData)
         {
            showBossCardEditDialog(itemData as BossCardData);
         }
      }
      
      /**
       * 显示装备编辑对话框
       */
      private static function showEquipEditDialog(equipData:EquipData):void
      {
         var equipDefine:* = equipData.save.getDefine();
         var currentLevel:int = equipData.save.getTrueLevel();
         var maxLevel:int = equipDefine.getMaxLevel ? equipDefine.getMaxLevel() : 99;
         
         var editText:String = "编辑装备: " + equipDefine.cnName + "\n";
         editText += "当前等级: " + currentLevel + "/" + maxLevel + "\n\n";
         editText += "请选择操作:\n";
         editText += "1. 设置等级 (输入数字)\n";
         editText += "2. 满级 (输入 max)\n";
         editText += "3. 重置 (输入 reset)";
         
         Gaming.uiGroup.alertBox.textInput.showTextInput(editText, currentLevel.toString(), 
            function(inputValue:String):void { processEquipEdit(equipData, inputValue); }, 
            "yesAndNo", 999);
      }
      
      /**
       * 处理装备编辑
       */
      private static function processEquipEdit(equipData:EquipData, inputValue:String):void
      {
         try
         {
            var equipDefine:* = equipData.save.getDefine();
            var maxLevel:int = equipDefine.getMaxLevel ? equipDefine.getMaxLevel() : 99;
            var originalLevel:int = equipData.save.getTrueLevel();
            
            if(inputValue.toLowerCase() == "max")
            {
               equipData.save.lv = maxLevel;
               Gaming.uiGroup.alertBox.showSuccess("✅ 装备 " + equipDefine.cnName + " 已设置为满级 (" + maxLevel + ")");
            }
            else if(inputValue.toLowerCase() == "reset")
            {
               equipData.save.lv = 1;
               Gaming.uiGroup.alertBox.showSuccess("✅ 装备 " + equipDefine.cnName + " 已重置为1级");
            }
            else
            {
               var newLevel:int = parseInt(inputValue);
               if(isNaN(newLevel) || newLevel < 1)
               {
                  Gaming.uiGroup.alertBox.showError("❌ 请输入有效的等级数字 (1-" + maxLevel + ")");
                  return;
               }
               
               if(newLevel > maxLevel) newLevel = maxLevel;
               equipData.save.lv = newLevel;
               Gaming.uiGroup.alertBox.showSuccess("✅ 装备 " + equipDefine.cnName + " 等级已设置为 " + newLevel);
            }
            
            // 刷新显示
            refreshAllUI();
            hideEditButton();
         }
         catch(e:Error)
         {
            Gaming.uiGroup.alertBox.showError("❌ 编辑装备失败: " + e.message);
         }
      }
      
      /**
       * 显示武器编辑对话框
       */
      private static function showArmsEditDialog(armsData:ArmsData):void
      {
         var armsDefine:* = armsData.save.getDefine();
         var currentLevel:int = armsData.save.getTrueLevel();
         var maxLevel:int = armsDefine.getMaxLevel ? armsDefine.getMaxLevel() : 99;
         
         var editText:String = "编辑武器: " + armsDefine.cnName + "\n";
         editText += "当前等级: " + currentLevel + "/" + maxLevel + "\n\n";
         editText += "请选择操作:\n";
         editText += "1. 设置等级 (输入数字)\n";
         editText += "2. 满级 (输入 max)\n";
         editText += "3. 重置 (输入 reset)";
         
         Gaming.uiGroup.alertBox.textInput.showTextInput(editText, currentLevel.toString(), 
            function(inputValue:String):void { processArmsEdit(armsData, inputValue); }, 
            "yesAndNo", 999);
      }
      
      /**
       * 处理武器编辑
       */
      private static function processArmsEdit(armsData:ArmsData, inputValue:String):void
      {
         try
         {
            var armsDefine:* = armsData.save.getDefine();
            var maxLevel:int = armsDefine.getMaxLevel ? armsDefine.getMaxLevel() : 99;
            
            if(inputValue.toLowerCase() == "max")
            {
               armsData.save.lv = maxLevel;
               Gaming.uiGroup.alertBox.showSuccess("✅ 武器 " + armsDefine.cnName + " 已设置为满级 (" + maxLevel + ")");
            }
            else if(inputValue.toLowerCase() == "reset")
            {
               armsData.save.lv = 1;
               Gaming.uiGroup.alertBox.showSuccess("✅ 武器 " + armsDefine.cnName + " 已重置为1级");
            }
            else
            {
               var newLevel:int = parseInt(inputValue);
               if(isNaN(newLevel) || newLevel < 1)
               {
                  Gaming.uiGroup.alertBox.showError("❌ 请输入有效的等级数字 (1-" + maxLevel + ")");
                  return;
               }
               
               if(newLevel > maxLevel) newLevel = maxLevel;
               armsData.save.lv = newLevel;
               Gaming.uiGroup.alertBox.showSuccess("✅ 武器 " + armsDefine.cnName + " 等级已设置为 " + newLevel);
            }
            
            refreshAllUI();
            hideEditButton();
         }
         catch(e:Error)
         {
            Gaming.uiGroup.alertBox.showError("❌ 编辑武器失败: " + e.message);
         }
      }
      
      /**
       * 显示载具编辑对话框
       */
      private static function showVehicleEditDialog(vehicleData:VehicleData):void
      {
         var vehicleDefine:* = vehicleData.save.getDefine();
         var currentLevel:int = vehicleData.save.getTrueLevel();
         var maxLevel:int = vehicleDefine.getMaxLevel ? vehicleDefine.getMaxLevel() : 99;

         var editText:String = "编辑载具: " + vehicleDefine.cnName + "\n";
         editText += "当前等级: " + currentLevel + "/" + maxLevel + "\n\n";
         editText += "请选择操作:\n";
         editText += "1. 设置等级 (输入数字)\n";
         editText += "2. 满级 (输入 max)\n";
         editText += "3. 重置 (输入 reset)";

         Gaming.uiGroup.alertBox.textInput.showTextInput(editText, currentLevel.toString(),
            function(inputValue:String):void { processVehicleEdit(vehicleData, inputValue); },
            "yesAndNo", 999);
      }

      /**
       * 处理载具编辑
       */
      private static function processVehicleEdit(vehicleData:VehicleData, inputValue:String):void
      {
         try
         {
            var vehicleDefine:* = vehicleData.save.getDefine();
            var maxLevel:int = vehicleDefine.getMaxLevel ? vehicleDefine.getMaxLevel() : 99;

            if(inputValue.toLowerCase() == "max")
            {
               vehicleData.save.lv = maxLevel;
               Gaming.uiGroup.alertBox.showSuccess("✅ 载具 " + vehicleDefine.cnName + " 已设置为满级 (" + maxLevel + ")");
            }
            else if(inputValue.toLowerCase() == "reset")
            {
               vehicleData.save.lv = 1;
               Gaming.uiGroup.alertBox.showSuccess("✅ 载具 " + vehicleDefine.cnName + " 已重置为1级");
            }
            else
            {
               var newLevel:int = parseInt(inputValue);
               if(isNaN(newLevel) || newLevel < 1)
               {
                  Gaming.uiGroup.alertBox.showError("❌ 请输入有效的等级数字 (1-" + maxLevel + ")");
                  return;
               }

               if(newLevel > maxLevel) newLevel = maxLevel;
               vehicleData.save.lv = newLevel;
               Gaming.uiGroup.alertBox.showSuccess("✅ 载具 " + vehicleDefine.cnName + " 等级已设置为 " + newLevel);
            }

            refreshAllUI();
            hideEditButton();
         }
         catch(e:Error)
         {
            Gaming.uiGroup.alertBox.showError("❌ 编辑载具失败: " + e.message);
         }
      }
      
      /**
       * 显示技能编辑对话框
       */
      private static function showSkillEditDialog(skillData:HeroSkillData):void
      {
         var skillDefine:* = skillData.save.getDefine();
         var currentLevel:int = skillData.save.lv;
         var maxLevel:int = skillDefine.getMaxLevel();
         
         var editText:String = "编辑技能: " + skillDefine.cnName + "\n";
         editText += "当前等级: " + currentLevel + "/" + maxLevel + "\n\n";
         editText += "请选择操作:\n";
         editText += "1. 设置等级 (输入数字)\n";
         editText += "2. 满级 (输入 max)\n";
         editText += "3. 重置 (输入 reset)";
         
         Gaming.uiGroup.alertBox.textInput.showTextInput(editText, currentLevel.toString(), 
            function(inputValue:String):void { processSkillEdit(skillData, inputValue); }, 
            "yesAndNo", 999);
      }
      
      /**
       * 处理技能编辑
       */
      private static function processSkillEdit(skillData:HeroSkillData, inputValue:String):void
      {
         try
         {
            var skillDefine:* = skillData.save.getDefine();
            var maxLevel:int = skillDefine.getMaxLevel();
            
            if(inputValue.toLowerCase() == "max")
            {
               skillData.save.lv = maxLevel;
               Gaming.uiGroup.alertBox.showSuccess("✅ 技能 " + skillDefine.cnName + " 已设置为满级 (" + maxLevel + ")");
            }
            else if(inputValue.toLowerCase() == "reset")
            {
               skillData.save.lv = 1;
               Gaming.uiGroup.alertBox.showSuccess("✅ 技能 " + skillDefine.cnName + " 已重置为1级");
            }
            else
            {
               var newLevel:int = parseInt(inputValue);
               if(isNaN(newLevel) || newLevel < 1)
               {
                  Gaming.uiGroup.alertBox.showError("❌ 请输入有效的等级数字 (1-" + maxLevel + ")");
                  return;
               }
               
               if(newLevel > maxLevel) newLevel = maxLevel;
               skillData.save.lv = newLevel;
               Gaming.uiGroup.alertBox.showSuccess("✅ 技能 " + skillDefine.cnName + " 等级已设置为 " + newLevel);
            }
            
            refreshAllUI();
            hideEditButton();
         }
         catch(e:Error)
         {
            Gaming.uiGroup.alertBox.showError("❌ 编辑技能失败: " + e.message);
         }
      }
      
      /**
       * 显示魂卡编辑对话框
       */
      private static function showBossCardEditDialog(cardData:BossCardData):void
      {
         var cardDefine:* = cardData.getBodyDefine();
         var currentStar:int = cardData.getStar();
         var maxStar:int = 8; // 魂卡最大星级通常是8

         var editText:String = "编辑魂卡: " + cardDefine.cnName + "\n";
         editText += "当前星级: " + currentStar + "/" + maxStar + "\n\n";
         editText += "请选择操作:\n";
         editText += "1. 设置星级 (输入数字)\n";
         editText += "2. 满星 (输入 max)\n";
         editText += "3. 重置 (输入 reset)";

         Gaming.uiGroup.alertBox.textInput.showTextInput(editText, currentStar.toString(),
            function(inputValue:String):void { processBossCardEdit(cardData, inputValue); },
            "yesAndNo", 999);
      }

      /**
       * 处理魂卡编辑
       */
      private static function processBossCardEdit(cardData:BossCardData, inputValue:String):void
      {
         try
         {
            var cardDefine:* = cardData.getBodyDefine();
            var maxStar:int = 8;

            if(inputValue.toLowerCase() == "max")
            {
               cardData.save.star = maxStar;
               Gaming.uiGroup.alertBox.showSuccess("✅ 魂卡 " + cardDefine.cnName + " 已设置为满星 (" + maxStar + "星)");
            }
            else if(inputValue.toLowerCase() == "reset")
            {
               cardData.save.star = 1;
               Gaming.uiGroup.alertBox.showSuccess("✅ 魂卡 " + cardDefine.cnName + " 已重置为1星");
            }
            else
            {
               var newStar:int = parseInt(inputValue);
               if(isNaN(newStar) || newStar < 1)
               {
                  Gaming.uiGroup.alertBox.showError("❌ 请输入有效的星级数字 (1-" + maxStar + ")");
                  return;
               }

               if(newStar > maxStar) newStar = maxStar;
               cardData.save.star = newStar;
               Gaming.uiGroup.alertBox.showSuccess("✅ 魂卡 " + cardDefine.cnName + " 星级已设置为 " + newStar);
            }

            refreshAllUI();
            hideEditButton();
         }
         catch(e:Error)
         {
            Gaming.uiGroup.alertBox.showError("❌ 编辑魂卡失败: " + e.message);
         }
      }
      
      /**
       * 刷新所有相关UI（优化版本）
       */
      private static function refreshAllUI():void
      {
         try
         {
            // 延迟刷新以避免阻塞UI
            setTimeout(function():void {
               try {
                  // 只刷新当前可见的UI
                  if(Gaming.uiGroup.allBagUI && Gaming.uiGroup.allBagUI.visible)
                  {
                     Gaming.uiGroup.allBagUI.fleshAllBox();
                  }

                  if(Gaming.uiGroup.wearUI && Gaming.uiGroup.wearUI.visible)
                  {
                     Gaming.uiGroup.wearUI.fleshData();
                  }

                  if(Gaming.uiGroup.skillUI && Gaming.uiGroup.skillUI.visible && Gaming.uiGroup.skillUI.wearBox)
                  {
                     Gaming.uiGroup.skillUI.wearBox.fleshData();
                  }
               }
               catch(delayedRefreshError:Error) {
                  // 延迟刷新失败不影响主要功能
               }
            }, 100); // 100ms延迟
         }
         catch(refreshError:Error)
         {
            // 刷新失败不影响主要功能
         }
      }
   }
}

package dataAll.arms.define
{
   import com.sounto.utils.ClassProperty;
   
   public class ArmsChargerDefine
   {
      public static var pro_arr:Array = [];
      
      protected var V:Number = Math.random() / 5 + 0.01;
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      private var _baseCharger:Number = 0;
      
      public var joinProperty:int = 0;
      
      public var capacityMul:Number = 1;
      
      public var chargerMul:Number = 1;
      
      public var hurtMul:Number = 1;
      
      public var decomposeArr:Array = [];
      
      public var mustPartArr:Array = [];
      
      public var strengthenMustAdd:int = 0;
      
      public function ArmsChargerDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         ClassProperty.inData_byXML(this,xml0,pro_arr);
      }
      
      public function set baseCharger(v0:Number) : void
      {
         this._baseCharger = v0 / this.V;
      }
      
      public function get baseCharger() : Number
      {
         return Math.round(this._baseCharger * this.V);
      }
   }
}


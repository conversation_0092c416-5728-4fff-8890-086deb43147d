package dataAll.level.define.endless
{
   import com.sounto.utils.ClassProperty;
   
   public class EndlessGradeDefine
   {
      public static var pro_arr:Array = [];
      
      public var n:int = 0;
      
      public var norSkill:int = 0;
      
      public var bossSkill:int = 0;
      
      public var doublePro:Number = 0;
      
      public function EndlessGradeDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function getLevelName() : String
      {
         if(Math.random() < this.doublePro)
         {
            if(this.n >= 16)
            {
               return "endless3";
            }
            return "endless2";
         }
         return "endless";
      }
   }
}


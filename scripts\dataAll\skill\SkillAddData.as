package dataAll.skill
{
   import dataAll.equip.creator.EquipSkillAddCtreator;
   import dataAll.skill.define.HeroSkillDefine;
   
   public class SkillAddData
   {
      public static var ZERO:SkillAddData = new SkillAddData();
      
      public static var pro_arr:Array = [];
      
      public var baseLabel:String = "";
      
      public var cd:Number = 0;
      
      public var value:Number = 0;
      
      public var mul:Number = 0;
      
      public var duration:Number = 0;
      
      public var range:Number = 0;
      
      public var minTriggerT:Number = 0;
      
      public function SkillAddData()
      {
         super();
      }
      
      public function inData(baseLabel0:String, pro0:String, v0:Number) : void
      {
         this.baseLabel = baseLabel0;
         this[pro0] = v0;
      }
      
      public function haveDataB() : Boolean
      {
         return this.baseLabel != "";
      }
      
      public function getGatherTip() : String
      {
         var d0:HeroSkillDefine = null;
         var obj0:Object = null;
         if(this.haveDataB())
         {
            d0 = Gaming.defineGroup.skill.getOriginalHeroDefine(this.baseLabel);
            obj0 = {};
            obj0[this.baseLabel] = this[d0.addD.pro];
            return EquipSkillAddCtreator.getOnePro(this.baseLabel,this[d0.addD.pro],false);
         }
         return "";
      }
   }
}


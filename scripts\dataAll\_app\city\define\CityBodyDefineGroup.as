package dataAll._app.city.define
{
   import dataAll._base.IO_NormalDefine;
   import dataAll._base.NormalDefineGroup;
   
   public class CityBodyDefineGroup extends NormalDefineGroup
   {
      public function CityBodyDefineGroup()
      {
         super();
         defineClass = CityBodyDefine;
      }
      
      override protected function getNewDefine(n0:int, xml0:XML, fatherXml0:XML) : IO_NormalDefine
      {
         var type0:String = xml0["@type"];
         if(type0 == "")
         {
            type0 = fatherXml0["@type"];
         }
         if(type0 == CityBodyType.house)
         {
            return new CityHouseDefine();
         }
         return new CityBodyDefine();
      }
      
      public function getDefine(name0:String) : CityBodyDefine
      {
         return obj[name0];
      }
      
      public function getArrByType(type0:String) : Array
      {
         var d0:CityBodyDefine = null;
         var arr0:Array = [];
         for each(d0 in arr)
         {
            if(d0.type == type0)
            {
               arr0.push(d0);
            }
         }
         return arr0;
      }
   }
}


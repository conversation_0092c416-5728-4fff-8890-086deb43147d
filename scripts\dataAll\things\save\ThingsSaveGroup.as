package dataAll.things.save
{
   import com.sounto.utils.ClassProperty;
   import dataAll._app.parts.define.PartsType;
   import dataAll.items.save.ItemsSaveGroup;
   import dataAll.things.define.ThingsDefine;
   
   public class ThingsSaveGroup extends ItemsSaveGroup
   {
      public static var pro_arr:Array = null;
      
      public static var mePro_arr:Array = [];
      
      public function ThingsSaveGroup()
      {
         super();
      }
      
      public static function setMergeSaveArrBySame(arr0:Array) : Array
      {
         var s0:ThingsSave = null;
         var d0:ThingsDefine = null;
         var name0:String = null;
         var baseS0:ThingsSave = null;
         var obj0:Object = {};
         var newArr0:Array = [];
         var changeB0:Boolean = false;
         for each(s0 in arr0)
         {
            d0 = s0.getDefine();
            name0 = d0.name;
            if(obj0.hasOwnProperty(name0) == false || d0.noOverlayB)
            {
               obj0[name0] = s0;
               newArr0.push(s0);
            }
            else
            {
               baseS0 = obj0[name0];
               baseS0.overNum(s0.nowNum);
               changeB0 = true;
            }
         }
         if(changeB0)
         {
            return newArr0;
         }
         return arr0;
      }
      
      public static function getMustSiteBySaveArr(arr0:Array) : int
      {
         var s0:ThingsSave = null;
         var d0:ThingsDefine = null;
         var name0:String = null;
         var saveObj0:Object = {};
         var num0:int = 0;
         for each(s0 in arr0)
         {
            d0 = s0.getDefine();
            name0 = d0.name;
            if(saveObj0.hasOwnProperty(name0) == false || d0.noOverlayB)
            {
               saveObj0[name0] = s0;
               num0++;
            }
         }
         return num0;
      }
      
      public function initSaveInArmsParts() : void
      {
         gripMaxNum = PartsType.getWearLen();
         unlockTo(PartsType.getWearUnlockLen() - 1);
         var s0:ThingsSave = arr[0];
         if(Boolean(s0))
         {
            if(s0.getBaseLabel() == "" && arr.length == 1)
            {
               arr.length = 0;
            }
         }
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObjAndClass(obj0,ThingsSave);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
      }
      
      public function mergeAllSameName() : void
      {
         arr = setMergeSaveArrBySame(arr);
      }
      
      public function getSaveArrClone() : Array
      {
         var s0:ThingsSave = null;
         var new0:ThingsSave = null;
         var newArr0:Array = [];
         for each(s0 in arr)
         {
            new0 = s0.clone();
            newArr0.push(new0);
         }
         return newArr0;
      }
      
      public function clone() : ThingsSaveGroup
      {
         var dg0:ThingsSaveGroup = new ThingsSaveGroup();
         dg0.inData_byObj(this);
         return dg0;
      }
   }
}


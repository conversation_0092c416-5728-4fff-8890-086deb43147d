package dataAll._app.parts.define
{
   import com.sounto.utils.ClassProperty;
   import dataAll.equip.define.EquipColor;
   
   public class PartsPropertyDefine
   {
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var effectName:String = "";
      
      public var effectCnName:String = "";
      
      public var rangeArr:Array = [];
      
      public function PartsPropertyDefine()
      {
         super();
      }
      
      public static function getMulByLevel(armsLv0:Number, proLv0:Number, proName0:String) : Number
      {
         var armsDps0:Number = NaN;
         var partsDps0:Number = NaN;
         var pro0:Number = NaN;
         if(proLv0 > armsLv0)
         {
            proLv0 = armsLv0;
         }
         var v0:Number = 1;
         if(proName0 != PartsType.bullet)
         {
            armsDps0 = Gaming.defineGroup.normal.getArmsDps(armsLv0);
            partsDps0 = Gaming.defineGroup.normal.getArmsDps(proLv0);
            pro0 = partsDps0 / armsDps0;
            v0 = 1 - (1 - pro0) * 0.4;
         }
         return v0;
      }
      
      public static function test() : void
      {
         for(var i:int = 10; i <= 50; i++)
         {
            trace(i + ":" + getMulByLevel(50,i,""));
         }
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var r_d0:PartsPropertyRangeDefine = null;
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         var range_xmlList0:XMLList = xml0.range;
         if(Boolean(range_xmlList0))
         {
            for(n in range_xmlList0)
            {
               r_d0 = new PartsPropertyRangeDefine();
               r_d0.inData_byXML(range_xmlList0[n]);
               this.rangeArr.push(r_d0);
            }
         }
      }
      
      public function getProValue(proLv0:int, armsLv0:int, armsColor0:String, proName0:String) : Number
      {
         var mul0:Number = getMulByLevel(armsLv0,proLv0,proName0);
         if(armsColor0 == EquipColor.BLUE)
         {
            mul0 *= 0.8;
         }
         else if(armsColor0 == "" || armsColor0 == EquipColor.WHITE || armsColor0 == EquipColor.GREEN)
         {
            mul0 *= 0.7;
         }
         var max0:Number = this.getMaxValueByLv(proLv0);
         return max0 * mul0;
      }
      
      private function getMaxValueByLv(lv0:int) : Number
      {
         var d2:PartsPropertyRangeDefine = null;
         var arr_len0:int = int(this.rangeArr.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            d2 = this.rangeArr[i];
            if(lv0 <= d2.maxLevel)
            {
               if(this.effectName == "dps")
               {
                  return Gaming.defineGroup.normal.getArmsDps(lv0) * d2.max;
               }
               return d2.max;
            }
         }
         INIT.showError("找不到PartsPropertyRangeDefine定义：lv" + lv0);
         return 0;
      }
   }
}


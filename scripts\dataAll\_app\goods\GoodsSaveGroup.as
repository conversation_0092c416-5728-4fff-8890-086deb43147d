package dataAll._app.goods
{
   import com.common.text.TextWay;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.goods.define.GoodsDefine;
   
   public class GoodsSaveGroup
   {
      public static var pro_arr:Array = [];
      
      public var todayBuyNumObj:Object = {};
      
      public var buyNumObj:Object = {};
      
      public var offset2025_7:Number = 0;
      
      public function GoodsSaveGroup()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.todayBuyNumObj = this.clearNoFindGoods(obj0["todayBuyNumObj"]);
         this.buyNumObj = this.clearNoFindGoods(obj0["buyNumObj"]);
      }
      
      public function newDayCtrl() : void
      {
         this.todayBuyNumObj = {};
      }
      
      private function clearNoFindGoods(obj0:Object) : Object
      {
         var n:* = undefined;
         var d0:GoodsDefine = null;
         var newObj0:Object = {};
         if(Boolean(obj0))
         {
            for(n in obj0)
            {
               d0 = Gaming.defineGroup.goods.getDefine(n);
               if(Boolean(d0))
               {
                  newObj0[n] = obj0[n];
               }
            }
         }
         return newObj0;
      }
      
      public function clearBuyObj(fatherArr0:Array) : void
      {
         this.buyNumObj = this.clearBuyObjOne(this.buyNumObj,fatherArr0);
         this.todayBuyNumObj = this.clearBuyObjOne(this.todayBuyNumObj,fatherArr0);
      }
      
      private function clearBuyObjOne(obj0:Object, fatherArr0:Array) : Object
      {
         var n:* = undefined;
         var d0:GoodsDefine = null;
         var new0:Object = {};
         var clearArr0:Array = [];
         for(n in obj0)
         {
            d0 = Gaming.defineGroup.goods.getDefine(n);
            if(Boolean(d0))
            {
               if(fatherArr0.indexOf(d0.father) == -1)
               {
                  new0[n] = obj0[n];
               }
               else
               {
                  clearArr0.push(n);
               }
            }
            else
            {
               clearArr0.push(n);
            }
         }
         return new0;
      }
      
      public function addDayBuyNum(name0:String, num0:int) : void
      {
         var v0:int = 0;
         var d0:GoodsDefine = Gaming.defineGroup.goods.getDefine(name0);
         if(d0 is GoodsDefine)
         {
            if(d0.dayBuyLimitNum > 0)
            {
               v0 = this.getDayBuyNum(name0);
               v0 += num0;
               this.setDayBuyNum(name0,v0);
            }
         }
      }
      
      private function setDayBuyNum(name0:String, num0:int) : void
      {
         this.todayBuyNumObj[name0] = TextWay.toCode32(num0 + "");
      }
      
      public function getDayBuyNum(name0:String) : int
      {
         if(this.todayBuyNumObj.hasOwnProperty(name0))
         {
            return int(TextWay.getText32(this.todayBuyNumObj[name0]));
         }
         return 0;
      }
      
      public function addSpecialNum(name0:String, num0:int) : void
      {
         if(this.hasOwnProperty(name0))
         {
            this[name0] += num0;
         }
      }
      
      public function getSpecialNum(name0:String) : Number
      {
         if(this.hasOwnProperty(name0))
         {
            return this[name0];
         }
         return 0;
      }
      
      public function addAllBuyNum(name0:String, num0:int) : void
      {
         var v0:int = 0;
         this.addSpecialNum(name0,num0);
         var d0:GoodsDefine = Gaming.defineGroup.goods.getDefine(name0);
         if(d0 is GoodsDefine)
         {
            if(d0.buyLimitNum > 0)
            {
               v0 = this.getAllBuyNum(name0);
               v0 += num0;
               this.setAllBuyNum(name0,v0);
            }
         }
      }
      
      private function setAllBuyNum(name0:String, num0:int) : void
      {
         this.buyNumObj[name0] = TextWay.toCode32(num0 + "");
      }
      
      public function getAllBuyNum(name0:String) : int
      {
         if(this.buyNumObj.hasOwnProperty(name0))
         {
            return int(TextWay.getText32(this.buyNumObj[name0]));
         }
         return 0;
      }
   }
}


package dataAll._app.achieve.define
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll.level.LevelDiffGetting;
   
   public class AchieveConditionDefine
   {
      public static var pro_arr:Array = [];
      
      public var trigger:String = "openUI";
      
      public var type:String = "";
      
      public var name:String = "";
      
      public var pro:String = "";
      
      public var level:Number = 0;
      
      public var value:Number = 0;
      
      public var mul:Number = 0;
      
      public var string:String = "";
      
      public var string2:String = "";
      
      public var infoB:Boolean = false;
      
      public var infoUnit:String = "";
      
      public var model:String = "";
      
      public var minDiff:int = 0;
      
      public var maxDiff:int = 3;
      
      public var minLevelLv:int = 0;
      
      public var maxLevelLv:int = 999;
      
      public var mapName:String = "";
      
      public var enemy:String = "";
      
      public function AchieveConditionDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         var diffStr0:String = xml0.@diff;
         if(diffStr0 != "")
         {
            this.minDiff = int(diffStr0.split("~")[0]);
            this.maxDiff = int(diffStr0.split("~")[1]);
         }
         var levelLvStr0:String = xml0.@levelLv;
         if(levelLvStr0 != "")
         {
            this.minLevelLv = int(levelLvStr0.split("~")[0]);
            this.maxLevelLv = int(levelLvStr0.split("~")[1]);
         }
      }
      
      public function getLevelString() : String
      {
         var levelStr0:String = "";
         if(this.minLevelLv == this.maxLevelLv)
         {
            levelStr0 = this.maxLevelLv + "级";
         }
         if(this.minLevelLv < this.maxLevelLv)
         {
            if(this.minLevelLv <= 0 && this.maxLevelLv >= 999)
            {
               levelStr0 = "";
            }
            else
            {
               levelStr0 = this.minLevelLv + "~" + this.maxLevelLv + "级";
            }
         }
         var mapStr0:String = "";
         if(this.mapName != "")
         {
            mapStr0 = Gaming.defineGroup.worldMap.getDefine(this.mapName).cnName;
         }
         if(this.enemy != "")
         {
            mapStr0 = this.enemy;
         }
         var diffStr0:String = "";
         if(this.minDiff == this.maxDiff)
         {
            diffStr0 = LevelDiffGetting.getCnName(this.maxDiff,MapMode.REGULAR);
         }
         if(diffStr0 != "")
         {
            diffStr0 = "(" + diffStr0 + ")";
         }
         var str0:String = levelStr0 + mapStr0 + diffStr0;
         return ComMethod.color(String(str0),"#00FFFF");
      }
      
      public function isRecordB() : Boolean
      {
         return this.pro != "" && this.infoB;
      }
      
      public function isLoadingB() : Boolean
      {
         return this.pro != "" && !this.infoB;
      }
      
      public function getValueString(v0:Number) : String
      {
         if(this.infoUnit == "%")
         {
            return TextWay.numberToPer(v0);
         }
         return v0.toFixed() + this.infoUnit;
      }
   }
}


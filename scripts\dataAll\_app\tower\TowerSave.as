package dataAll._app.tower
{
   import com.common.data.Base64;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.cf.NumberEncodeObj;
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class TowerSave
   {
      public static var pro_arr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var ws:Boolean = false;
      
      public var ng:String = "";
      
      public var dO:NumberEncodeObj = new NumberEncodeObj();
      
      public var gO:NumberEncodeObj = new NumberEncodeObj();
      
      public var p0:NumberEncodeObj = new NumberEncodeObj();
      
      public var p1:NumberEncodeObj = new NumberEncodeObj();
      
      public var p2:NumberEncodeObj = new NumberEncodeObj();
      
      public var pNow:String = "0";
      
      public function TowerSave()
      {
         super();
      }
      
      public function get blv() : Number
      {
         return this.CF.getAttribute("blv");
      }
      
      public function set blv(v0:Number) : void
      {
         this.CF.setAttribute("blv",v0);
      }
      
      public function get unendLv() : Number
      {
         return this.CF.getAttribute("unendLv");
      }
      
      public function set unendLv(v0:Number) : void
      {
         this.CF.setAttribute("unendLv",v0);
      }
      
      public function get uP() : Number
      {
         return this.CF.getAttribute("uP");
      }
      
      public function set uP(v0:Number) : void
      {
         this.CF.setAttribute("uP",v0);
      }
      
      public function get uAll() : Number
      {
         return this.CF.getAttribute("uAll");
      }
      
      public function set uAll(v0:Number) : void
      {
         this.CF.setAttribute("uAll",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         var allGift0:GiftAddDefineGroup = null;
         var lv0:Number = NaN;
         var i:int = 0;
         var name0:String = null;
         var d0:TowerDefine = null;
         var g0:GiftAddDefineGroup = null;
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         if(obj0.hasOwnProperty("lv"))
         {
            if(this.blv == 0 && this.ng == "")
            {
               allGift0 = new GiftAddDefineGroup();
               lv0 = Number(obj0["lv"]);
               this.blv = lv0;
               for(i = 1; i <= lv0; i++)
               {
                  name0 = i + "";
                  this.winEvent(name0,TowerDefine.MAX_DIFF);
                  if(lv0 >= i + 0.5)
                  {
                     this.giftEvent(name0,TowerDefine.MAX_DIFF);
                     d0 = Gaming.defineGroup.tower.getDefine(i + "");
                     g0 = d0.getNoMaxGift();
                     allGift0.merge(g0);
                  }
               }
               if(allGift0.haveDataB())
               {
                  this.ng = Base64.encodeObject(allGift0);
               }
            }
         }
      }
      
      public function getNoGiftAndClear() : GiftAddDefineGroup
      {
         var obj0:Object = null;
         var g0:GiftAddDefineGroup = null;
         if(this.ng != "")
         {
            obj0 = Base64.decodeObject(this.ng);
            g0 = new GiftAddDefineGroup();
            g0.inData_byObj(obj0);
            this.ng = "";
            return g0;
         }
         return null;
      }
      
      public function winEvent(name0:String, diff0:int) : void
      {
         var now0:int = this.getWinDiff(name0);
         if(now0 < diff0)
         {
            this.dO.setAttribute(name0,diff0);
         }
      }
      
      public function getWinDiff(name0:String) : int
      {
         return this.dO.getAttribute(name0) as int;
      }
      
      public function giftEvent(name0:String, diff0:int) : void
      {
         var now0:int = this.getGiftDiff(name0);
         if(now0 < diff0)
         {
            this.gO.setAttribute(name0,diff0);
         }
      }
      
      public function getGiftDiff(name0:String) : int
      {
         return this.gO.getAttribute(name0) as int;
      }
      
      public function getNow() : NumberEncodeObj
      {
         return this["p" + this.pNow];
      }
   }
}


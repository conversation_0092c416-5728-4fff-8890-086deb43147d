package dataAll._app.parts
{
   import dataAll._app.parts.define.PartsConst;
   import dataAll.things.ThingsData;
   import dataAll.things.save.ThingsSave;
   
   public class PartsComposeCtrl
   {
      public function PartsComposeCtrl()
      {
         super();
      }
      
      public static function compose(dg0:PartsDataGroup, minLv0:int = 0, maxLv0:int = 999) : Number
      {
         var da0:ThingsData = null;
         var mLv0:int = 0;
         var s0:ThingsSave = null;
         var lv0:int = 0;
         var coin0:Number = 0;
         var nowMaxLv0:int = PartsConst.getMaxPartsLevel();
         if(maxLv0 > nowMaxLv0 - PartsConst.minLv)
         {
            maxLv0 = nowMaxLv0 - PartsConst.minLv;
         }
         var lvArr0:Array = [];
         for each(da0 in dg0.dataArr)
         {
            s0 = da0.save;
            lv0 = s0.getTrueLevel();
            if(lv0 <= maxLv0 && lv0 >= minLv0)
            {
               if(lvArr0.indexOf(lv0) == -1)
               {
                  lvArr0.push(lv0);
               }
            }
         }
         lvArr0.sort(sortNumArray);
         for each(mLv0 in lvArr0)
         {
            coin0 += composeLevel(dg0,mLv0);
         }
         return coin0;
      }
      
      private static function sortNumArray(v1:Number, v2:Number) : int
      {
         if(v1 < v2)
         {
            return -1;
         }
         if(v1 > v2)
         {
            return 1;
         }
         return 0;
      }
      
      private static function composeLevel(dg0:PartsDataGroup, mLv0:int) : Number
      {
         var coin0:Number = 0;
         var oneCoin0:Number = 0;
         do
         {
            oneCoin0 = composeOneLevel(dg0,mLv0);
            coin0 += oneCoin0;
         }
         while(oneCoin0 > 0);
         
         return coin0;
      }
      
      private static function composeOneLevel(dg0:PartsDataGroup, mLv0:int) : Number
      {
         var da0:ThingsData = null;
         var s0:ThingsSave = null;
         var lv0:int = 0;
         var num0:int = 0;
         var oneMustNum0:int = 0;
         var cNum0:int = 0;
         var nextName0:String = null;
         var dataArr0:Array = dg0.dataArr;
         for each(da0 in dataArr0)
         {
            s0 = da0.save;
            if(s0.isPartsNormalB())
            {
               lv0 = s0.getTrueLevel();
               if(lv0 == mLv0)
               {
                  num0 = s0.nowNum;
                  oneMustNum0 = PartsMethod.getComposeMustNum(s0);
                  cNum0 = int(num0 / oneMustNum0);
                  if(cNum0 > 0)
                  {
                     nextName0 = s0.getPartsNextName();
                     dg0.useThings(s0.name,cNum0 * oneMustNum0,false,false);
                     dg0.addDataByName(nextName0,cNum0);
                     return PartsMethod.getComposeMustCoin(da0,cNum0);
                  }
               }
            }
         }
         return 0;
      }
   }
}


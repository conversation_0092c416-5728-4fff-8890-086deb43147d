package UI.bag.allBag
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.bag.ItemsEditCtrl;
   import UI.bag.ItemsEditManager;
   import UI.base.AppNormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import dataAll._player.PlayerData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.equip.EquipDataFilter;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.define.EquipType;
   import dataAll.items.IO_ItemsData;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.utils.setTimeout;
   
   public class AllBagUI extends AppNormalUI
   {
      private var labelBox:LabelBox;
      
      private var armsBox:ItemsGripBox;
      
      private var armsBagBox:ItemsGripBox;
      
      private var equipBox:ItemsGripBox;
      
      private var equipBagBox:ItemsGripBox;
      
      private var thingsBagBox:ItemsGripBox;
      
      private var nowBox:ItemsGripBox = null;
      
      private var boxLabelArr:Array;
      
      private var boxArr:Array;
      
      private var sortBtn:NormalBtn;
      
      private var sortBtn2:NormalBtn;
      
      private var closeBtn:SimpleButton;
      
      private var gripTag:Sprite = null;
      
      private var sortBtnSp:MovieClip = null;
      
      private var sortBtnSp2:MovieClip = null;
      
      private var infoTxt:TextField;
      
      private var titleTxt:TextField;
      
      private var armsPageTag:Sprite = null;
      
      private var labelTag:Sprite = null;
      
      private var nowData:IO_ItemsData;

      // 性能优化配置
      private var enableTips:Boolean = false;
      private var enableEdit:Boolean = false;
      private var maxItemsPerPage:int = 50;

      public function AllBagUI()
      {
         var label0:* = null;
         var box0:ItemsGripBox = null;
         this.labelBox = new LabelBox();
         this.armsBox = new ItemsGripBox();
         this.armsBagBox = new ItemsGripBox();
         this.equipBox = new ItemsGripBox();
         this.equipBagBox = new ItemsGripBox();
         this.thingsBagBox = new ItemsGripBox();
         this.boxLabelArr = ["arms","armsBag","equip","equipBag","thingsBag"];
         this.boxArr = [];
         this.sortBtn = new NormalBtn();
         this.sortBtn2 = new NormalBtn();
         super();
         for each(label0 in this.boxLabelArr)
         {
            box0 = this[label0 + "Box"];
            this.boxArr.push(box0);
            box0.label = label0;
         }
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var box0:ItemsGripBox = null;
         elementNameArr = ["infoTxt","closeBtn","sortBtnSp2","sortBtnSp","labelTag","gripTag","armsPageTag","titleTxt"];
         super.setImg(img0);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         NormalUICtrl.setTag(this.labelBox,this.labelTag);
         NormalUICtrl.setTag(this.armsBox,this.gripTag);
         NormalUICtrl.setTag(this.armsBagBox,this.gripTag);
         NormalUICtrl.setTag(this.equipBox,this.gripTag);
         NormalUICtrl.setTag(this.equipBagBox,this.gripTag);
         NormalUICtrl.setTag(this.thingsBagBox,this.gripTag);
         this.labelBox.arg.init(4,1,-5,0);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         addChild(this.sortBtn);
         this.sortBtn.setImg(this.sortBtnSp);
         addChild(this.sortBtn2);
         this.sortBtn2.setImg(this.sortBtnSp2);
         this.sortBtn.addEventListener(MouseEvent.CLICK,this.sortBagClick);
         this.sortBtn.visible = false;
         this.sortBtn2.addEventListener(MouseEvent.CLICK,this.sortBagClick2);
         this.sortBtn2.visible = false;
         addChild(this.labelBox);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         for each(box0 in this.boxArr)
         {
            addChild(box0);
            NormalUICtrl.setTag(box0,this.gripTag);
            box0.pageBox.setToNormalBtn();
            box0.pageBox.setXY_bySp(this.armsPageTag,box0);
            if(box0 == this.armsBox || box0 == this.armsBagBox)
            {
               box0.imgType = "armsGrip";
               box0.arg.init(2,4,3,5);
            }
            else
            {
               box0.imgType = "equipGrip";
               box0.arg.init(6,5,2,3);
            }
            box0.evt.setWantEvent(true,true,true,true,true,true);
            box0.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
            // 智能延迟加载：在游戏完全初始化后再添加高级功能
            ItemsGripTipCtrl.addEvent_byItemsGripBox(box0);
            // 编辑功能将在游戏加载完成后自动启用
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function setTitleStr(str0:String) : void
      {
         this.titleTxt.text = str0;
      }
      
      public function outLoginEvent() : void
      {
         this.nowData = null;
      }

      /**
       * 启用高级功能（编辑功能）
       * 在游戏完全加载后调用以避免卡顿
       */
      public function enableAdvancedFeatures() : void
      {
         var box0:ItemsGripBox = null;
         for each(box0 in this.boxArr)
         {
            // 暂时禁用编辑功能注册，避免加载时卡顿
            // ItemsEditManager.registerBox(box0);
         }
      }

      /**
       * 设置性能配置
       */
      public function setPerformanceConfig(enableTips:Boolean = false, enableEdit:Boolean = false, maxItems:int = 50) : void
      {
         this.enableTips = enableTips;
         this.enableEdit = enableEdit;
         this.maxItemsPerPage = maxItems;
      }

      /**
       * 禁用高级功能以提高性能
       */
      public function disableAdvancedFeatures() : void
      {
         var box0:ItemsGripBox = null;
         for each(box0 in this.boxArr)
         {
            // 移除事件监听器
            ItemsEditCtrl.removeEvent_byItemsGripBox(box0);
            // 注意：ItemsGripTipCtrl 没有提供移除方法，需要手动处理
         }
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showAndLabel(label0:String) : void
      {
         super.show();
         Gaming.PG.changeEquip();
         this.showBox(label0);

         // 延迟启用高级功能，避免初始加载卡顿
         setTimeout(this.enableAdvancedFeatures, 1000);
      }
      
      private function fleshLabelByLabel(label0:String) : void
      {
         var nameArr0:Array = [];
         var cnNameArr0:Array = [];
         if(label0.indexOf("armsEvo") >= 0)
         {
            nameArr0 = ["armsEvo","armsEvoBag"];
            cnNameArr0 = ["人物武器","背包武器"];
         }
         else if(label0.indexOf("arms") >= 0)
         {
            nameArr0 = ["arms","armsBag"];
            cnNameArr0 = ["人物武器","背包武器"];
         }
         else if(label0.indexOf("equipEvo") >= 0)
         {
            nameArr0 = ["equipEvo","equipEvoBag"];
            cnNameArr0 = ["人物装备","背包装备"];
         }
         else if(label0.indexOf("equip") >= 0)
         {
            nameArr0 = ["equip","equipBag"];
            cnNameArr0 = ["人物装备","背包装备"];
         }
         else if(label0.indexOf("device") >= 0)
         {
            nameArr0 = ["device","deviceBag"];
            cnNameArr0 = ["人物装置","背包装置"];
         }
         else if(label0.indexOf("weapon") >= 0)
         {
            nameArr0 = ["weapon","weaponBag"];
            cnNameArr0 = ["人物副手","背包副手"];
         }
         else if(label0.indexOf("shield") >= 0)
         {
            nameArr0 = ["shield","shieldBag"];
            cnNameArr0 = ["人物护盾","背包护盾"];
         }
         else if(label0.indexOf("jewelry") >= 0)
         {
            nameArr0 = ["jewelry","jewelryBag"];
            cnNameArr0 = ["人物饰品","背包饰品"];
         }
         else if(label0.indexOf("blackChip") >= 0)
         {
            nameArr0 = ["blackChipBag"];
            cnNameArr0 = ["黑色碎片"];
         }
         this.labelBox.inData("longLabelBtn",nameArr0,cnNameArr0);
      }
      
      public function fleshAllBox() : void
      {
         this.showBox(this.labelBox.nowLabel);
      }
      
      private function showBox(label0:String, fleshB0:Boolean = true) : void
      {
         var box0:ItemsGripBox = null;
         if(label0 == "")
         {
            label0 = "arms";
         }
         this.fleshLabelByLabel(label0);
         this.labelBox.setChoose(label0);
         var pd0:PlayerData = Gaming.PG.da;
         var dataArr0:Array = [];

         // 优化：添加性能检查，避免重复加载相同数据
         if(this.nowBox && this.nowBox.label == label0 && this.nowBox.gripArr.length > 0)
         {
            // 如果当前显示的就是要求的标签且已有数据，直接显示
            for each(box0 in this.boxArr)
            {
               box0.visible = this.nowBox == box0;
            }
            return;
         }
         if(label0 == "arms")
         {
            this.setTitleStr("请选择武器");
            this.nowBox = this.armsBox;
            // 优化：减少数据处理量
            var armsData:Array = pd0.getArmsDataArr(false,true,true);
            if(armsData.length > this.maxItemsPerPage) {
               // 如果数据量过大，只显示配置的最大数量
               armsData = armsData.slice(0, this.maxItemsPerPage);
            }
            this.nowBox.inData_byArr(armsData,"inData_arms");
         }
         else if(label0 == "armsBag")
         {
            this.setTitleStr("请选择武器");
            this.nowBox = this.armsBagBox;
            this.nowBox.inData_byDataGroup(pd0.armsBag);
         }
         else if(label0 == "armsEvo")
         {
            this.setTitleStr("选择要进化武器");
            this.nowBox = this.armsBox;
            this.nowBox.inData_byArr(ArmsDataGroup.filterCanEvoDataArr(pd0.getArmsDataArr(false,true,true)),"inData_arms");
         }
         else if(label0 == "armsEvoBag")
         {
            this.setTitleStr("选择要进化武器");
            this.nowBox = this.armsBagBox;
            this.nowBox.inData_byArr(ArmsDataGroup.filterCanEvoDataArr(pd0.armsBag.dataArr),"inData_arms");
         }
         else if(label0 == "equip")
         {
            this.setTitleStr("请选择装备");
            this.nowBox = this.equipBox;
            // 优化：减少装备数据处理量
            var equipData:Array = pd0.getEquipDataArr(false,true,true,true);
            if(equipData.length > this.maxItemsPerPage) {
               // 如果数据量过大，只显示配置的最大数量
               equipData = equipData.slice(0, this.maxItemsPerPage);
            }
            this.nowBox.inData_byArr(equipData,"inData_equip");
         }
         else if(label0 == "equipBag")
         {
            this.setTitleStr("请选择装备");
            this.nowBox = this.equipBagBox;
            var equipBagData:Array = pd0.equipBag.getNormalDataArr();
            if(equipBagData.length > this.maxItemsPerPage) {
               // 如果数据量过大，只显示配置的最大数量
               equipBagData = equipBagData.slice(0, this.maxItemsPerPage);
            }
            this.nowBox.inData_byArr(equipBagData,"inData_equip");
         }
         else if(label0 == "equipEvo")
         {
            this.setTitleStr("选择要进化装备");
            this.nowBox = this.equipBox;
            this.nowBox.inData_byArr(EquipDataGroup.filterCanEvoDataArr(pd0.getEquipDataArr(false,true,true,true)),"inData_equip");
         }
         else if(label0 == "equipEvoBag")
         {
            this.setTitleStr("选择要进化装备");
            this.nowBox = this.equipBagBox;
            this.nowBox.inData_byArr(EquipDataGroup.filterCanEvoDataArr(pd0.equipBag.getNormalDataArr()),"inData_equip");
         }
         else if(label0 == "device")
         {
            this.setTitleStr("请选择装置");
            this.nowBox = this.equipBox;
            dataArr0 = pd0.getEquipDataArr(false,true,true);
            dataArr0 = EquipDataFilter.filter(dataArr0,"device");
            this.nowBox.inData_byArr(dataArr0,"inData_equip");
         }
         else if(label0 == "deviceBag")
         {
            this.nowBox = this.equipBox;
            dataArr0 = pd0.equipBag.dataArr;
            dataArr0 = EquipDataFilter.filter(dataArr0,"device");
            this.nowBox.inData_byArr(dataArr0,"inData_equip");
         }
         else if(label0 == "shield")
         {
            this.setTitleStr("请选择护盾");
            this.nowBox = this.equipBox;
            dataArr0 = pd0.getEquipDataArr(false,true,true);
            dataArr0 = EquipDataFilter.filter(dataArr0,EquipType.SHIELD);
            this.nowBox.inData_byArr(dataArr0,"inData_equip");
         }
         else if(label0 == "shieldBag")
         {
            this.nowBox = this.equipBox;
            dataArr0 = pd0.equipBag.dataArr;
            dataArr0 = EquipDataFilter.filter(dataArr0,EquipType.SHIELD);
            this.nowBox.inData_byArr(dataArr0,"inData_equip");
         }
         else if(label0 == "jewelry")
         {
            this.setTitleStr("请选择饰品");
            this.nowBox = this.equipBox;
            dataArr0 = pd0.getEquipDataArr(false,true,true);
            dataArr0 = EquipDataFilter.filter(dataArr0,EquipType.JEWELRY);
            this.nowBox.inData_byArr(dataArr0,"inData_equip");
         }
         else if(label0 == "jewelryBag")
         {
            this.nowBox = this.equipBox;
            dataArr0 = pd0.equipBag.dataArr;
            dataArr0 = EquipDataFilter.filter(dataArr0,EquipType.JEWELRY);
            this.nowBox.inData_byArr(dataArr0,"inData_equip");
         }
         else if(label0 == "weapon")
         {
            this.setTitleStr("请选择副手");
            this.nowBox = this.equipBox;
            dataArr0 = pd0.getEquipDataArr(false,true,true);
            dataArr0 = EquipDataFilter.filter(dataArr0,"weapon");
            this.nowBox.inData_byArr(dataArr0,"inData_equip");
         }
         else if(label0 == "weaponBag")
         {
            this.nowBox = this.equipBox;
            dataArr0 = pd0.equipBag.dataArr;
            dataArr0 = EquipDataFilter.filter(dataArr0,"weapon");
            this.nowBox.inData_byArr(dataArr0,"inData_equip");
         }
         else if(label0 == "blackChipBag")
         {
            this.nowBox = this.thingsBagBox;
            dataArr0 = pd0.thingsBag.getChipConverArr();
            this.nowBox.inData_byArr(dataArr0,"inData_things");
         }
         this.infoTxt.visible = this.nowBox.gripArr.length == 0;
         this.infoTxt.text = "无数据";
         this.setChooseItemsData(this.nowData);
         for each(box0 in this.boxArr)
         {
            box0.visible = this.nowBox == box0;
         }
      }
      
      public function chooseItemsData_out(da0:IO_ItemsData, defaultLabel0:String) : void
      {
         var type0:String = null;
         var place0:String = null;
         var label0:String = null;
         this.nowData = da0;
         if(da0 is IO_ItemsData)
         {
            type0 = defaultLabel0;
            place0 = da0.getPlaceType();
            label0 = type0;
            if(type0.indexOf("Bag") == -1)
            {
               label0 += place0 == "bag" ? "Bag" : "";
            }
            this.showAndLabel(label0);
         }
         else
         {
            this.showAndLabel(defaultLabel0);
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var da0:IO_ItemsData = e.childData as IO_ItemsData;
         if(da0 is IO_ItemsData)
         {
            this.setChooseItemsData(da0);
            Gaming.uiGroup.forgingUI.itemsGripClick(da0);
         }
      }
      
      private function setChooseItemsData(da0:IO_ItemsData) : void
      {
         var box0:ItemsGripBox = null;
         var grip0:ItemsGrid = null;
         this.nowData = da0;
         var chooseB0:Boolean = !(da0 is IO_ItemsData);
         for each(box0 in this.boxArr)
         {
            if(!chooseB0)
            {
               grip0 = box0.findGripByData(da0);
               if(grip0 is ItemsGrid)
               {
                  chooseB0 = true;
                  box0.setChoose_byIndex(grip0.index);
               }
               else
               {
                  box0.setChoose_byIndex(-1);
               }
            }
            else
            {
               box0.setChoose_byIndex(-1);
            }
         }
      }
      
      private function findDataInBox(da0:IO_ItemsData) : ItemsGripBox
      {
         var box0:ItemsGripBox = null;
         for each(box0 in this.boxArr)
         {
            if(box0.findGripByData(da0) is NormalBtn)
            {
               return box0;
            }
         }
         return null;
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
         Gaming.uiGroup.forgingUI.hide();
      }
      
      private function sortBagClick(e:MouseEvent) : void
      {
         if(Boolean(this.nowBox))
         {
            this.nowBox.fatherData.sort(Gaming.PG.getOpposingDataGroup(this.nowBox.fatherData));
            this.fleshAllBox();
         }
      }
      
      private function sortBagClick2(e:MouseEvent) : void
      {
         if(Boolean(this.nowBox))
         {
            this.nowBox.fatherData.sort2(Gaming.PG.getOpposingDataGroup(this.nowBox.fatherData));
            this.fleshAllBox();
         }
      }
   }
}


package dataAll.arms.creator
{
   import com.sounto.image.DisplayMotionDefine;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.BmpMethod;
   import dataAll.arms.define.GunPart;
   import dataAll.arms.skin.ArmsSkinDefine;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.BlendMode;
   import flash.display.Sprite;
   import flash.filters.GlowFilter;
   import flash.geom.ColorTransform;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.utils.getTimer;
   
   public class GunImageCreator
   {
      public static var glowFilter:GlowFilter = new GlowFilter(0,0.7,1,1,255,1,true);
      
      public function GunImageCreator()
      {
         super();
      }
      
      public static function testGetImage() : void
      {
         var bmp0:BitmapData = null;
         var sp0:Bitmap = null;
         var tt0:Number = getTimer();
         for(var i:int = 0; i < 100000; i++)
         {
            bmp0 = Gaming.swfLoaderManager.getResourceFull("specialGun/yearDragonBmp") as BitmapData;
            sp0 = new Bitmap(bmp0,"auto",true);
            sp0.y = i * sp0.height;
            Gaming.ME.addChild(sp0);
         }
         INIT.TRACE("Bitmap100000次耗时：" + (getTimer() - tt0));
      }
      
      private static function testGetImage2() : void
      {
         var tt0:Number = getTimer();
         for(var i:int = 0; i < 1000; i++)
         {
            Gaming.gunImageManager.obj["texture$m32_specialGun$yearDragon_0_0_specialGun$bullet_0_0"] = null;
            Gaming.gunImageManager.addImage("texture$m32_specialGun$yearDragon_0_0_specialGun$bullet_0_0");
         }
         INIT.TRACE("生成武器图像耗时：" + (getTimer() - tt0));
      }
      
      public static function creatGunImage(sp_arr:Array, t_sp:Sprite, iconB:Boolean = false) : GunImage
      {
         var con:Sprite = null;
         var n:* = undefined;
         var bmp1:BitmapData = null;
         var rect1:Rectangle = null;
         var bitmap1:Bitmap = null;
         var sp0:* = undefined;
         var name0:String = null;
         var rect0:Rectangle = null;
         var cx0:Number = NaN;
         var cy0:Number = NaN;
         var coverB:Boolean = false;
         var c_bmp:BitmapData = null;
         var bitmap0:Bitmap = null;
         var icon_bmp:BitmapData = null;
         var part_list:Array = GunPart.PART_ARR;
         var img0:GunImage = new GunImage();
         var scale:Number = 1;
         if(!iconB)
         {
            scale = 0.5;
         }
         var t_bmp:BitmapData = ComMethod.getBmp(t_sp,true,scale);
         var t_rect:Rectangle = t_sp.getRect(t_sp);
         scaleRect(t_rect,scale);
         var body_sp:Sprite = sp_arr[part_list.indexOf(GunPart.body)];
         img0.inputPoint(body_sp,GunPart.body,scale);
         img0.setPoint(GunPart.body,new DisplayMotionDefine());
         con = new Sprite();
         var bullet_bitmap:Bitmap = null;
         var bullet_rect:Rectangle = null;
         for(n in part_list)
         {
            sp0 = sp_arr[n];
            name0 = part_list[n];
            if(sp0)
            {
               if(name0 != GunPart.body)
               {
                  img0.inputPoint(sp0,name0,scale);
               }
               rect0 = sp0.getBounds(sp0);
               scaleRect(rect0,scale);
               cx0 = -t_rect.x + rect0.x + img0.p_obj[name0].x;
               cy0 = -t_rect.y + rect0.y + img0.p_obj[name0].y;
               coverB = Boolean(sp0.getChildByName("cover_mc"));
               c_bmp = creatCover(t_bmp,sp0,rect0,cx0,cy0,coverB,scale);
               bitmap0 = new Bitmap(c_bmp,"auto",true);
               bitmap0.x = rect0.x + img0.p_obj[name0].x;
               bitmap0.y = rect0.y + img0.p_obj[name0].y;
               con.addChildAt(bitmap0,0);
               if(name0 == GunPart.bullet)
               {
                  bullet_bitmap = bitmap0;
                  bullet_rect = rect0;
               }
            }
         }
         rect1 = con.getRect(con);
         bullet_bitmap.visible = false;
         if(iconB)
         {
            scaleRect(rect1,0.5);
            scaleRect(bullet_rect,0.5);
            bmp1 = ComMethod.getBmp(con,true,0.5);
            bmp1.applyFilter(bmp1,bmp1.rect,new Point(),glowFilter);
            bullet_bitmap.visible = true;
            icon_bmp = ComMethod.getBmp(con,true,1);
            icon_bmp.applyFilter(icon_bmp,icon_bmp.rect,new Point(),glowFilter);
            img0.iconBmp = icon_bmp;
            img0.setPointScale(0.5);
            bullet_bitmap.scaleX = 0.5;
            bullet_bitmap.scaleY = 0.5;
         }
         else
         {
            bmp1 = ComMethod.getBmp(con,true,1);
         }
         bitmap1 = new Bitmap(bmp1,"auto",true);
         bitmap1.x = rect1.x;
         bitmap1.y = rect1.y;
         img0.gun.addChild(bitmap1);
         bullet_bitmap.x = bullet_rect.x;
         bullet_bitmap.y = bullet_rect.y;
         bullet_bitmap.visible = true;
         img0.bullet.addChild(bullet_bitmap);
         img0.bullet.x = img0.bullet_p.x;
         img0.bullet.y = img0.bullet_p.y;
         img0.moveByRightHand();
         return img0;
      }
      
      public static function creatOnlyBodyImage(body_sp:Sprite) : GunImage
      {
         var img0:GunImage = null;
         var gunBit0:Bitmap = null;
         var bmpScale0:Number = NaN;
         img0 = new GunImage();
         var bitmap0:Bitmap = BmpMethod.findBitmapInSp(body_sp);
         if(Boolean(bitmap0))
         {
            gunBit0 = new Bitmap(bitmap0.bitmapData,"auto",true);
            bmpScale0 = 1;
            if(bitmap0.width != gunBit0.width)
            {
               bmpScale0 = bitmap0.width / gunBit0.width;
            }
            gunBit0.scaleX = 0.5 * bmpScale0;
            gunBit0.scaleY = 0.5 * bmpScale0;
            gunBit0.x = bitmap0.x * 0.5;
            gunBit0.y = bitmap0.y * 0.5;
            img0.iconBmp = bitmap0.bitmapData;
            img0.gun.addChild(gunBit0);
         }
         img0.inputPoint(body_sp,GunPart.body,1);
         img0.setBulletHandToNormal();
         img0.setPointScale(0.5);
         img0.moveByRightHand();
         return img0;
      }
      
      private static function creatCover(t_bmp:BitmapData, p_sp:Sprite, p_rect:Rectangle, x0:Number, y0:Number, coverB:Boolean = true, scale:Number = 1) : BitmapData
      {
         var cover_mc:* = undefined;
         var cover_rect:Rectangle = null;
         var cover_bmp:BitmapData = null;
         var cover_p:Point = null;
         var p_bmp:BitmapData = ComMethod.getBmp(p_sp,false,scale);
         var c_bmp:BitmapData = null;
         if(coverB)
         {
            cover_mc = p_sp.getChildByName("cover_mc");
            cover_rect = cover_mc.getRect(p_sp);
            scaleRect(cover_rect,scale);
            cover_bmp = ComMethod.getBmp(cover_mc,false,scale);
            c_bmp = new BitmapData(p_bmp.width,p_bmp.height,true,0);
            cover_p = new Point(Math.round(p_rect.x - cover_rect.x),Math.round(p_rect.y - cover_rect.y));
            c_bmp.copyPixels(t_bmp,new Rectangle(x0,y0,c_bmp.width,c_bmp.height),new Point(),cover_bmp,cover_p);
            c_bmp.draw(p_bmp,null,null,BlendMode.SHADER);
            if(Math.random() < 0.5)
            {
            }
            c_bmp.draw(c_bmp,null,new ColorTransform(1,1,1,0.3),BlendMode.ADD);
         }
         else
         {
            c_bmp = p_bmp;
         }
         return c_bmp;
      }
      
      private static function scaleRect(rect0:Rectangle, scale:Number) : void
      {
         rect0.x *= scale;
         rect0.y *= scale;
         rect0.width *= scale;
         rect0.height *= scale;
      }
      
      public static function getSkinByArmsImgLabel(armsImgLabel0:String) : String
      {
         var url0:String = getUrlByArmsImgLabel(armsImgLabel0);
         var d0:ArmsSkinDefine = Gaming.defineGroup.armsCharger.getSkinByUrl(url0);
         if(Boolean(d0))
         {
            return d0.name;
         }
         return url0;
      }
      
      public static function getUrlByArmsImgLabel(armsImgLabel0:String) : String
      {
         var sarr0:Array = armsImgLabel0.split("_");
         var url0:String = sarr0[1];
         return String(url0).replace("$","/");
      }
      
      public static function getArmsImgLabelByUrl(url0:String) : String
      {
         url0 = String(url0).replace("/","$");
         return "0_" + url0 + "_0_0_0_0_0";
      }
   }
}


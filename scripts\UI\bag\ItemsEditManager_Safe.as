package UI.bag
{
   import UI.bag.ItemsEditCtrl;
   import UI.bag.ItemsGripBox;
   
   /**
    * 物品编辑功能管理器 - 安全版本
    * 简化版本，避免在游戏加载时执行复杂逻辑
    */
   public class ItemsEditManager_Safe
   {
      private static var instance:ItemsEditManager_Safe;
      private static var isEnabled:Boolean = false;
      
      public function ItemsEditManager_Safe()
      {
         if(instance != null)
         {
            throw new Error("ItemsEditManager_Safe is a singleton!");
         }
      }
      
      public static function getInstance():ItemsEditManager_Safe
      {
         if(instance == null)
         {
            instance = new ItemsEditManager_Safe();
         }
         return instance;
      }
      
      /**
       * 简单注册Box，立即启用编辑功能
       */
      public static function registerBox(box:ItemsGripBox):void
      {
         if(!box || !isEnabled) return;
         
         try {
            ItemsEditCtrl.addEvent_byItemsGripBox(box);
         } catch(e:Error) {
            // 启用失败不影响游戏运行
         }
      }
      
      /**
       * 启用编辑功能
       */
      public static function enable():void
      {
         isEnabled = true;
      }
      
      /**
       * 禁用编辑功能
       */
      public static function disable():void
      {
         isEnabled = false;
      }
      
      /**
       * 移除Box的编辑功能
       */
      public static function unregisterBox(box:ItemsGripBox):void
      {
         if(!box) return;
         
         try {
            ItemsEditCtrl.removeEvent_byItemsGripBox(box);
         } catch(e:Error) {
            // 移除失败不影响游戏运行
         }
      }
      
      /**
       * 检查是否已启用
       */
      public static function isReady():Boolean
      {
         return isEnabled;
      }
   }
}

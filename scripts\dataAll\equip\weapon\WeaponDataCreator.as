package dataAll.equip.weapon
{
   import dataAll._player.PlayerData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.must.define.MustDefine;
   
   public class WeaponDataCreator
   {
      public function WeaponDataCreator()
      {
         super();
      }
      
      public static function getSave(name0:String, num0:int) : WeaponSave
      {
         var d0:WeaponDefine = Gaming.defineGroup.weapon.getDefine(name0);
         if(!(d0 is WeaponDefine))
         {
            INIT.showError("找不到定义WeaponDefine：" + name0);
         }
         return getSaveByDefine(d0,num0);
      }
      
      public static function getSaveByDefine(d0:WeaponDefine, num0:int) : WeaponSave
      {
         var s0:WeaponSave = new WeaponSave();
         s0.inDataByDefine(d0);
         s0.nowNum = num0;
         return s0;
      }
      
      public static function getTempData(d0:WeaponDefine, pd0:PlayerData) : WeaponData
      {
         var s0:WeaponSave = getSaveByDefine(d0,1);
         var da0:WeaponData = new WeaponData();
         da0.inData_bySave(s0,pd0,null);
         return da0;
      }
      
      public static function getMustTip(d0:WeaponDefine) : String
      {
         var lv0:int = 0;
         var nowD0:WeaponDefine = null;
         var mustD0:MustDefine = null;
         var s0:String = "";
         var maxLv0:int = d0.getMaxLv();
         if(maxLv0 > 1)
         {
            for(lv0 = 2; lv0 <= maxLv0; lv0++)
            {
               nowD0 = Gaming.defineGroup.weapon.getDefine(d0.baseLabel + "_" + lv0);
               mustD0 = getUpgradeThingsMust(d0,lv0,ItemsDataGroup.PLACE_BAG);
               if(s0 != "")
               {
                  s0 += "\n";
               }
               s0 += "<b><yellow 第" + lv0 + "阶/></b>" + "<gray2 [" + nowD0.hurtMul + "倍]/>：" + mustD0.getThingsMustTip(99);
            }
         }
         return s0;
      }
      
      public static function getUpgradeMust(da0:WeaponData, lv0:int) : MustDefine
      {
         var weaponDefine0:WeaponDefine = da0.weaponDefine;
         var d0:MustDefine = getUpgradeThingsMust(weaponDefine0,lv0,da0.placeType);
         d0.lv = 75;
         return d0;
      }
      
      public static function getUpgradeThingsMust(weaponDefine0:WeaponDefine, lv0:int, placeType0:String) : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         var num0:int = getUpgradeNum(lv0,weaponDefine0);
         if(placeType0 == "bag" && lv0 == 2)
         {
            num0++;
         }
         var thingArr0:Array = [weaponDefine0.getUpgradeMustName() + ";" + num0];
         if(lv0 >= 9)
         {
            thingArr0.push("sickleScythe_2;1");
         }
         else if(lv0 >= 7)
         {
            thingArr0.push("sprintSword_1;1");
            if(lv0 >= 8)
            {
               thingArr0.push("saberDarts_1;1");
            }
         }
         d0.inThingsDataByArrAndMeger(thingArr0,MustDefine.NUM_EQUIP);
         d0.coin = num0 * 1000;
         return d0;
      }
      
      private static function getUpgradeNum(lv0:int, weaponDefine0:WeaponDefine) : int
      {
         var num0:int = 20;
         if(weaponDefine0.upgradeNum > 0)
         {
            num0 = weaponDefine0.upgradeNum;
         }
         var v0:int = (lv0 - 1) * num0;
         if(lv0 >= 7)
         {
            v0 = (lv0 - 6) * num0 * 6 + 60;
         }
         if(weaponDefine0.rareB && lv0 >= 3)
         {
         }
         return v0;
      }
      
      public static function getUpgradeAll(lv0:int, weaponDefine0:WeaponDefine) : int
      {
         var v0:Number = 1;
         for(var i:int = 2; i <= lv0; i++)
         {
            v0 += getUpgradeNum(i,weaponDefine0);
         }
         return v0;
      }
   }
}


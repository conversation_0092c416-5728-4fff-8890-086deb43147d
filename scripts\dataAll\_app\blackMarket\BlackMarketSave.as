package dataAll._app.blackMarket
{
   import com.sounto.oldUtils.SountoCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.arms.save.ArmsSaveGroup;
   import dataAll.equip.save.EquipSaveGroup;
   
   public class BlackMarketSave
   {
      public static var pro_arr:Array = [];
      
      private var CF:SountoCF = new SountoCF();
      
      public var arms:ArmsSaveGroup = new ArmsSaveGroup();
      
      public var equip:EquipSaveGroup = new EquipSaveGroup();
      
      public var goodsNameArr:Array = [];
      
      public var addB:Boolean = false;
      
      public function BlackMarketSave()
      {
         super();
         this.buyNum = 0;
         this.fleshNum = 0;
         this.arms.gripMaxNum = 999;
         this.arms.unlockTo(998);
         this.equip.gripMaxNum = 999;
         this.equip.unlockTo(998);
      }
      
      public function get buyNum() : Number
      {
         return this.CF.getAttribute("buyNum");
      }
      
      public function set buyNum(v0:Number) : void
      {
         this.CF.setAttribute("buyNum",v0);
      }
      
      public function get fleshNum() : Number
      {
         return this.CF.getAttribute("fleshNum");
      }
      
      public function set fleshNum(v0:Number) : void
      {
         this.CF.setAttribute("fleshNum",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function newDayCtrl() : void
      {
         this.addB = false;
         this.fleshNum = 0;
         this.buyNum = 0;
      }
   }
}


package dataAll._app.task
{
   public class TaskState
   {
      public static const lock:String = "lock";
      
      public static const no:String = "no";
      
      public static const ing:String = "ing";
      
      public static const complete:String = "complete";
      
      public static const over:String = "over";
      
      public static const del:String = "del";
      
      public static const fail:String = "fail";
      
      public static const noStart:String = "noStart";
      
      public static const isEnd:String = "isEnd";
      
      public static const noInTimeArr:Array = [noStart,isEnd];
      
      public function TaskState()
      {
         super();
      }
   }
}


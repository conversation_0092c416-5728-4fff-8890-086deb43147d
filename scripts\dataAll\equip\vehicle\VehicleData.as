package dataAll.equip.vehicle
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.city.dress.CityDressType;
   import dataAll._app.city.dress.IO_CityDressMouldMaker;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.body.attack.BodyAttackDefine;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipPropertyData;
   import dataAll.equip.save.EquipSave;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.items.ItemsDataGroup;
   import dataAll.skill.HeroSkillData;
   import dataAll.skill.HeroSkillDataGroup;
   import dataAll.skill.HeroSkillReset;
   import gameAll.body.data.NormalBodyData;
   
   public class VehicleData extends EquipData implements IO_CityDressMouldMaker
   {
      protected var attackDps:Number = 1;
      
      public var vehicleDefine:VehicleDefine;
      
      public var skill:HeroSkillDataGroup = new HeroSkillDataGroup();
      
      public function VehicleData()
      {
         super();
      }
      
      protected function getDpsNameArr() : Array
      {
         return VehicleDefineType.getDpsNameArr(this.vehicleDefine.defineType);
      }
      
      public function getVehicleSave() : VehicleSave
      {
         return save as VehicleSave;
      }
      
      override public function inData_bySave(s0:EquipSave, pd0:NormalPlayerData, dg0:ItemsDataGroup = null) : void
      {
         super.inData_bySave(s0,pd0,dg0);
         this.skill.inData_bySaveGroup(this.getVehicleSave().skill);
         this.skill.clearSameName();
         this.vehicleDefine = this.getVehicleSave().getVehicleDefine();
         this.skill.clearNoInBaseLabelArr(this.vehicleDefine.getSkillArr());
      }
      
      override public function clone() : EquipData
      {
         var da0:VehicleData = super.clone() as VehicleData;
         da0.vehicleDefine = this.vehicleDefine;
         return da0;
      }
      
      override public function getSellPrice() : Number
      {
         var goods_d0:GoodsDefine = null;
         if(save.shopB)
         {
            goods_d0 = Gaming.defineGroup.goods.getDefine(save.imgName);
            if(goods_d0 is GoodsDefine)
            {
               return goods_d0.price * 5000;
            }
         }
         return save.getSellPrice("equip") * 100;
      }
      
      override public function canStrengthenB() : Boolean
      {
         return true;
      }
      
      override public function getStartLv() : int
      {
         return int(this.getVehicleSave().getAllAddLv() / 5);
      }
      
      public function getPDDps(name0:String) : Number
      {
         var v0:Number = this.getPDDpsByArms(name0);
         var max0:Number = this.getPDDpsLimit(name0);
         if(v0 > max0)
         {
            v0 = max0;
         }
         return v0;
      }
      
      public function getPDDpsByArms(name0:String) : Number
      {
         var pd0:NormalPlayerData = normalPlayerData;
         if(!normalPlayerData)
         {
            return 0;
         }
         var armsType0:String = VehicleAttackLabel.dpsTargetObj[name0];
         var value0:Number = pd0.arms.getMaxDpsByType(armsType0);
         var max0:Number = pd0.arms.getMaxDpsByType("") * 0.5;
         if(value0 < max0)
         {
            value0 = max0;
         }
         value0 *= 4;
         value0 *= this.getHeroDpsAddMul();
         return value0 * this.getMulByName(name0);
      }
      
      public function getPDDpsLimit(name0:String) : Number
      {
         var maxLimit0:Number = this.getLimitDps();
         maxLimit0 *= this.getMulByName(name0);
         maxLimit0 *= this.getHeroDpsAddMul();
         return maxLimit0 * this.getHeroDpsAddMulWhole();
      }
      
      public function getHeroDpsAddMul() : Number
      {
         var me0:EquipPropertyData = null;
         var v0:Number = 1;
         if(Boolean(normalPlayerData))
         {
            me0 = normalPlayerData.getHeroMerge();
            v0 *= 1 + me0.vehicleDpsMul;
         }
         return v0;
      }
      
      public function getHeroDpsAddMulWhole() : Number
      {
         var me0:EquipPropertyData = null;
         var v0:Number = 1;
         if(Boolean(normalPlayerData))
         {
            me0 = normalPlayerData.getHeroMerge();
            v0 *= 1 + normalPlayerData.getDpsWholeAdd();
         }
         return v0;
      }
      
      public function fleshByNormalPlayerData() : void
      {
         var name0:* = null;
         var value0:Number = NaN;
         var dpsNameArr0:Array = this.getDpsNameArr();
         for each(name0 in dpsNameArr0)
         {
            value0 = this.getPDDps(name0);
            this.setDpsByType(name0,value0);
         }
      }
      
      public function fleshByNormalBodyData(dat0:NormalBodyData) : void
      {
         var name0:* = null;
         var value0:Number = NaN;
         var dpsNameArr0:Array = this.getDpsNameArr();
         for each(name0 in dpsNameArr0)
         {
            value0 = dat0.dpsFactor;
            value0 *= this.getMulByName(name0);
            this.setDpsByType(name0,value0);
         }
      }
      
      public function getBodyDefine() : NormalBodyDefine
      {
         return this.vehicleDefine.getBodyDefine();
      }
      
      public function getDps() : Number
      {
         return this.attackDps * 1;
      }
      
      public function setDps(v0:Number) : void
      {
         this.attackDps = v0;
      }
      
      public function getDpsByType(type0:String) : Number
      {
         return this[type0 + "Dps"];
      }
      
      public function setDpsByType(type0:String, value0:Number) : void
      {
         this[type0 + "Dps"] = value0;
      }
      
      public function getUnderHurtMul() : Number
      {
         var defAdd0:Number = 0;
         if(Boolean(playerData))
         {
            defAdd0 = playerData.getHeroMerge().vehicleDefMul;
         }
         return 1 / (1 + defAdd0);
      }
      
      public function getBaseLife() : Number
      {
         var d0:VehicleDefine = this.vehicleDefine;
         var lifeMul0:Number = this.getLifeMul();
         var base0:Number = Gaming.defineGroup.normal.getPropertyValue("vehicleBaseLife",save.getTrueLevel());
         return base0 * lifeMul0;
      }
      
      public function getMaxLife() : Number
      {
         var heroMaxLife0:Number = NaN;
         var d0:VehicleDefine = this.vehicleDefine;
         var base0:Number = this.getBaseLife();
         var heroLife0:Number = 0;
         if(Boolean(normalPlayerData))
         {
            heroLife0 = normalPlayerData.base.getMaxLife();
            heroMaxLife0 = this.getLimitLife();
            heroLife0 *= 8;
            if(heroLife0 > heroMaxLife0)
            {
               heroLife0 = heroMaxLife0;
            }
            heroLife0 *= this.getLifeMul();
         }
         return Math.ceil(base0 + heroLife0);
      }
      
      public function getMulByName(name0:String) : Number
      {
         return this.getMulByLabel(name0 + "Mul");
      }
      
      public function getMulByLabel(name0:String) : Number
      {
         var d0:VehicleDefine = this.vehicleDefine;
         return this.getVehicleSave().getMulAdd(name0) + d0.getMulByLabel(name0);
      }
      
      public function getLifeMul() : Number
      {
         return this.getMulByLabel("lifeMul");
      }
      
      public function getAttackMul() : Number
      {
         return this.getMulByLabel("attackMul");
      }
      
      override public function getStrengthenLv() : int
      {
         return this.getVehicleSave().getAllAddLv();
      }
      
      public function canStudySkillB() : Boolean
      {
         return this.getStrengthenLv() >= VehicleDataCreator.getSkillStudyLv();
      }
      
      public function getSkillMustLVStr() : String
      {
         var must0:int = VehicleDataCreator.getSkillStudyLv();
         var now0:int = this.getStrengthenLv();
         return ComMethod.mustColor(now0,must0);
      }
      
      override public function getResolveGift() : GiftAddDefineGroup
      {
         var skillG0:GiftAddDefineGroup = null;
         var strengthenG0:GiftAddDefineGroup = null;
         var g0:GiftAddDefineGroup = super.getResolveGift();
         if(Boolean(g0))
         {
            skillG0 = HeroSkillReset.getReset(this.skill.dataArr,1,false);
            g0.merge(skillG0);
            strengthenG0 = VehicleDataCreator.getAllStrengthenMust(this.getVehicleSave().getAllAddLv());
            g0.merge(strengthenG0);
            return g0;
         }
         return null;
      }
      
      public function limitLife() : Number
      {
         var d0:VehicleDefine = this.vehicleDefine;
         var heroMaxLife0:Number = this.getLimitLife();
         var base0:Number = this.getBaseLife();
         return Math.ceil(base0 + heroMaxLife0 * this.getLifeMul());
      }
      
      public function limitAttackHurt() : Number
      {
         var maxDps0:Number = this.getLimitDps();
         var bodyD0:NormalBodyDefine = this.getBodyDefine();
         var at_d0:BodyAttackDefine = bodyD0.hurtArr[0];
         var hurt0:Number = at_d0.hurtRatio;
         return Math.round(maxDps0 * this.getAttackMul() * hurt0);
      }
      
      protected function getLimitLife() : Number
      {
         var v0:Number = Gaming.defineGroup.normal.getPropertyValue("vehicleHeroMaxLife",save.getTrueLevel());
         var mul0:Number = 1 + this.getVehicleSave().upLv * 0.1;
         return Math.ceil(v0 * mul0);
      }
      
      protected function getLimitDps() : Number
      {
         var v0:Number = Gaming.defineGroup.normal.getPropertyValue("vehicleMaxDps",save.getTrueLevel());
         var mul0:Number = 1 + this.getVehicleSave().upLv * 0.1;
         return Math.ceil(v0 * mul0);
      }
      
      override public function canUpgradeB() : Boolean
      {
         return this.canUpgradeVehicleB();
      }
      
      public function canUpgradeVehicleB() : Boolean
      {
         return this.getUpgradeAndLv() < 105;
      }
      
      public function upgradeVehicle() : void
      {
         if(this.canUpgradeVehicleB())
         {
            if(save.getTrueLevel() >= 99)
            {
               ++this.getVehicleSave().upLv;
            }
            else
            {
               ++save.addLevel;
            }
            this.fleshByNormalPlayerData();
         }
      }
      
      public function getUpgradeAndLv() : int
      {
         return save.getTrueLevel() + this.getVehicleSave().upLv;
      }
      
      override public function canResolveB() : Boolean
      {
         if(save.shopB)
         {
            return false;
         }
         return super.canResolveB();
      }
      
      public function canEvolutionB() : Boolean
      {
         return this.vehicleDefine.canEvolutionB();
      }
      
      public function getEvolutionData() : VehicleData
      {
         var da0:VehicleData = null;
         var d0:VehicleDefine = this.vehicleDefine.getEvolutionDefine();
         if(Boolean(d0))
         {
            return VehicleDataCreator.getTempData(d0,normalPlayerData,save.getTrueLevel(),this.getVehicleSave());
         }
         return null;
      }
      
      public function changeToOneData(da0:VehicleData) : void
      {
         var name0:* = null;
         var proArr0:Array = ["site","lockB","getTime","severTime"];
         for each(name0 in proArr0)
         {
            da0.save[name0] = save[name0];
         }
         this.getVehicleSave().inEvoData_byObj(da0.save);
         this.skill.saveGroup = null;
         this.inData_bySave(save,normalPlayerData);
         this.fleshByNormalPlayerData();
      }
      
      public function getUISkillArr() : Array
      {
         var name0:* = null;
         var da0:HeroSkillData = null;
         var daArr0:Array = [];
         var dArr0:Array = [];
         var vD0:VehicleDefine = this.getVehicleSave().getVehicleDefine();
         var arr0:Array = vD0.getSkillArr();
         for each(name0 in arr0)
         {
            da0 = this.skill.getDataByBase(name0) as HeroSkillData;
            if(da0 is HeroSkillData)
            {
               daArr0.push(da0);
            }
            else
            {
               dArr0.push(Gaming.defineGroup.skill.getDefine(name0));
            }
         }
         daArr0.sort(this.uiSkillSortFun);
         return daArr0.concat(dArr0);
      }
      
      private function uiSkillSortFun(a0:HeroSkillData, b0:HeroSkillData) : int
      {
         return ArrayMethod.sortNumberFun(a0.save.site,b0.save.site);
      }
      
      public function getHaveSkillCnArr() : Array
      {
         var sda0:HeroSkillData = null;
         var cnArr0:Array = [];
         for each(sda0 in this.skill.dataArr)
         {
            cnArr0.push(sda0.save.getDefine().cnName);
         }
         return cnArr0;
      }
      
      override public function getGatherTip(compareDa0:EquipData = null, tipType0:String = "", showSkillB:Boolean = true) : String
      {
         var str0:String = "";
         this.fleshByNormalPlayerData();
         return str0 + VehicleDataCreator.getTip(this,compareDa0 as VehicleData);
      }
      
      public function getId() : String
      {
         return save.id;
      }
      
      public function getIconUrl() : String
      {
         return getIconImgUrl();
      }
      
      public function getCityDressType() : String
      {
         return CityDressType.vehicle;
      }
      
      public function getCityDressImgUrl() : String
      {
         var b0:NormalBodyDefine = this.vehicleDefine.getBodyDefine();
         return b0.bmpUrl;
      }
   }
}


package dataAll.arms.save
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.ObjectMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit._def.EditProMethod;
   import dataAll.pro.ProType;
   
   public class FilterSave
   {
      private static const ZERO_ARR:Array = [];
      
      public static const NO_NUM:Number = -1;
      
      public var o:Object = {};
      
      public function FilterSave()
      {
         super();
      }
      
      public static function getTextAll(save0:ArmsFilterSave) : String
      {
         var d0:EditProDefine = null;
         var one0:String = null;
         var s0:String = "";
         var darr0:Array = save0.getProDefArr();
         for each(d0 in darr0)
         {
            one0 = getTextOne(d0,save0);
            if(s0 != "")
            {
               s0 += "\n";
            }
            s0 += one0;
         }
         return s0 + save0.getTextLast();
      }
      
      private static function getTextOne(d0:EditProDefine, save0:ArmsFilterSave) : String
      {
         var type0:String = save0.getProType(d0);
         if(type0 == ProType.ARRAY)
         {
            return getTextArray(d0,save0);
         }
         if(type0 == ProType.MIN_MAX)
         {
            return getTextMinMax(d0,save0);
         }
         return d0.cnName;
      }
      
      private static function getTextMinMax(d0:EditProDefine, save0:ArmsFilterSave) : String
      {
         var rangeStr0:String = null;
         var s0:String = "";
         var cn0:String = d0.cnName;
         var pro0:String = d0.name;
         var min0:Number = save0.getProMin(pro0);
         var max0:Number = save0.getProMax(pro0);
         var minStr0:String = " " + NumberMethod.toFixed(min0,d0.fixed) + " ";
         var maxStr0:String = " " + NumberMethod.toFixed(max0,d0.fixed) + " ";
         minStr0 = TextMethod.link(minStr0,pro0 + ":min");
         maxStr0 = TextMethod.link(maxStr0,pro0 + ":max");
         s0 = cn0 + "  ";
         if(min0 == NO_NUM && max0 == NO_NUM)
         {
            s0 += "无";
         }
         else
         {
            s0 += minStr0 + " ~ " + maxStr0;
            rangeStr0 = NumberMethod.toFixed(save0.getDefineProMin(pro0),d0.fixed) + "~" + NumberMethod.toFixed(save0.getDefineProMax(pro0),d0.fixed);
            rangeStr0 = ComMethod.color("(" + rangeStr0 + ")","#666666",12);
            s0 += " " + rangeStr0;
         }
         return s0;
      }
      
      private static function getTextArray(d0:EditProDefine, save0:ArmsFilterSave) : String
      {
         var value0:* = null;
         var valueCn0:String = null;
         var one0:String = null;
         var linkLabel0:String = null;
         var linkStr0:String = null;
         var lastStr0:String = null;
         var chooseB0:Boolean = false;
         var valueArr0:Array = save0.getProValueArr(d0.name);
         var chooseArr0:Array = save0.getProChooseArr(d0);
         var chooseMax0:int = save0.getProChooseMax(d0.name);
         var s0:String = "";
         var len0:int = int(valueArr0.length);
         var cn0:String = d0.cnName;
         if(save0.getProArrayMustB(d0))
         {
            cn0 = "必含" + cn0;
         }
         var numStr0:String = len0 + "";
         if(len0 > 0)
         {
            numStr0 = "<b>" + ComMethod.green(numStr0) + "</b>";
         }
         if(chooseMax0 >= 0)
         {
            numStr0 += "/" + chooseMax0;
         }
         s0 += cn0 + "(" + numStr0 + ")  ";
         for each(value0 in chooseArr0)
         {
            valueCn0 = EditProMethod.getCn(d0.method,value0);
            one0 = "";
            linkLabel0 = d0.name + ":" + value0;
            linkStr0 = TextMethod.link(valueCn0,linkLabel0);
            lastStr0 = "";
            chooseB0 = valueArr0.indexOf(value0) >= 0;
            if(chooseB0)
            {
               lastStr0 = ComMethod.orange("<b>√</b> ");
               linkStr0 = "<b>" + linkStr0 + "</b>";
            }
            else
            {
               lastStr0 = "  ";
            }
            one0 += linkStr0 + lastStr0;
            s0 += one0;
         }
         return s0;
      }
      
      public function getProValueArr(pro0:String) : Array
      {
         var arr0:Array = ObjectMethod.getEleIfHave(this.o,pro0,null) as Array;
         if(arr0 == null)
         {
            arr0 = ZERO_ARR;
         }
         return arr0;
      }
      
      public function getProValueString(pro0:String) : String
      {
         return ObjectMethod.getEleIfHave(this.o,pro0,"");
      }
      
      public function getProValue(pro0:String) : *
      {
         return ObjectMethod.getEleIfHave(this.o,pro0,null);
      }
      
      public function setProValue(pro0:String, v0:*) : void
      {
         this.o[pro0] = v0;
      }
      
      public function getProValueArrAdd(pro0:String) : Array
      {
         return ObjectMethod.getEleAndAdd(this.o,pro0,Array) as Array;
      }
      
      public function getDefineProMin(pro0:String) : Number
      {
         return NO_NUM;
      }
      
      public function getDefineProMax(pro0:String) : Number
      {
         return NO_NUM;
      }
      
      private function inDefineProRange(pro0:String, v0:Number, maxB0:Boolean) : Number
      {
         var min0:Number = this.getDefineProMin(pro0);
         var max0:Number = this.getDefineProMax(pro0);
         if(v0 == NO_NUM)
         {
            if(maxB0)
            {
               v0 = max0;
            }
            else
            {
               v0 = min0;
            }
         }
         else
         {
            if(min0 != NO_NUM && v0 < min0)
            {
               v0 = min0;
            }
            if(max0 != NO_NUM && v0 > max0)
            {
               v0 = max0;
            }
         }
         return v0;
      }
      
      public function getProMin(pro0:String) : Number
      {
         var str0:String = this.getProValueString(pro0);
         var v0:Number = NumberMethod.getNumInArrStr(str0,0,NO_NUM);
         return this.inDefineProRange(pro0,v0,false);
      }
      
      public function setProMin(pro0:String, v0:Number) : void
      {
         v0 = this.inDefineProRange(pro0,v0,false);
         var str0:String = this.getProValueString(pro0);
         str0 = NumberMethod.setNumInArrStr(str0,v0,0);
         this.setProValue(pro0,str0);
      }
      
      public function getProMax(pro0:String) : Number
      {
         var str0:String = this.getProValueString(pro0);
         var v0:Number = NumberMethod.getNumInArrStr(str0,1,NO_NUM);
         return this.inDefineProRange(pro0,v0,true);
      }
      
      public function setProMax(pro0:String, v0:Number) : void
      {
         v0 = this.inDefineProRange(pro0,v0,true);
         var str0:String = this.getProValueString(pro0);
         str0 = NumberMethod.setNumInArrStr(str0,v0,1);
         this.setProValue(pro0,str0);
      }
   }
}


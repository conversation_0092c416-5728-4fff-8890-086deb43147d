package dataAll._app.union.task
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   
   public class UnionTaskSave
   {
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var getB:Boolean = false;
      
      public function UnionTaskSave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function inData_byDefine(d0:UnionTaskDefine) : void
      {
      }
   }
}


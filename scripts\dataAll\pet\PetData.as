package dataAll.pet
{
   import UI.pet.info.PetSuppleExpendUIData;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.city.dress.CityDressType;
   import dataAll._app.city.dress.IO_CityDressMouldMaker;
   import dataAll._player.PlayerData;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.equip.EquipPropertyData;
   import dataAll.equip.define.EquipColor;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.pet.base.PetBaseData;
   import dataAll.pet.dispatch.PetDispatchGift;
   import dataAll.pet.gene.GeneData;
   import dataAll.pet.gene.define.GeneDefine;
   import dataAll.pet.skill.PetSkillDataGroup;
   import dataAll.skill.HeroSkillData;
   import dataAll.skill.HeroSkillReset;
   import gameAll.body.IO_NormalBody;
   
   public class PetData implements IO_CityDressMouldMaker
   {
      public var PD:PlayerData;
      
      public var save:PetSave;
      
      public var gene:GeneData;
      
      public var base:PetBaseData = new PetBaseData();
      
      public var skill:PetSkillDataGroup = new PetSkillDataGroup();
      
      private var proData:EquipPropertyData;
      
      public var tempBody:IO_NormalBody;
      
      public function PetData()
      {
         super();
      }
      
      public function inData_bySave(s0:PetSave, pd0:PlayerData) : void
      {
         this.PD = pd0;
         this.save = s0;
         this.gene = new GeneData();
         this.gene.inData_bySave(s0.gene,pd0);
         this.base.inData_bySave(s0.base,pd0);
         this.skill.inData_bySaveGroup(s0.skill);
         this.skill.initGripMaxNum();
         this.skill.sortByActive();
         this.fleshProData();
      }
      
      public function initData_byGeneData(geneDa0:GeneData, pd0:PlayerData) : void
      {
         if(this.gene is GeneData)
         {
            INIT.showError("PetData已经拥有gene了");
         }
         this.PD = pd0;
         this.gene = geneDa0;
         this.base.initData_byGeneData(this.gene,pd0);
         this.skill.initData_byGeneData(this.gene,pd0);
         this.fleshSave();
         this.fleshProData();
      }
      
      private function fleshSave() : void
      {
         this.save = new PetSave();
         this.save.base = this.base.save;
         this.save.gene = this.gene.save;
         this.save.skill = this.skill.saveGroup;
      }
      
      public function getGeneDefine() : GeneDefine
      {
         return this.gene.save.getDefine();
      }
      
      public function setNewGeneData(da0:GeneData) : void
      {
         this.gene = da0;
         this.fleshSave();
      }
      
      public function fleshProData() : void
      {
         this.proData = new EquipPropertyData();
         this.proData.addData(this.gene.save.obj);
         this.proData.addData(this.gene.save.getLifeRateGrowObj(this.proData.lifeRate));
         this.base.inProData(this.proData);
      }
      
      public function getProData() : EquipPropertyData
      {
         return this.proData;
      }
      
      public function resetSkill(baseLabel0:String) : String
      {
         var haveArr0:Array = this.gene.save.getAllSkillArr();
         var newArr0:Array = Gaming.defineGroup.geneCreator.getLaterSkillArrBy(1,haveArr0);
         var new0:String = newArr0[0];
         var beforeDa0:HeroSkillData = this.skill.getDataByBase(baseLabel0);
         if(Boolean(beforeDa0))
         {
            this.skill.removeData(beforeDa0);
         }
         ArrayMethod.replace(this.gene.save.laterSkillArr,baseLabel0,new0);
         return new0;
      }
      
      public function getUISkillArr() : Array
      {
         var name0:* = null;
         var da0:HeroSkillData = null;
         var arr2:Array = [];
         var arr0:Array = this.gene.save.getAllSkillArr();
         for each(name0 in arr0)
         {
            da0 = this.skill.getDataByBase(name0) as HeroSkillData;
            if(da0 is HeroSkillData)
            {
               arr2.push(da0);
            }
            else
            {
               arr2.push(Gaming.defineGroup.skill.getDefine(name0));
            }
         }
         return arr2;
      }
      
      public function getSkillStudyMustLv() : int
      {
         return this.skill.getStudyMustLv() + PetCount.skillFirstLevel;
      }
      
      public function getBodyDefine() : NormalBodyDefine
      {
         return this.gene.save.getDefine().getBodyDefine();
      }
      
      public function setTempBody(b0:IO_NormalBody) : void
      {
         this.tempBody = b0;
         if(Boolean(b0))
         {
            b0.getData().inPetData(this);
         }
      }
      
      public function setState(str0:String) : void
      {
         if(str0 == PetState.FIGHT)
         {
            this.save.fightB = true;
            this.save.suppleB = false;
         }
         else if(str0 == PetState.SUPPLE)
         {
            this.save.fightB = false;
            this.save.suppleB = true;
         }
         else
         {
            this.save.fightB = false;
            this.save.suppleB = false;
         }
      }
      
      public function getState() : String
      {
         return this.save.getState();
      }
      
      public function getColorPlayerName() : String
      {
         var color0:String = EquipColor.htmlColor(this.gene.getColor());
         return ComMethod.color(this.base.save.playerName,color0);
      }
      
      public function getDispatchHour() : int
      {
         return PetDispatchGift.getHourByLife(this.base.getMaxLife());
      }
      
      public function getDispatchHighPro() : Number
      {
         var pro0:Number = PetDispatchGift.getHighProByDps(this.base.getShowDps());
         var pro2:Number = PetDispatchGift.getHighProByGrow(this.gene.getGrowAllPro());
         return pro0 + pro2;
      }
      
      public function getReturnThingsAffterGiveUp() : GiftAddDefineGroup
      {
         var suppleNum0:int = 0;
         var dg0:GiftAddDefineGroup = HeroSkillReset.getReset(this.skill.dataArr,0.5,false);
         var sNum0:int = this.base.getNowUseStrengthenDrugNum() * 0.5;
         if(sNum0 > 0)
         {
            dg0.addGiftByStr("things;strengthenDrug;" + sNum0);
         }
         if(this.save.base.suppleFunB)
         {
            suppleNum0 = PetSuppleExpendUIData.getMustBackNum();
            dg0.addGiftByStr("things;petSuppleCard;" + suppleNum0);
         }
         return dg0;
      }
      
      public function transferOther(da0:PetData) : void
      {
         this.save.base.transferOther(da0.save.base);
         this.gene.save.transferOther(da0.gene.save);
         this.fleshProData();
      }
      
      public function getId() : String
      {
         return this.save.base.id;
      }
      
      public function getCnName() : String
      {
         return this.save.base.playerName;
      }
      
      public function getIconUrl() : String
      {
         return this.getBodyDefine().headIconUrl;
      }
      
      public function getCityDressType() : String
      {
         return CityDressType.pet;
      }
      
      public function getCityDressImgUrl() : String
      {
         var b0:NormalBodyDefine = this.getBodyDefine();
         return b0.name + "/stand";
      }
   }
}


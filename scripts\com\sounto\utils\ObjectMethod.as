package com.sounto.utils
{
   public class ObjectMethod
   {
      public function ObjectMethod()
      {
         super();
      }
      
      public static function testSamePan() : void
      {
         var a:Object = {
            "x":11,
            "y":"gd"
         };
         var b:Object = {"x":11};
         var bb0:Boolean = samePan(a,b);
         a = {"x":"11"};
         b = {"x":11};
         var bb1:Boolean = samePan(a,b);
         a = {"x":[11]};
         b = {"x":[11]};
         var bb2:Boolean = samePan(a,b);
         a = {"x":["11"]};
         b = {"x":[11]};
         var bb3:Boolean = samePan(a,b);
         a = {"x":{
            "x":11,
            "y":"gd"
         }};
         b = {"x":{"x":11}};
         var bb4:Boolean = samePan(a,b);
         a = {"x":{"x":11}};
         b = {"x":{"x":11}};
         var bb5:<PERSON><PERSON>an = samePan(a,b);
         a = {
            "x":{"x":11},
            "y":[1,"df"],
            "z":34
         };
         b = {
            "z":34,
            "x":{"x":11},
            "y":[1,"df"]
         };
         var bb6:Boolean = samePan(a,b);
      }
      
      public static function samePan(o1:Object, o2:Object) : Boolean
      {
         var n:* = undefined;
         var m:* = undefined;
         var v1:* = undefined;
         var v2:* = undefined;
         if(o1 is Array && o2 is Array)
         {
            return ArrayMethod.samePan(o1 as Array,o2 as Array);
         }
         if(o1 is Array && !(o2 is Array))
         {
            return false;
         }
         if(o2 is Array && !(o1 is Array))
         {
            return false;
         }
         for(n in o1)
         {
            if(o2.hasOwnProperty(n) == false)
            {
               return false;
            }
            v1 = o1[n];
            v2 = o2[n];
            if(ClassProperty.isNormalPro(v1))
            {
               if(typeof v1 != typeof v2)
               {
                  return false;
               }
               if(v1 != v2)
               {
                  if(!(v1 is Number && v2 is Number))
                  {
                     return false;
                  }
                  if(Number(v1).toFixed(8) != Number(v2).toFixed(8))
                  {
                     return false;
                  }
               }
            }
            else if(v1 is Array && v2 is Array)
            {
               if(ArrayMethod.samePan(v1 as Array,v2 as Array) == false)
               {
                  return false;
               }
            }
            else if(samePan(v1,v2) == false)
            {
               return false;
            }
         }
         for(m in o2)
         {
            if(o1.hasOwnProperty(m) == false)
            {
               return false;
            }
         }
         return true;
      }
      
      public static function getNumberEleIfHave(obj0:Object, name0:String) : Number
      {
         if(obj0.hasOwnProperty(name0))
         {
            return obj0[name0];
         }
         return 0;
      }
      
      public static function getEleIfHave(obj0:Object, name0:String, noValue0:*) : *
      {
         if(obj0 == null)
         {
            return noValue0;
         }
         if(obj0.hasOwnProperty(name0))
         {
            return obj0[name0];
         }
         return noValue0;
      }
      
      public static function getEleAndAdd(obj0:Object, name0:String, addClass0:Class) : *
      {
         if(obj0.hasOwnProperty(name0) == false)
         {
            obj0[name0] = new addClass0();
         }
         return obj0[name0];
      }
      
      public static function addNum(obj0:Object, name0:String, v0:Number) : void
      {
         if(obj0[name0] == null)
         {
            obj0[name0] = 0;
         }
         obj0[name0] += v0;
      }
      
      public static function addNumberObj(mobj0:Object, obj0:Object) : *
      {
         var n:* = undefined;
         if(obj0 == null || mobj0 == null)
         {
            return;
         }
         for(n in obj0)
         {
            if(obj0[n] is Number)
            {
               if(mobj0.hasOwnProperty(n) == false)
               {
                  mobj0[n] = 0;
               }
               mobj0[n] += obj0[n];
            }
         }
      }
      
      public static function keepName(targetObj0:Object, nameObj0:Object) : Object
      {
         var n:* = undefined;
         var obj0:Object = {};
         for(n in nameObj0)
         {
            if(Boolean(targetObj0[n]))
            {
               obj0[n] = targetObj0[n];
            }
         }
         return obj0;
      }
      
      public static function remove(obj0:Object, name0:Object) : Object
      {
         var newObj0:Object = null;
         var n:* = undefined;
         if(obj0.hasOwnProperty(name0))
         {
            newObj0 = {};
            for(n in obj0)
            {
               if(n != name0)
               {
                  newObj0[n] = obj0[n];
               }
            }
            return newObj0;
         }
         return obj0;
      }
      
      public static function removeArr(obj0:Object, nameArr0:Array) : Object
      {
         var n:* = undefined;
         var new0:Object = {};
         for(n in obj0)
         {
            if(nameArr0.indexOf(n) == -1)
            {
               new0[n] = obj0[n];
            }
         }
         return new0;
      }
      
      public static function getNameArr(obj0:Object) : Array
      {
         var n:* = undefined;
         var nameArr0:Array = [];
         for(n in obj0)
         {
            nameArr0.push(n);
         }
         return nameArr0;
      }
      
      public static function shadowCopy(obj0:Object) : Object
      {
         var n:* = undefined;
         var b2:Object = {};
         for(n in obj0)
         {
            b2[n] = obj0[n];
         }
         return b2;
      }
      
      public static function coverObj(targetObj0:Object, obj2:Object) : void
      {
         var n:* = undefined;
         for(n in targetObj0)
         {
            if(obj2.hasOwnProperty(n))
            {
               targetObj0[n] = obj2[n];
            }
         }
      }
      
      public static function fixedObj(targetObj0:Object, obj2:Object) : void
      {
         var n:* = undefined;
         for(n in obj2)
         {
            targetObj0[n] = obj2[n];
         }
      }
      
      public static function getObjElementNum(obj0:Object) : int
      {
         var n:* = undefined;
         var num0:int = 0;
         for(n in obj0)
         {
            num0++;
         }
         return num0;
      }
      
      public static function isZeroB(obj0:Object) : Boolean
      {
         var n:* = undefined;
         var _loc3_:int = 0;
         var _loc4_:* = obj0;
         for(n in _loc4_)
         {
            return false;
         }
         return true;
      }
      
      public static function toArr(obj0:Object) : Array
      {
         var b0:Object = null;
         var arr0:Array = [];
         for each(b0 in obj0)
         {
            arr0.push(b0);
         }
         return arr0;
      }
      
      public static function swapElement(obj0:Object, id1:*, id2:*) : void
      {
         var v1:Object = null;
         var v2:Object = null;
         if(Boolean(obj0))
         {
            v1 = obj0[id1];
            v2 = obj0[id2];
            obj0[id1] = v2;
            obj0[id2] = v1;
         }
      }
      
      public static function getNumberObjSum(obj0:Object) : Number
      {
         var v0:Number = NaN;
         var sum0:Number = 0;
         for each(v0 in obj0)
         {
            sum0 += v0;
         }
         return sum0;
      }
      
      public static function getRandomNameByProValue(obj0:Object) : String
      {
         var n:* = undefined;
         var index0:int = 0;
         var nameArr0:Array = [];
         var proArr0:Array = [];
         for(n in obj0)
         {
            proArr0.push(obj0[n]);
            nameArr0.push(n);
         }
         index0 = ArrayMethod.getPro_byArrSum(proArr0);
         return nameArr0[index0];
      }
      
      public static function fixedNumberObjNew(obj0:Object, obj2:Object) : Object
      {
         var n:* = undefined;
         var targetObj0:Object = ClassProperty.copyObj(obj0);
         for(n in obj2)
         {
            if(!targetObj0.hasOwnProperty(n))
            {
               targetObj0[n] = 0;
            }
            targetObj0[n] += obj2[n];
         }
         return targetObj0;
      }
      
      public static function fixedNumberObj(obj0:Object, obj2:Object) : Object
      {
         var n:* = undefined;
         var targetObj0:Object = obj0;
         if(!targetObj0)
         {
            targetObj0 = {};
         }
         for(n in obj2)
         {
            if(!targetObj0.hasOwnProperty(n))
            {
               targetObj0[n] = 0;
            }
            targetObj0[n] += obj2[n];
         }
         return targetObj0;
      }
      
      public static function getProByTwo(obj0:Object, id1:String, id2:String) : *
      {
         var obj2:Object = null;
         if(obj0.hasOwnProperty(id1))
         {
            obj2 = obj0[id1];
            if(Boolean(obj2))
            {
               if(obj2.hasOwnProperty(id2))
               {
                  return obj2[id2];
               }
            }
         }
         return null;
      }
      
      public static function getProValueByPath(obj0:Object, path0:String) : *
      {
         var pro0:* = null;
         var index0:Number = NaN;
         var nowObj0:Object = null;
         var strArr0:Array = path0.split(".");
         var beforeObj0:Object = obj0;
         for each(pro0 in strArr0)
         {
            index0 = Number(pro0);
            if(isNaN(index0))
            {
               if(beforeObj0.hasOwnProperty(pro0))
               {
                  nowObj0 = beforeObj0[pro0];
               }
               else if(beforeObj0.hasOwnProperty("getValueByName"))
               {
                  nowObj0 = beforeObj0["getValueByName"](pro0);
               }
               else
               {
                  INIT.showError(obj0 + " 无法获得路径：" + path0 + " 中的 " + pro0);
               }
            }
            else
            {
               nowObj0 = beforeObj0[int(index0)];
            }
            beforeObj0 = nowObj0;
         }
         return beforeObj0;
      }
      
      public static function stringToNumberObj(str0:String) : Object
      {
         var oneArr0:Array = null;
         var one0:* = null;
         var proArr0:Array = null;
         var pro0:String = null;
         var num0:Number = NaN;
         var obj0:Object = {};
         if(str0 != "")
         {
            oneArr0 = str0.split(",");
            for each(one0 in oneArr0)
            {
               proArr0 = one0.split(":");
               pro0 = proArr0[0];
               num0 = Number(proArr0[1]);
               obj0[pro0] = num0;
            }
         }
         return obj0;
      }
      
      public static function isMustObjectB(obj0:*) : Boolean
      {
         if(obj0 == null)
         {
            return false;
         }
         if(obj0 is String)
         {
            return false;
         }
         if(obj0 is Number)
         {
            return false;
         }
         if(obj0 is int)
         {
            return false;
         }
         if(obj0 is Boolean)
         {
            return false;
         }
         if(obj0 is Array)
         {
            return false;
         }
         return true;
      }
   }
}


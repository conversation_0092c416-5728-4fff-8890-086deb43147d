package dataAll._app.parts.define
{
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.bullet.BulletDefine;
   
   public class PartsType
   {
      public static const bullet:String = "bullet";
      
      public static const shooter:String = "shooter";
      
      public static const capacity:String = "capacity";
      
      public static const loader:String = "loader";
      
      public static const stabler:String = "stabler";
      
      public static const sight:String = "sight";
      
      public static const special:String = "special";
      
      public static const skill:String = "skill";
      
      public static const rare:String = "rare";
      
      private static const normalArr:Array = [bullet,shooter,capacity,loader,stabler,sight];
      
      public static const canLoadArr:Array = normalArr.concat([skill,special]);
      
      public static const arr:Array = canLoadArr.concat([rare]);
      
      public static const NORMAL:String = loader;
      
      public static const wearArr:Array = arr.concat([rare,rare,rare,rare,rare,rare]);
      
      private static const bulletCn:String = "伤害";
      
      private static const shooterCn:String = "射速";
      
      private static const capacityCn:String = "弹容";
      
      private static const loaderCn:String = "装弹速";
      
      private static const stablerCn:String = "精准度";
      
      private static const sightCn:String = "射程";
      
      private static const specialCn:String = "芯片";
      
      private static const skillCn:String = "技能器";
      
      private static const rareCn:String = "稀零";
      
      public static const normalDescrip:String = "根据装配位置，提升武器的伤害、射击速度、弹容、装弹速度、精准度或者射程。";
      
      public static const NORMAL_PARTS:String = getPartsName(NORMAL);
      
      private static const normalPartArr:Array = [];
      
      public function PartsType()
      {
         super();
      }
      
      public static function staticInit() : void
      {
         var type0:* = null;
         for each(type0 in normalArr)
         {
            normalPartArr.push(getPartsName(type0));
         }
      }
      
      public static function getWearLen() : int
      {
         return wearArr.length;
      }
      
      public static function getWearUnlockLen() : int
      {
         return 10;
      }
      
      public static function getTypeCn(type0:String, colorB0:Boolean = false) : String
      {
         if(type0 == skill || type0 == special)
         {
            if(colorB0)
            {
               return "<purple 特殊零件/>";
            }
            return "特殊零件";
         }
         if(type0 == rare)
         {
            if(colorB0)
            {
               return "<orange 稀有零件/>";
            }
            return "稀有零件";
         }
         return "普通零件";
      }
      
      public static function getCn(type0:String) : String
      {
         return PartsType[type0 + "Cn"];
      }
      
      public static function getPartsName(type0:String) : String
      {
         return type0 + "Parts";
      }
      
      public static function getWearTypeSite(type0:String) : int
      {
         return wearArr.indexOf(type0);
      }
      
      public static function getWearType(site0:int) : String
      {
         return wearArr[site0];
      }
      
      public static function getWearThingsType(site0:int) : String
      {
         var type0:String = getWearType(site0);
         if(isNormalTypeB(type0))
         {
            return NORMAL;
         }
         return type0;
      }
      
      public static function getThingsTypeBySite(site0:int) : String
      {
         var gripType0:String = getWearType(site0);
         return getThingsType(gripType0);
      }
      
      public static function isNormalTypeB(type0:String) : Boolean
      {
         return normalArr.indexOf(type0) >= 0;
      }
      
      public static function getWearNormalLen() : int
      {
         return normalArr.length;
      }
      
      public static function isNormalPartsB(partsName0:String) : Boolean
      {
         return normalPartArr.indexOf(partsName0) >= 0;
      }
      
      public static function getThingsType(gripType0:String) : String
      {
         if(isNormalTypeB(gripType0))
         {
            return NORMAL;
         }
         return gripType0;
      }
      
      public static function samePan(gripType0:String, thingsType0:String) : Boolean
      {
         if(gripType0 == thingsType0)
         {
            return true;
         }
         if(isNormalTypeB(gripType0) && thingsType0 == NORMAL)
         {
            return true;
         }
         return false;
      }
      
      public static function supportType(d0:ArmsDefine, gripType0:String) : Boolean
      {
         if(gripType0 == stabler)
         {
            if(d0.shakeAngle == 0 && d0.shootAngle == 0)
            {
               return false;
            }
         }
         else if(gripType0 == sight)
         {
            if(d0.hitType != BulletDefine.LONG_LINE)
            {
               return false;
            }
         }
         else if(gripType0 == shooter)
         {
            if(ArmsType.getAttackGapAdd(d0.armsType) <= 0)
            {
               return false;
            }
         }
         return true;
      }
   }
}


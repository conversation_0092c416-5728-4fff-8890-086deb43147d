package UI.bag.allBag
{
   /**
    * AllBagUI性能优化器
    * 用于管理AllBagUI的性能配置和优化设置
    */
   public class AllBagUIOptimizer
   {
      private static var instance:AllBagUIOptimizer;
      
      // 性能配置
      public var enableTipsOnLoad:Boolean = false;      // 是否在加载时启用提示
      public var enableEditOnLoad:Boolean = false;      // 是否在加载时启用编辑
      public var maxItemsPerPage:int = 30;              // 每页最大物品数量
      public var delayedFeatureTime:int = 2000;         // 延迟启用功能的时间(毫秒)
      
      public function AllBagUIOptimizer()
      {
         if(instance != null)
         {
            throw new Error("AllBagUIOptimizer is a singleton!");
         }
      }
      
      public static function getInstance():AllBagUIOptimizer
      {
         if(instance == null)
         {
            instance = new AllBagUIOptimizer();
         }
         return instance;
      }
      
      /**
       * 应用性能优化配置到AllBagUI
       */
      public function applyOptimization(allBagUI:AllBagUI):void
      {
         if(allBagUI)
         {
            // 设置性能配置
            allBagUI.setPerformanceConfig(
               this.enableTipsOnLoad,
               this.enableEditOnLoad,
               this.maxItemsPerPage
            );
         }
      }
      
      /**
       * 设置高性能模式（最小功能，最快加载）
       */
      public function setHighPerformanceMode():void
      {
         this.enableTipsOnLoad = false;
         this.enableEditOnLoad = false;
         this.maxItemsPerPage = 20;
         this.delayedFeatureTime = 3000;
      }
      
      /**
       * 设置平衡模式（部分功能，中等加载速度）
       */
      public function setBalancedMode():void
      {
         this.enableTipsOnLoad = true;
         this.enableEditOnLoad = false;
         this.maxItemsPerPage = 30;
         this.delayedFeatureTime = 2000;
      }
      
      /**
       * 设置完整功能模式（所有功能，较慢加载）
       */
      public function setFullFeatureMode():void
      {
         this.enableTipsOnLoad = true;
         this.enableEditOnLoad = true;
         this.maxItemsPerPage = 50;
         this.delayedFeatureTime = 1000;
      }
      
      /**
       * 根据设备性能自动选择模式
       */
      public function autoSelectMode():void
      {
         // 这里可以根据实际的性能检测来选择模式
         // 目前默认使用平衡模式
         this.setBalancedMode();
      }
      
      /**
       * 获取当前配置的描述
       */
      public function getConfigDescription():String
      {
         var desc:String = "AllBagUI性能配置:\n";
         desc += "提示功能: " + (this.enableTipsOnLoad ? "启用" : "禁用") + "\n";
         desc += "编辑功能: " + (this.enableEditOnLoad ? "启用" : "禁用") + "\n";
         desc += "每页最大物品数: " + this.maxItemsPerPage + "\n";
         desc += "延迟启用时间: " + this.delayedFeatureTime + "ms";
         return desc;
      }
   }
}

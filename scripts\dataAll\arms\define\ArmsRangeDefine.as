package dataAll.arms.define
{
   import com.sounto.utils.ObjectMethod;
   import dataAll.arms.creator.ArmsDataCreator;
   import dataAll.arms.save.ArmsSave;
   import dataAll.bullet.*;
   import dataAll.pro.PropertyArrayDefine;
   
   public class ArmsRangeDefine
   {
      private static const laser1RangeObj:Object = {
         "attackGap":[0.3,0.35],
         "capacity":[6,9],
         "reloadGap":[2.1,2.5]
      };
      
      private var xml:XML;
      
      private var range:Object = {};
      
      private var otherRange:Object = {};
      
      public var def:ArmsDefine = new ArmsDefine();
      
      public function ArmsRangeDefine()
      {
         super();
      }
      
      public function get name() : String
      {
         return this.def.name;
      }
      
      public function clearRange() : void
      {
         this.range = {};
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var name0:String = null;
         var proStr0:String = null;
         var strArr0:Array = null;
         var d_pro0:* = undefined;
         var min0:Number = NaN;
         var max0:Number = NaN;
         this.xml = xml0;
         this.def.inData_byXML(xml0);
         var pro_arr:Array = ArmsSave.pro_arr;
         for(n in pro_arr)
         {
            name0 = pro_arr[n];
            proStr0 = xml0[name0];
            strArr0 = proStr0.split(",");
            if(this.def.hasOwnProperty(name0))
            {
               d_pro0 = this.def[name0];
               if(d_pro0 is int || d_pro0 is Number)
               {
                  min0 = Number(strArr0[0]);
                  max0 = min0;
                  if(strArr0.length > 1)
                  {
                     max0 = Number(strArr0[1]);
                  }
                  this.range[name0] = [min0,max0];
                  if(isNaN(this.def[name0]) || this.def[name0] == 0)
                  {
                     this.def[name0] = min0;
                  }
               }
            }
         }
      }
      
      public function clone() : ArmsRangeDefine
      {
         var d0:ArmsRangeDefine = new ArmsRangeDefine();
         d0.inData_byXML(this.xml);
         d0.def.armsType = this.def.armsType;
         d0.def.inAllImgName(this.def);
         return d0;
      }
      
      public function getSuperArmsSave(color0:String, position0:String) : ArmsSave
      {
         var n:* = undefined;
         var arr0:Array = null;
         var min0:Number = NaN;
         var max0:Number = NaN;
         var random_p0:Number = NaN;
         var maxBulletNum0:int = 0;
         var s0:ArmsSave = new ArmsSave();
         var attackSpeed_p:Number = 0;
         var capacity_p:Number = 0;
         for(n in this.range)
         {
            arr0 = this.range[n];
            min0 = Number(arr0[0]);
            max0 = Number(arr0[1]);
            random_p0 = 0;
            if(n == "attackGap")
            {
               if(position0 == "min")
               {
                  random_p0 = 1;
               }
               if(position0 == "mid")
               {
                  random_p0 = 0.5;
               }
               if(position0 == "max")
               {
                  random_p0 = 0;
               }
               attackSpeed_p = random_p0;
            }
            else if(n == "capacity")
            {
               capacity_p = random_p0;
            }
            else if(n == "reloadGap")
            {
               random_p0 = 1;
            }
            else if(n == "shakeAngle")
            {
               random_p0 = 1;
            }
            else if(n == "shootAngle")
            {
               random_p0 = 1;
            }
            s0[n] = min0 + random_p0 * (max0 - min0);
            if(n == "bulletNum")
            {
               maxBulletNum0 = ArmsType.getMaxBulletNum(this.def.armsType);
               if(s0[n] < maxBulletNum0)
               {
                  s0[n] = maxBulletNum0;
               }
            }
         }
         this.setSaveNumberByDef(s0);
         this.setOtherPro(s0,color0,attackSpeed_p,capacity_p);
         return s0;
      }
      
      public function getRangeArr(name0:String) : Array
      {
         var min0:Number = NaN;
         var max0:Number = NaN;
         var arr0:Array = this.range[name0];
         if(arr0 == null)
         {
            arr0 = this.otherRange[name0];
         }
         if(arr0 == null)
         {
            if(name0 == ArmsPro.shootSpeed)
            {
               arr0 = this.range[ArmsPro.attackGap];
               min0 = 1 / arr0[1];
               max0 = 1 / arr0[0];
               arr0 = [min0,max0];
               this.otherRange[name0] = arr0;
            }
            else if(name0 == ArmsPro.precision)
            {
               arr0 = [0,1];
               this.otherRange[name0] = arr0;
            }
            else if(name0 == ArmsPro.shootWidth)
            {
               arr0 = this.range[ArmsPro.bulletWidth].concat();
               arr0[0] = ArmsDataCreator.getShootRange(this.def.armsType,this.def.hitType,arr0[0],this.def.bulletLife,this.def.bulletSpeed,this.def.aiShootRange);
               arr0[1] = ArmsDataCreator.getShootRange(this.def.armsType,this.def.hitType,arr0[1],this.def.bulletLife,this.def.bulletSpeed,this.def.aiShootRange);
               arr0[0] = ArmsDataCreator.getShowShootRange(arr0[0]);
               arr0[1] = ArmsDataCreator.getShowShootRange(arr0[1]);
               this.otherRange[name0] = arr0;
            }
         }
         return arr0;
      }
      
      public function getRangeGather(name0:String) : String
      {
         var min0:Number = NaN;
         var max0:Number = NaN;
         var proD0:PropertyArrayDefine = null;
         var s0:String = null;
         var proDefineName0:String = name0;
         if(name0 == "shootSpeed")
         {
            proDefineName0 = name0;
            name0 = "attackGap";
         }
         var arr0:Array = this.getRangeArr(name0);
         if(Boolean(arr0))
         {
            if(arr0.length >= 2)
            {
               min0 = Number(arr0[0]);
               max0 = Number(arr0[1]);
               if(max0 > min0)
               {
                  proD0 = ArmsTipDefine.getDefine(proDefineName0);
                  if(Boolean(proD0))
                  {
                     s0 = "";
                     if(name0 == "attackGap")
                     {
                        s0 = proD0.getValueString(1 / max0,false,"",true) + "~" + proD0.getValueString(1 / min0,false,"",true);
                     }
                     else
                     {
                        s0 = proD0.getValueString(min0,false,"") + "~" + proD0.getValueString(max0,false,"");
                     }
                     return "<gray2 (" + s0 + ")";
                  }
               }
            }
         }
         return "";
      }
      
      public function getArmsSave(color0:String) : ArmsSave
      {
         var n:* = undefined;
         var arr0:Array = null;
         var min0:Number = NaN;
         var max0:Number = NaN;
         var random_p0:Number = NaN;
         var newArr0:Array = null;
         var s0:ArmsSave = new ArmsSave();
         var attackSpeed_p:Number = 0;
         var capacity_p:Number = 0;
         for(n in this.range)
         {
            arr0 = this.range[n];
            min0 = Number(arr0[0]);
            max0 = Number(arr0[1]);
            if(this.name == "laser1")
            {
               if(laser1RangeObj.hasOwnProperty(n))
               {
                  newArr0 = laser1RangeObj[n];
                  min0 = Number(newArr0[0]);
                  max0 = Number(newArr0[1]);
               }
            }
            random_p0 = Math.random();
            s0[n] = min0 + random_p0 * (max0 - min0);
            if(n == "attackGap")
            {
               attackSpeed_p = random_p0;
            }
            else if(n == "capacity")
            {
               capacity_p = random_p0;
            }
         }
         this.setSaveNumberByDef(s0);
         this.setOtherPro(s0,color0,attackSpeed_p,capacity_p);
         return s0;
      }
      
      private function setSaveNumberByDef(s0:ArmsSave) : void
      {
         var pro0:* = null;
         var v0:* = undefined;
         if(ObjectMethod.isZeroB(this.range))
         {
            for each(pro0 in ArmsSave.pro_arr)
            {
               if(this.def.hasOwnProperty(pro0))
               {
                  v0 = this.def[pro0];
                  if(v0 is Number || v0 is int)
                  {
                     s0[pro0] = v0;
                  }
               }
            }
         }
      }
      
      private function setOtherPro(s0:ArmsSave, color0:String, attackSpeed_p:Number, capacity_p:Number) : void
      {
         s0.name = this.def.name;
         s0.bounceD = this.def.bounceD.copy();
         s0.followD = this.def.followD.copy();
         s0.critD = this.def.critD.copy();
         s0.setArmsImgLabel(this.def.getRandomImageLabel(color0,attackSpeed_p,capacity_p),this.def.shootSoundUrl);
         if(this.def.shootSoundUrl != "")
         {
            s0.shootSoundUrl = this.def.shootSoundUrl;
         }
         s0.fleshCnByArmsImgLabel();
      }
   }
}


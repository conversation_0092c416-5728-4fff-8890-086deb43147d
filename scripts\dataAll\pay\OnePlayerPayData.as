package dataAll.pay
{
   import com.adobe.serialization.json.JSON2;
   import com.common.data.Base64;
   
   public class OnePlayerPayData
   {
      public var uid:String = "";
      
      public var index:int = 0;
      
      public var obj:Object = {};
      
      public var rank:int = 0;
      
      public var saveIndex:int = 0;
      
      public var pay:int = 0;
      
      public function OnePlayerPayData()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         this.uid = xml0.uid;
         this.rank = int(xml0.rank);
         this.saveIndex = int(xml0.saveIndex);
         this.pay = int(xml0.pay);
         var pay0:String = Base64.decodeString(xml0.extra);
         this.obj = JSON2.decode(pay0);
      }
      
      public function inData_obj(obj0:Object) : void
      {
         this.uid = obj0.uid;
         this.saveIndex = obj0.index;
         this.rank = obj0.rank;
         this.pay = obj0.score;
         this.obj = JSON2.decode(obj0.extra);
      }
   }
}


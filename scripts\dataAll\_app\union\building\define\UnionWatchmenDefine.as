package dataAll._app.union.building.define
{
   import com.adobe.serialization.json.JSON2;
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.skill.define.SkillDefine;
   
   public class UnionWatchmenDefine
   {
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var effectType:String = "";
      
      public var value:String = "";
      
      public var info:String = "";
      
      public var type:String = "";
      
      public function UnionWatchmenDefine()
      {
         super();
         this.unlockLv = 10;
      }
      
      public function get unlockLv() : Number
      {
         return this.CF.getAttribute("unlockLv");
      }
      
      public function set unlockLv(v0:Number) : void
      {
         this.CF.setAttribute("unlockLv",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         if(this.value == "")
         {
            this.value = this.name;
         }
         if(this.type == "")
         {
            this.type = this.name;
         }
      }
      
      public function fleshDefine() : void
      {
         var d0:SkillDefine = null;
         if(this.info == "" && this.effectType == "skill")
         {
            d0 = Gaming.defineGroup.skill.getDefine(this.value);
            this.info = d0.getDescription(true);
         }
      }
      
      public function getInfo(unlockB0:Boolean = false) : String
      {
         var str0:String = "";
         if(unlockB0)
         {
            str0 = "<green <b>已解锁</b>/>";
         }
         else
         {
            str0 = "<purple <b>饲养等级到达" + this.unlockLv + "级才可解锁该效果</b>/>";
         }
         str0 += "\n\n";
         return str0 + ("<blue 效果说明：/>\n" + this.info);
      }
      
      public function getSkillLabel() : String
      {
         if(this.effectType == "skill")
         {
            return this.value;
         }
         return "";
      }
      
      public function getAddObj() : Object
      {
         var json0:String = null;
         var str0:String = null;
         var obj0:Object = {};
         if(this.effectType == "pro")
         {
            json0 = this.value;
            if(json0 != "")
            {
               str0 = TextWay.replaceStr(json0,"\'","\"");
               obj0 = JSON2.decode(str0);
            }
         }
         return obj0;
      }
   }
}


package dataAll.things.define
{
   import com.adobe.serialization.json.JSON2;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.parts.PartsCreator;
   import dataAll._app.parts.define.PartsConst;
   import dataAll._app.parts.define.PartsType;
   import dataAll._player.PlayerData;
   import dataAll._player.state.define.PlayerStateDefine;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.equip.creator.BlackEquipComposeCtrl;
   import dataAll.equip.define.EquipDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.define.IO_GiftDefine;
   import dataAll.items.creator.ItemsComposeCtrl;
   import dataAll.items.define.IO_ComposeItemsDefine;
   import dataAll.items.define.IO_ItemsDefine;
   
   public class ThingsDefine implements IO_GiftDefine, IO_ItemsDefine
   {
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var baseLabel:String = "";
      
      public var addDropDefineB:Boolean = false;
      
      public var zuobiNum:int = 0;
      
      public var objType:String = "";
      
      public var skillArr:Array = [];
      
      public var maxLevel:int = 0;
      
      public var index:int = 0;
      
      public var father:String = "";
      
      public var secType:String = "";
      
      public var iconUrl:String = "";
      
      public var hideB:Boolean = false;
      
      public var itemsLevel:int = 1;
      
      public var dropLevelArr:Array = [];
      
      public var noOverlayB:Boolean = false;
      
      public var blackCanConverB:Boolean = true;
      
      public var canSellB:Boolean = false;
      
      public var btnList:Array = [0];
      
      public var useSave:String = "";
      
      public var effectD:ThingsEffectDefine = new ThingsEffectDefine();
      
      public var giftRandomB:Boolean = false;
      
      public var showGiftB:Boolean = false;
      
      public var giftD:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      public var stateD:PlayerStateDefine = null;
      
      public var loveD:ThingsLoveDefine = new ThingsLoveDefine();
      
      public var smeltD:ThingsSmeltDefine = new ThingsSmeltDefine();
      
      public var description:String = "";
      
      private var _o:Object = null;
      
      public function ThingsDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML, father0:String) : void
      {
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         this.index = int(xml0.@index);
         this.father = father0;
         if(this.baseLabel == "")
         {
            this.baseLabel = this.name;
         }
         this.effectD.inData_byXML(xml0["effectD"][0]);
         this.giftD.inAllData_byXML(xml0["gift"],this.giftRandomB);
         if(this.giftD.name == "")
         {
            this.giftD.name = this.name;
         }
         this.loveD.inData_byXML(xml0["loveD"][0]);
         this.smeltD.inData_byXML(xml0["smeltD"][0]);
         if(this.iconUrl == "")
         {
            this.iconUrl = "ThingsIcon/" + this.name;
         }
         if(father0 == "parts")
         {
            if(this.iconUrl == "")
            {
               this.iconUrl = "ThingsIcon/" + this.name;
            }
         }
         if(String(xml0.@o) != "")
         {
            this._o = JSON2.decode(TextWay.replaceStr(String(xml0.@o),"\'","\""));
         }
         if(xml0.stateD.length() > 0)
         {
            this.stateD = new PlayerStateDefine();
            this.stateD.name = this.name;
            this.stateD.iconUrl = this.iconUrl + "16";
            this.stateD.inData_byXML(xml0.stateD[0],this.father);
            Gaming.defineGroup.playerState.outAddDefine(this.stateD);
            this.effectD.type = "addState";
         }
         this.fleshDescription();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function inFatherXml(x0:XML) : void
      {
         this.smeltD.type = x0.@smeltType;
      }
      
      public function getName() : String
      {
         return this.name;
      }
      
      public function getCnName() : String
      {
         return this.cnName;
      }
      
      public function getTypeCn() : String
      {
         if(this.isPartsB())
         {
            return "零件";
         }
         return "物品";
      }
      
      public function getSortType() : String
      {
         var type0:String = this.smeltD.type;
         if(type0 == ThingsSmeltType.pet)
         {
            type0 = ThingsSmeltType.stone;
         }
         if(type0 == ThingsSmeltType.currency)
         {
            type0 = ThingsSmeltType.stone;
         }
         return type0;
      }
      
      public function fleshDescription() : void
      {
         if(this.description != "")
         {
            this.description = TextWay.toHan2(this.description);
            this.description = TextWay.replaceStr(this.description,"[n]","\n");
            this.description = TextWay.replaceStr(this.description,"{","<");
            this.description = TextWay.replaceStr(this.description,"}",">");
         }
      }
      
      public function fleshRandomDescription() : void
      {
         if(this.getShowGiftB())
         {
            this.description += "";
            this.description += "\n";
            this.description += this.giftD.getDescription(1,true,true);
            this.description += "\n\n" + ComMethod.color("<b>按下F键查看物品详情</b>","#FF9900");
         }
      }
      
      public function getBtnList() : Array
      {
         if(this.btnList[0] == 0)
         {
            return null;
         }
         return this.btnList;
      }
      
      public function isPropsB() : Boolean
      {
         return this.father == ThingsType.PROPS;
      }
      
      public function getShowGiftB() : Boolean
      {
         return this.showGiftB || this.giftRandomB;
      }
      
      public function isBlackChip() : Boolean
      {
         return this.father == "blackChip";
      }
      
      public function canConverB() : Boolean
      {
         return this.secType == "equip" && this.itemsLevel < 91 && this.father == ThingsType.BLACK_CHIP && this.name.indexOf("oracleSuit") == -1;
      }
      
      public function canAutoUseB() : Boolean
      {
         return this.effectD.autoUseCondition != "";
      }
      
      public function haveChipTipB() : Boolean
      {
         return this.father == ThingsType.BLACK_CHIP || this.father == ThingsType.RARE_CHIP;
      }
      
      public function isNormalChipB() : Boolean
      {
         return this.father == ThingsType.NORMAL_CHIP;
      }
      
      public function canRefiningB() : Boolean
      {
         var plv0:int = 0;
         if(this.father == ThingsType.BLACK_CHIP)
         {
            return this.itemsLevel < 86;
         }
         if(this.isPartsChestB())
         {
            plv0 = this.getPartsChestLv();
            if(plv0 >= 75 && plv0 <= 87)
            {
               return true;
            }
         }
         return false;
      }
      
      public function isSmeltMaterialB() : Boolean
      {
         if(this.isPartsB() == false)
         {
            if(this.smeltD.havePriceB)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getComposeDefine() : ThingsComposeDefine
      {
         return Gaming.defineGroup.things.getThingsComposeDefine(this.name);
      }
      
      public function getComposeItemsDefine() : IO_ComposeItemsDefine
      {
         var equipD0:EquipDefine = Gaming.defineGroup.equip.getDefine(this.name);
         if(Boolean(equipD0))
         {
            return equipD0;
         }
         var armsD0:ArmsDefine = Gaming.defineGroup.bullet.getArmsDefineInRange(this.name);
         if(Boolean(armsD0))
         {
            return armsD0;
         }
         return null;
      }
      
      public function getAffterUseSaveType() : String
      {
         if(this.useSave != "")
         {
            return this.useSave;
         }
         if(this.isPartsChestB())
         {
            return ThingsUseSave.no;
         }
         if(this.name.indexOf("Box") > 0)
         {
            return ThingsUseSave.box;
         }
         if(this.name.indexOf("Chest") > 0 && this.name != "tigerChest")
         {
            return ThingsUseSave.chest;
         }
         return ThingsUseSave.no;
      }
      
      public function isShopAutoUseB() : Boolean
      {
         return this.useSave == ThingsUseSave.shopAuto;
      }
      
      public function getGatherTip(pd0:PlayerData = null) : String
      {
         var str0:String = null;
         if(this.isPartsB())
         {
            return PartsCreator.getPartsGatherText(this);
         }
         if(this.haveChipTipB())
         {
            return BlackEquipComposeCtrl.getThingsTipAll(this,pd0);
         }
         if(this.isNormalChipB())
         {
            return ItemsComposeCtrl.getTip(this,pd0);
         }
         str0 = "";
         return str0 + ("<blue " + this.description + "/>");
      }
      
      public function isPartsB() : Boolean
      {
         return this.father == ThingsType.PARTS;
      }
      
      public function isPartsSpecialB() : Boolean
      {
         return this.objType == PartsType.special || this.objType == PartsType.skill;
      }
      
      public function isPartsRareB() : Boolean
      {
         return this.objType == PartsType.rare;
      }
      
      public function isPartsNormalB() : Boolean
      {
         return this.objType == PartsType.NORMAL;
      }
      
      public function toTrace() : String
      {
         return this.cnName + "：  " + this.smeltD.price;
      }
      
      public function getAddObj() : Object
      {
         return this._o;
      }
      
      public function isPartsChestB() : Boolean
      {
         return this.name.indexOf(ThingsType.PARTS_CHEST) >= 0;
      }
      
      private function getPartsChestLv() : int
      {
         return int(this.name.replace(ThingsType.PARTS_CHEST,""));
      }
      
      public function getPartsChestRefiningGift(n0:int = 1) : GiftAddDefineGroup
      {
         var one0:int = 0;
         var num0:int = 0;
         var name0:String = null;
         var g0:GiftAddDefineGroup = null;
         var lv0:int = this.getPartsChestLv();
         if(lv0 >= 72)
         {
            one0 = Math.round(PartsConst.getComposeMustNum(lv0 - 3) / 1.5);
            num0 = one0 * n0;
            name0 = ThingsType.PARTS_CHEST + (lv0 - 3);
            g0 = new GiftAddDefineGroup();
            g0.addGiftByStr("things;" + name0 + ";" + num0);
            return g0;
         }
         return null;
      }
      
      public function getGiftCn() : String
      {
         if(this.isPartsB())
         {
            return this.cnName + "(" + this.itemsLevel + "级)";
         }
         return this.cnName;
      }
      
      public function getGiftTip() : String
      {
         return this.getGatherTip();
      }
      
      public function getGiftIconUrl() : String
      {
         return this.iconUrl;
      }
   }
}


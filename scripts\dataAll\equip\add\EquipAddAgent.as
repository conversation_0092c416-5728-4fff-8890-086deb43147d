package dataAll.equip.add
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ObjectMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._player.PlayerData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.define.EquipPro;
   import dataAll.pro.PropertyArrayDefine;
   
   public class EquipAddAgent
   {
      public var def:PropertyArrayDefine;
      
      private var arr:Array = [];
      
      private var obj:Object = {};
      
      private var more50B:Boolean = false;
      
      public function EquipAddAgent()
      {
         super();
      }
      
      private static function getChildShowB(pro0:String, v0:Number, max0:Number, dataName0:String) : Boolean
      {
         if(v0 > 0)
         {
            return true;
         }
         if(max0 > 0)
         {
            return true;
         }
         return false;
      }
      
      public function haveDataB() : Boolean
      {
         return this.arr.length > 0;
      }
      
      public function canShowB(pd0:NormalPlayerData) : Boolean
      {
         if(this.arr.length == 0)
         {
            if(pd0 is PlayerData == false)
            {
               return false;
            }
            if(EquipPro.noProNoShowArr.indexOf(this.def.name) >= 0)
            {
               return false;
            }
         }
         return true;
      }
      
      public function inData(d0:PropertyArrayDefine, dataNameArr0:Array, addOO:Object, tipOO:Object, pd0:NormalPlayerData) : void
      {
         var dataName0:* = null;
         var getter0:IO_EquipAddGetter = null;
         var vv0:* = undefined;
         var v0:Number = NaN;
         var max0:Number = NaN;
         var maxObj0:Object = null;
         var tipGetter0:IO_EquipAddTipGetter = null;
         var showB0:Boolean = false;
         var child0:EquipAddChild = null;
         this.def = d0;
         var pro0:String = d0.name;
         for each(dataName0 in dataNameArr0)
         {
            getter0 = pd0.getEquipAddGetter(dataName0);
            if(Boolean(getter0))
            {
               vv0 = ObjectMethod.getProByTwo(addOO,dataName0,pro0);
               v0 = vv0 is Number ? vv0 : 0;
               max0 = 0;
               maxObj0 = null;
               tipGetter0 = getter0 as IO_EquipAddTipGetter;
               if(Boolean(tipGetter0))
               {
                  maxObj0 = tipGetter0.getProAddMaxObj(pro0);
                  if(Boolean(maxObj0))
                  {
                     max0 = Number(maxObj0.v);
                  }
               }
               showB0 = getChildShowB(pro0,v0,max0,dataName0);
               if(showB0)
               {
                  child0 = new EquipAddChild();
                  child0.def = d0;
                  child0.dataFrom = dataName0;
                  child0.value = v0;
                  child0.max = max0;
                  child0.maxObj = maxObj0;
                  child0.tipArr = ObjectMethod.getProByTwo(tipOO,dataName0,pro0);
                  this.arr.push(child0);
                  this.obj[dataName0] = child0;
               }
            }
         }
         this.more50B = pd0.isMore50Pro(pro0);
      }
      
      public function getTip() : String
      {
         return this.def.info;
      }
      
      public function getChild(dataName0:String) : EquipAddChild
      {
         return this.obj[dataName0];
      }
      
      public function getTitleText() : String
      {
         var s0:String = this.def.cnName;
         var link0:String = this.def.name;
         s0 = TextMethod.link(s0,link0);
         if(EquipPro.startArr.indexOf(this.def.name) >= 0)
         {
            s0 = ComMethod.color("·","#00FFBF") + s0;
         }
         else
         {
            s0 = "  " + s0;
         }
         return s0;
      }
      
      public function getText() : String
      {
         var child0:EquipAddChild = null;
         var allS0:String = null;
         var sumMax0:Number = NaN;
         var s0:String = "";
         var all0:Number = 0;
         for each(child0 in this.arr)
         {
            if(s0 != "")
            {
               s0 += " ";
            }
            all0 += child0.value;
            s0 += child0.getText();
         }
         allS0 = "";
         if(all0 > 0)
         {
            allS0 += "<b>" + ComMethod.blue(this.def.getValueString(all0)) + "</b>";
         }
         if(EquipPro.bagDropArr.indexOf(this.def.name) >= 0)
         {
            sumMax0 = EquipPro.getSumMax(this.def.name);
            allS0 += ComMethod.gray2("(装备上限" + this.def.getValueString(sumMax0,false,"") + ",背包可生效)");
         }
         else if(this.more50B)
         {
            allS0 += ComMethod.color("(衰减50%)","#A854A8");
         }
         if(allS0 != "")
         {
            s0 = allS0 + " " + s0;
         }
         return s0;
      }
   }
}


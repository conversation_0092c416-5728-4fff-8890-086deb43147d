package dataAll.equip.creator
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import dataAll.arms.creator.ArmsStrengthenCtrl;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSave;
   import dataAll.ui.tip.CheckData;
   
   public class EquipStrengthenCtrl
   {
      private static var CF:OldNiuBiCF = new OldNiuBiCF();
      
      private static var canTypeArr:Array = [EquipType.HEAD,EquipType.COAT,EquipType.PANTS,EquipType.BELT];
      
      public function EquipStrengthenCtrl()
      {
         super();
      }
      
      public static function get maxAddLevel() : Number
      {
         return CF.getAttribute("maxAddLevel");
      }
      
      public static function set maxAddLevel(v0:Number) : void
      {
         CF.setAttribute("maxAddLevel",v0);
      }
      
      public static function init() : void
      {
         maxAddLevel = 28;
      }
      
      public static function getAfterData(da0:EquipData) : EquipData
      {
         var affter_s0:EquipSave = da0.save.clone();
         ++affter_s0.strengthenLv;
         var affter_da0:EquipData = affter_s0.getDataClass();
         affter_da0.inData_bySave(affter_s0,null,null);
         return affter_da0;
      }
      
      public static function strengthenOne(da0:EquipData, oneB0:Boolean) : void
      {
         var s0:EquipSave = da0.save;
         if(oneB0)
         {
            ++s0.strengthenLv;
         }
         else
         {
            s0.strengthenLv = getStrengthenLv(s0.strengthenLv);
         }
         s0.lockB = true;
         s0.fleshSMaxLv();
      }
      
      public static function getStrengthenLv(nowLv0:int, max0:int = -1) : int
      {
         var ran0:Number = NaN;
         if(max0 == -1)
         {
            max0 = maxAddLevel;
         }
         var ranMax0:int = ArmsStrengthenCtrl.getStrenRanMax();
         if(nowLv0 < ranMax0)
         {
            max0 = ranMax0;
            ran0 = Math.random();
            if(ran0 <= 0.5)
            {
               nowLv0 += 1;
            }
            else if(ran0 <= 0.8)
            {
               nowLv0 += 2;
            }
            else
            {
               nowLv0 += 3;
            }
         }
         else
         {
            nowLv0 += 1;
         }
         if(nowLv0 > max0)
         {
            nowLv0 = max0;
         }
         return nowLv0;
      }
      
      public static function downStrengthenOne(da0:EquipData) : void
      {
         var s0:EquipSave = da0.save;
         var lv0:int = s0.strengthenLv;
         lv0--;
         if(lv0 < 0)
         {
            lv0 = 0;
         }
         s0.strengthenLv = lv0;
         s0.lockB = true;
      }
      
      public static function getSuccessRate(lv0:int) : Number
      {
         return Gaming.defineGroup.equip.strengthen.getPropertyValue("successRate",lv0 + 1);
      }
      
      public static function panStrengthenB(da0:EquipData) : CheckData
      {
         var c0:CheckData = new CheckData();
         if(da0.save.getTrueLevel() < 50)
         {
            c0.bb = false;
            c0.info = "<red <b>装备等级必须到达50级。</b>/>";
         }
         else if(canTypeArr.indexOf(da0.save.partType) == -1)
         {
            c0.bb = false;
            c0.info = "<red <b>暂时只能强化战衣、战裤、头盔。</b>/>";
         }
         else if(da0.save.strengthenLv >= maxAddLevel)
         {
            c0.bb = false;
            c0.info = "<green <b>装备已经强化至最高等级。</b>/>";
         }
         return c0;
      }
      
      public static function canStrengthenB(da0:EquipData) : Boolean
      {
         var c0:CheckData = panStrengthenB(da0);
         return c0.bb;
      }
      
      public static function canStrengthenMoveB(da0:EquipData) : Boolean
      {
         if(da0.save.getTrueLevel() < 50 || canTypeArr.indexOf(da0.save.partType) == -1)
         {
            return false;
         }
         return true;
      }
      
      public static function getNewObj(s0:EquipSave) : Object
      {
         var obj0:Object = s0.obj;
         var addObj0:Object = getProByPartType(s0.partType,s0.strengthenLv);
         return ComMethod.fixedObj(obj0,addObj0);
      }
      
      public static function getProByPartType(type0:String, strengthenLv0:int) : Object
      {
         var obj0:Object = {};
         var v0:Number = Gaming.defineGroup.equip.strengthen.getPropertyValue("addMul",strengthenLv0);
         if(type0 == EquipType.COAT)
         {
            obj0["lifeMul"] = v0;
         }
         else if(type0 == EquipType.PANTS)
         {
            obj0["dpsMul"] = v0;
         }
         else if(type0 == EquipType.HEAD)
         {
            obj0["lifeRateMul"] = v0;
            obj0["skillDedut"] = Gaming.defineGroup.equip.strengthen.getPropertyValue("skillDedutAddMul",strengthenLv0);
         }
         else if(type0 == EquipType.BELT)
         {
            obj0["bulletDedut"] = Number(Number(strengthenLv0 * 0.01).toFixed(2));
         }
         return obj0;
      }
   }
}


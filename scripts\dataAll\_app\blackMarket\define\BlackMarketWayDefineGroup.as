package dataAll._app.blackMarket.define
{
   public class BlackMarketWayDefineGroup
   {
      public var obj:Object = {};
      
      public var arr:Array = [];
      
      public function BlackMarketWayDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var thingsXML0:XML = null;
         var d0:BlackMarketWayDefine = null;
         var fatherXML0:XMLList = xml0.way;
         for(i in fatherXML0)
         {
            thingsXML0 = fatherXML0[i];
            d0 = new BlackMarketWayDefine();
            d0.inData_byXML(thingsXML0);
            this.obj[d0.name] = d0;
            this.arr.push(d0);
         }
      }
      
      public function getDefine(name0:String) : BlackMarketWayDefine
      {
         return this.obj[name0];
      }
   }
}


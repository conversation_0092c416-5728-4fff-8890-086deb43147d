package UI.bag
{
   import UI.NormalUICtrl;
   import UI.bag.find.ItemsFindCtrlBox;
   import UI.bag.moreChoose.ItemsMoreCtrlBox;
   import UI.bag.parts.PartsComposeBox;
   import UI.bag.ItemsEditCtrl;
   import UI.bag.ItemsEditManager;
   import UI.base.AppNormalUI;
   import UI.base.CheckText;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.label.LabelBox;
   import UI.base.tip.TipBox;
   import dataAll._app.parts.PartsMethod;
   import dataAll.items.ItemsDataGroup;
   import dataAll.pet.PetCount;
   import dataAll.pet.gene.GeneDataGroup;
   import dataAll.pet.gene.creator.GeneDataDecompose;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class BagUI extends AppNormalUI
   {
      public var labelBox:LabelBox = new LabelBox();
      
      public var armsBox:ItemsGripBox = new ItemsGripBox();
      
      public var equipBox:ItemsGripBox = new ItemsGripBox();
      
      public var thingsBox:ItemsGripBox = new ItemsGripBox();
      
      public var partsBox:ItemsGripBox = new ItemsGripBox();
      
      public var geneBox:ItemsGripBox = new ItemsGripBox();
      
      private var nowBox:ItemsGripBox = null;
      
      public var boxArr:Array = [this.armsBox,this.equipBox,this.thingsBox,this.partsBox,this.geneBox];
      
      public var sortBtn:NormalBtn = new NormalBtn();
      
      public var sortBtn2:NormalBtn = new NormalBtn();
      
      public var delBtn:NormalBtn = new NormalBtn();
      
      public var composeBtn:NormalBtn = new NormalBtn();
      
      public var closeBtn:SimpleButton;
      
      public var sortBtnSp:MovieClip = null;
      
      public var sortBtnSp2:MovieClip = null;
      
      public var delBtnSp:MovieClip = null;
      
      public var composeBtnSp:MovieClip = null;
      
      public var labelTag:Sprite = null;
      
      public var gripTag:Sprite = null;
      
      public var armsPageTag:Sprite = null;
      
      public var equipPageTag:Sprite = null;
      
      public var titleTxt:TextField;
      
      public var infoTxt:TextField;
      
      public var partsComposeBoxSp:Sprite;
      
      public var ctrl:BagFleshCtrl = new BagFleshCtrl();
      
      public var partsComposeBox:PartsComposeBox = new PartsComposeBox();
      
      public var moreBtnSp:MovieClip = null;
      
      public var moreBtn:NormalBtn = new NormalBtn();
      
      private var itemsMore:ItemsMoreCtrlBox = new ItemsMoreCtrlBox();
      
      private var findBtnSp:MovieClip = null;
      
      private var findBtn:NormalBtn = new NormalBtn();
      
      private var itemsFind:ItemsFindCtrlBox = new ItemsFindCtrlBox();

      // 编辑按钮
      private var editButton:NormalBtn = null;

      public function BagUI()
      {
         super();
         addChild(this.labelBox);
         addChild(this.armsBox);
         addChild(this.equipBox);
         addChild(this.thingsBox);
         addChild(this.partsBox);
         addChild(this.geneBox);
         addChild(this.sortBtn);
         addChild(this.sortBtn2);
         addChild(this.delBtn);
         addChild(this.composeBtn);
         this.labelBox.arg.init(5,1,-6,0);
         this.armsBox.imgType = "armsGrip";
         this.armsBox.arg.init(2,4,3,5);
         this.armsBox.evt.setWantEvent(true,true,true,true,true,true);
         this.equipBox.imgType = "equipGrip";
         this.equipBox.arg.init(6,5,2,3);
         this.equipBox.evt.setWantEvent(true,true,true,true,true,true);
         this.thingsBox.imgType = "equipGrip";
         this.thingsBox.arg.init(6,5,2,3);
         this.thingsBox.evt.setWantEvent(true,true,true,true,true,true);
         this.partsBox.imgType = "equipGrip";
         this.partsBox.arg.init(6,5,2,3);
         this.partsBox.evt.setWantEvent(true,true,true,true,true,true);
         this.geneBox.imgType = "equipGrip";
         this.geneBox.arg.init(6,5,2,3);
         this.geneBox.evt.setWantEvent(true,true,true,true,true,true);
         ItemsGripMoveCtrl.addEvent_byItemsGripBox(this.armsBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.armsBox);
         ItemsGripUnlockCtrl.addEvent_byItemsGripBox(this.armsBox);
         ItemsEditManager.registerBox(this.armsBox);
         ItemsGripMoveCtrl.addEvent_byItemsGripBox(this.equipBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.equipBox);
         ItemsGripUnlockCtrl.addEvent_byItemsGripBox(this.equipBox);
         ItemsEditManager.registerBox(this.equipBox);
         ItemsGripMoveCtrl.addEvent_byItemsGripBox(this.thingsBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.thingsBox);
         ItemsGripUnlockCtrl.addEvent_byItemsGripBox(this.thingsBox);
         ItemsGripMoveCtrl.addEvent_byItemsGripBox(this.partsBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.partsBox);
         ItemsGripUnlockCtrl.addEvent_byItemsGripBox(this.partsBox);
         ItemsGripMoveCtrl.addEvent_byItemsGripBox(this.geneBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.geneBox);
         ItemsGripUnlockCtrl.addEvent_byItemsGripBox(this.geneBox);
         this.sortBtn.addEventListener(MouseEvent.CLICK,this.sortBagClick);
         ItemsGripTipCtrl.addNormalBtnTip(this.sortBtn);
         this.sortBtn2.addEventListener(MouseEvent.CLICK,this.sortBagClick2);
         this.delBtn.addEventListener(MouseEvent.CLICK,this.batchSellClick);
         this.delBtn.addEventListener(MouseEvent.MOUSE_OVER,this.batchSellOver);
         this.delBtn.addEventListener(MouseEvent.MOUSE_OUT,this.batchSellOut);
         this.composeBtn.addEventListener(MouseEvent.CLICK,this.composeClick);
         this.composeBtn.addEventListener(MouseEvent.MOUSE_OVER,this.batchSellOver);
         this.composeBtn.addEventListener(MouseEvent.MOUSE_OUT,this.batchSellOut);
         this.moreBtn.addEventListener(MouseEvent.CLICK,this.moreBtnClick);
         this.findBtn.addEventListener(MouseEvent.CLICK,this.findBtnClick);
         FontDeal.dealOne(this.titleTxt);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["titleTxt","findBtnSp","moreBtnSp","partsComposeBoxSp","infoTxt","closeBtn","sortBtnSp2","sortBtnSp","delBtnSp","composeBtnSp","labelTag","gripTag","armsPageTag","equipPageTag"];
         super.setImg(img0);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
         addChildAt(img,0);
         NormalUICtrl.setTag(this.labelBox,this.labelTag);
         NormalUICtrl.setTag(this.armsBox,this.gripTag);
         NormalUICtrl.setTag(this.equipBox,this.gripTag);
         NormalUICtrl.setTag(this.thingsBox,this.gripTag);
         NormalUICtrl.setTag(this.partsBox,this.gripTag);
         NormalUICtrl.setTag(this.geneBox,this.gripTag);
         this.labelBox.inData("bagLabelBtn",["arms","equip","things","gene","parts"],["武器","装备","物品","基因体","零件"]);
         this.labelBox.setChoose("arms");
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         this.armsBox.pageBox.setToNormalBtn();
         this.equipBox.pageBox.setToNormalBtn();
         this.thingsBox.pageBox.setToNormalBtn();
         this.partsBox.pageBox.setToNormalBtn();
         this.geneBox.pageBox.setToNormalBtn();
         this.armsBox.pageBox.x = this.armsPageTag.x - this.gripTag.x;
         this.armsBox.pageBox.y = this.armsPageTag.y - this.gripTag.y;
         this.equipBox.pageBox.x = this.armsBox.pageBox.x;
         this.equipBox.pageBox.y = this.armsBox.pageBox.y;
         this.thingsBox.pageBox.x = this.armsBox.pageBox.x;
         this.thingsBox.pageBox.y = this.armsBox.pageBox.y;
         this.partsBox.pageBox.x = this.armsBox.pageBox.x;
         this.partsBox.pageBox.y = this.armsBox.pageBox.y;
         this.geneBox.pageBox.x = this.armsBox.pageBox.x;
         this.geneBox.pageBox.y = this.armsBox.pageBox.y;
         this.sortBtn.setImg(this.sortBtnSp);
         this.sortBtn2.setImg(this.sortBtnSp2);
         this.delBtn.setImg(this.delBtnSp);
         this.findBtn.setImg(this.findBtnSp);
         addChild(this.findBtn);
         this.composeBtn.setImg(this.composeBtnSp);
         this.delBtn.setName("一键卖出");
         addChild(this.infoTxt);
         this.showInfoText("");
         addChild(this.partsComposeBox);
         this.partsComposeBox.setImg(this.partsComposeBoxSp);
         this.moreBtn.setImg(this.moreBtnSp);
         this.itemsMore.setToNormalImg();
         addChild(this.itemsMore);
         addChild(this.moreBtn);
         this.itemsMore.x = this.moreBtn.x + this.moreBtn.width + 6 - this.itemsMore.width;
         this.itemsMore.y = this.moreBtn.y - 6;
         this.itemsMore.hide();
         this.itemsFind.setToNormalImg();
         this.itemsFind.x = 6;
         this.itemsFind.y = 413;
         addChild(this.itemsFind);
         this.itemsFind.hide();

         // 创建编辑按钮
         this.createEditButton();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }

      // 创建编辑按钮
      private function createEditButton() : void
      {
         if(!this.editButton)
         {
            this.editButton = new NormalBtn();
            this.editButton.addEventListener(MouseEvent.CLICK, this.editButtonClick);

            addChild(this.editButton);

            try
            {
               this.editButton.setName("编辑");
               this.editButton.width = 40;
               this.editButton.height = 20;

               this.editButton.graphics.clear();
               this.editButton.graphics.beginFill(0xFF6600, 0.9);
               this.editButton.graphics.drawRoundRect(0, 0, 40, 20, 5, 5);
               this.editButton.graphics.endFill();

               if(this.editButton.textField)
               {
                  this.editButton.textField.textColor = 0xFFFFFF;
                  this.editButton.textField.size = 10;
                  this.editButton.textField.text = "编辑";
               }
            }
            catch(styleError:Error)
            {
               this.editButton.setName("编辑");
            }

            // 设置按钮位置
            this.editButton.x = 650;
            this.editButton.y = 50;
         }
      }

      // 编辑按钮点击事件
      private function editButtonClick(e:MouseEvent) : void
      {
         var currentLabel:String = this.labelBox.nowLabel;
         if(currentLabel == "arms")
         {
            Gaming.uiGroup.alertBox.showInfo("武器编辑功能已激活！");
         }
         else if(currentLabel == "equip")
         {
            Gaming.uiGroup.alertBox.showInfo("装备编辑功能已激活！");
         }
         else
         {
            Gaming.uiGroup.alertBox.showInfo("当前标签页编辑功能已激活！");
         }
      }
      
      public function fleshAllBox() : void
      {
         if(this.visible)
         {
            this.showBox(this.labelBox.nowLabel);
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.PG.changeEquip();
         this.fleshAllBox();
      }
      
      public function showAndLabel(label0:String, lockOtherLabelB0:Boolean = false) : void
      {
         this.hideItemsMore();
         this.setItemsFindVisible(false);
         this.labelBox.nowLabel = label0;
         this.show();
         if(lockOtherLabelB0)
         {
            this.lockOtherLabel();
         }
      }
      
      public function lockOtherLabel() : void
      {
         this.labelBox.setAllPro("actived",false);
         this.labelBox.setAllPro("lockActivedB",true);
      }
      
      public function unlockAllLabel() : void
      {
         this.labelBox.setAllPro("actived",true);
         this.labelBox.setAllPro("lockActivedB",false);
      }
      
      public function setLeftByMe(sp0:DisplayObject) : void
      {
         sp0.x = this.x - sp0.width;
         sp0.y = this.y;
      }
      
      override public function hide() : void
      {
         super.hide();
         this.partsComposeBox.hide();
         Gaming.uiGroup.bulletPathBox.hide();
         Gaming.uiGroup.armsSkinBox.hide();
         Gaming.uiGroup.armsFilterBoard.hide();
      }
      
      public function outLoginEvent() : void
      {
         this.ctrl.clearFleshFun();
      }
      
      public function showInfoText(str0:String) : void
      {
         this.infoTxt.visible = str0 != "";
         this.infoTxt.htmlText = str0;
      }
      
      private function get wearShowB() : Boolean
      {
         return Gaming.uiGroup.wearUI.visible;
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.hideItemsMore();
         this.setItemsFindVisible(false);
         this.showBox(e.label);
         if(e.label == "arms" || e.label == "equip")
         {
            Gaming.uiGroup.houseUI.showBox(e.label);
         }
      }
      
      public function showBox(label0:String) : void
      {
         var n:* = undefined;
         if(!this.visible)
         {
            return;
         }
         Gaming.uiGroup.btnList.hide();
         label0 = this.canPartsPan(label0);
         this.fleshFill();
         this.labelBox.setChoose(label0);
         for(n in this.boxArr)
         {
            this.boxArr[n].visible = false;
         }
         this.sortBtn.visible = true;
         this.sortBtn2.visible = false;
         this.delBtn.actived = true;
         this.composeBtn.visible = false;
         this.partsComposeBox.hide();
         this.moreBtn.visible = false;
         this.findBtn.visible = false;
         this.sortBtn.tipString = "";
         if(label0 == "arms")
         {
            this.nowBox = this.armsBox;
            this.moreBtn.visible = this.wearShowB;
            if(PartsMethod.levelCanB())
            {
               this.composeBtn.visible = true;
               this.composeBtn.setName("一键拆解");
               this.composeBtn.label = "decompose";
            }
         }
         else if(label0 == "equip")
         {
            this.nowBox = this.equipBox;
            this.sortBtn2.visible = true;
            this.moreBtn.visible = this.wearShowB;
            if(PartsMethod.levelCanB())
            {
               this.composeBtn.visible = true;
               this.composeBtn.setName("一键拆解");
               this.composeBtn.label = "decompose";
            }
         }
         else if(label0 == "things")
         {
            this.delBtn.actived = false;
            this.nowBox = this.thingsBox;
            this.sortBtn2.visible = true;
            this.findBtn.visible = true;
            this.sortBtn.tipString = "点击两下可按照物品数量进行排序。";
         }
         else if(label0 == "parts")
         {
            this.nowBox = this.partsBox;
            if(PartsMethod.levelCanB())
            {
               this.composeBtn.visible = true;
               this.composeBtn.setName("一键合成");
               this.composeBtn.label = "compose";
            }
         }
         else if(label0 == "gene")
         {
            this.nowBox = this.geneBox;
            this.composeBtn.visible = true;
            this.composeBtn.setName("一键分解");
            this.composeBtn.label = "decompose";
         }
         this.nowBox.visible = true;
         this.ctrl.flesh(this.nowBox,label0,this);
         if(this.nowBox.moreChooseB)
         {
            this.nowBox.fleshMoreChoose();
         }
         var dg0:ItemsDataGroup = this.nowBox.fatherData as ItemsDataGroup;
         this.sortBtn.setName(dg0.getSortBtnText());
         this.sortBtn2.setName(dg0.getSortBtnText2());
      }
      
      public function fleshNowBox() : void
      {
         if(Boolean(this.nowBox))
         {
            this.nowBox.refleshItemsDataNow();
         }
         this.fleshFill();
      }
      
      public function fleshNowBoxChoose() : void
      {
         if(Boolean(this.nowBox))
         {
            this.nowBox.fleshMoreChoose();
         }
      }
      
      private function fleshFill() : void
      {
         var n:* = undefined;
         var btn0:NormalBtn = null;
         var space0:int = 0;
         var arr0:Array = this.labelBox.gripArr;
         for(n in arr0)
         {
            btn0 = arr0[n];
            space0 = Gaming.PG.da.getBagSpaceByType(btn0.label);
            btn0.setSmallIcon(space0 == 0 ? "fill" : "");
         }
         Gaming.uiGroup.mainUI.bagFillPan();
         Gaming.uiGroup.gameWorldUI.bagFillPan();
      }
      
      private function canPartsPan(label0:String) : String
      {
         var nameArr0:Array = ["arms","equip","things"];
         var cnArr0:Array = ["武器","装备","物品"];
         if(PetCount.levelCanB())
         {
            nameArr0.push("gene");
            cnArr0.push("基因体");
         }
         if(PartsMethod.levelCanB())
         {
            nameArr0.push("parts");
            cnArr0.push("零件");
         }
         if(nameArr0.indexOf(label0) == -1)
         {
            label0 = "arms";
         }
         this.labelBox.inData("bagLabelBtn",nameArr0,cnArr0);
         return label0;
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         this.hide();
         Gaming.uiGroup.wearUI.bagCloseEvent();
         Gaming.uiGroup.shopUI.bagCloseEvent();
         Gaming.uiGroup.partsUI.bagCloseEvent();
         Gaming.uiGroup.blackMarketUI.bagCloseEvent();
         Gaming.uiGroup.houseUI.bagCloseEvent();
         Gaming.uiGroup.forgingUI.bagCloseEvent();
         Gaming.uiGroup.cityUI.bagCloseEvent();
      }
      
      private function sortBagClick(e:MouseEvent) : void
      {
         if(Boolean(this.nowBox))
         {
            this.nowBox.fatherData.sort(Gaming.PG.getOpposingDataGroup(this.nowBox.fatherData));
            this.fleshAllBox();
         }
      }
      
      private function sortBagClick2(e:MouseEvent) : void
      {
         if(Boolean(this.nowBox))
         {
            this.nowBox.fatherData.sort2(Gaming.PG.getOpposingDataGroup(this.nowBox.fatherData));
            this.fleshAllBox();
         }
      }
      
      public function sortByTime() : void
      {
         if(Boolean(this.nowBox))
         {
            this.nowBox.fatherData.sortByTime();
            this.fleshAllBox();
         }
      }
      
      private function batchSellClick(e:MouseEvent) : void
      {
         var stateArr0:Array = null;
         var type0:String = this.nowBox.fatherData.dataType;
         var str0:String = "你确定要一键卖出指定条件的" + this.nowBox.fatherData.getCnName() + "？";
         if(type0 != "parts")
         {
            stateArr0 = Gaming.PG.save.setting.getColorArr(type0,false);
            Gaming.uiGroup.alertBox.itemsColor.showColorChoose(str0,stateArr0,type0,false,this.affter_batchSellClick);
         }
         else
         {
            Gaming.uiGroup.alertBox.showNormal(str0,"yesAndNo",this.affter_batchSellClick);
         }
      }
      
      private function affter_batchSellClick() : void
      {
         var stateArr0:Array = null;
         var type0:String = null;
         var dataArr0:Array = null;
         if(Boolean(this.nowBox))
         {
            stateArr0 = Gaming.uiGroup.alertBox.itemsColor.getDataArr();
            type0 = this.nowBox.fatherData.dataType;
            Gaming.PG.save.setting.setColorArr(type0,false,stateArr0);
            dataArr0 = this.nowBox.fatherData.getBatchSellDataArr(stateArr0);
            if(dataArr0.length > 0)
            {
               ItemsGripBtnListCtrl.batchSell(dataArr0,this.nowBox.fatherData);
            }
            this.fleshAllBox();
         }
      }
      
      private function composeClick(e:MouseEvent) : void
      {
         var type0:String = this.nowBox.fatherData.dataType;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.label == "compose")
         {
            if(type0 == ItemsDataGroup.TYPE_PARTS)
            {
               this.partsComposeBox.showOrHide();
               if(this.partsComposeBox.visible)
               {
                  addChild(this.partsComposeBox);
               }
            }
         }
         else if(btn0.label == "decompose")
         {
            this.gotoDecompose();
         }
      }
      
      public function gotoDecomposeMust(type0:String) : void
      {
         if(visible)
         {
            type0 = this.nowBox.fatherData.dataType;
            if(type0 == type0)
            {
               if(this.composeBtn.actived)
               {
                  this.gotoDecompose();
               }
            }
         }
      }
      
      private function gotoDecompose() : void
      {
         var type0:String = this.nowBox.fatherData.dataType;
         var str0:String = "你确定要一键" + (type0 == "arms" ? "拆解" : "分解") + "指定条件的" + this.nowBox.fatherData.getCnName() + "？";
         var stateArr0:Array = Gaming.PG.save.setting.getColorArr(type0,true);
         Gaming.uiGroup.alertBox.itemsColor.showColorChoose(str0,stateArr0,type0,true,this.affter_composeClick);
      }
      
      private function affter_composeClick() : void
      {
         var check0:CheckText = null;
         var type0:String = this.nowBox.fatherData.dataType;
         var stateArr0:Array = Gaming.uiGroup.alertBox.itemsColor.getDataArr();
         Gaming.PG.save.setting.setColorArr(type0,true,stateArr0);
         if(type0 == "arms" || type0 == "equip")
         {
            check0 = PartsMethod.batchDecompose(Gaming.PG.da[type0 + "Bag"],Gaming.PG.da.partsBag,stateArr0);
            if(check0.state == "" || check0.state == CheckText.BREACK)
            {
               Gaming.uiGroup.partsUI.itemsDataRemove();
               this.fleshAllBox();
            }
            Gaming.uiGroup.alertBox.showNormal(check0.tipStr0,"yes",null,null,check0.state == "" ? "yes" : "");
         }
         else if(type0 == "gene")
         {
            GeneDataDecompose.decomposeDataGroup(this.nowBox.fatherData as GeneDataGroup,stateArr0);
         }
      }
      
      private function batchSellOver(e:MouseEvent) : void
      {
         var str0:String = null;
         var tipBox0:TipBox = Gaming.uiGroup.tipBox;
         if(Boolean(this.nowBox))
         {
            if(e.target == this.delBtn)
            {
               str0 = this.nowBox.fatherData.getBatchSellTextTip();
            }
            if(e.target == this.composeBtn)
            {
               str0 = this.nowBox.fatherData.getComposeTextTip();
            }
            if(str0 != "")
            {
               tipBox0.textTip.setText(str0);
               tipBox0.textTip.show();
               tipBox0.followMouseB = true;
            }
            else
            {
               tipBox0.hide();
            }
         }
         else
         {
            tipBox0.hide();
         }
      }
      
      private function batchSellOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function moreBtnClick(e:MouseEvent) : void
      {
         if(this.itemsMore.visible)
         {
            this.hideItemsMore();
         }
         else
         {
            this.showItemsMore();
         }
      }
      
      private function showItemsMore() : void
      {
         this.moreBtn.setName("关闭");
         this.itemsMore.showBox(this.nowBox);
         this.partsComposeBox.hide();
      }
      
      private function hideItemsMore() : void
      {
         this.moreBtn.setName("多选");
         this.itemsMore.hide();
      }
      
      private function findBtnClick(e:MouseEvent) : void
      {
         this.setItemsFindVisible(!this.itemsFind.visible);
      }
      
      private function setItemsFindVisible(bb0:Boolean) : void
      {
         if(bb0)
         {
            this.itemsFind.showBox(this.nowBox);
            this.findBtn.setName("搜索");
            this.partsComposeBox.hide();
         }
         else
         {
            this.itemsFind.hide();
            this.findBtn.setName("搜索");
         }
      }
      
      public function FTimer() : void
      {
         if(this.visible)
         {
            this.armsBox.pageBox.FTimer();
            this.equipBox.pageBox.FTimer();
            this.thingsBox.pageBox.FTimer();
            this.partsBox.pageBox.FTimer();
            this.geneBox.pageBox.FTimer();
         }
      }
   }
}


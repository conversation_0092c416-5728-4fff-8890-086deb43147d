package dataAll._app.union.info
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.union.UnionData;
   import dataAll._app.union.battle.UBattleAgent;
   import dataAll._app.union.battle.UBattleMapAgent;
   import dataAll._app.union.battle.UnionBattleMapDefine;
   import dataAll._app.union.define.MilitaryDefine;
   import dataAll._app.union.define.UnionRole;
   import dataAll._app.union.extra.MemberExtra;
   import dataAll._app.union.extra.MemberExtraPer;
   
   public class MemberInfo
   {
      public static var pro_arr:Array = [];
      
      public static var LIST_ARR:Array = ["rank","getNickName","getMilitaryCnName","getDpsString","getBattleString","getRoleCnNameInList","getLoginTime","getWeekContribution","contribution"];
      
      public static var LIST_CN:Array = ["排名","昵称","军衔","战斗力","周争霸分","职位","最近登录日期","周贡献","总贡献"];
      
      public static var CHECK_ARR:Array = ["rank","getNickName","extraObj.lv","extraObj.vip","getLifeString","getDpsString"];
      
      public static var CHECK_CN:Array = ["编号","昵称","等级","VIP","生命值","战斗力"];
      
      public static const ALL_ARR:Array = ["rank","getNickName","getRoleCnNameInList","getMilitaryCnName","extraObj.vip","getDpsStringNoColor","getLifeStringNoColor","getBattleString","getBattleMapCn","getLoginTime","getWeekContribution","getWeekContribution_1","getWeekContribution_2","contribution"];
      
      public static const ALL_CN:Array = ["排名","昵称","职位","军衔","VIP","战斗力","生命值","周争霸分数","周争霸地图","最近登录日期","周贡献","上周贡献","上上周贡献","总贡献"];
      
      private static const SDATA:MemberInfo = new MemberInfo();
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var rank:int = 0;
      
      public var gameId:int = 0;
      
      public var unionId:int = 0;
      
      public var uId:Number = 0;
      
      public var userName:String = "";
      
      public var index:int = 0;
      
      public var nickName:String = "";
      
      public var extra:String = "";
      
      public var extra2:String = "";
      
      public var active_time:String = "";
      
      public var roleId:Number = 0;
      
      public var roleName:String = "";
      
      public var extraObj:MemberExtra = new MemberExtra();
      
      public var extraPerObj:MemberExtraPer = new MemberExtraPer();
      
      private var attackB:Boolean = false;
      
      public function MemberInfo()
      {
         super();
         this.contribution = 0;
      }
      
      public static function getRoleCnNameById(id0:int) : String
      {
         if(id0 == 0)
         {
            return "无";
         }
         if(id0 == 1)
         {
            return "军队管理员";
         }
         if(id0 == 27)
         {
            return "副司令";
         }
         if(id0 == -1)
         {
            return "总司令";
         }
         return "";
      }
      
      public static function getSimulated(index0:int) : MemberInfo
      {
         var i0:MemberInfo = new MemberInfo();
         i0.rank = index0 + 1;
         i0.gameId = 0;
         i0.unionId = index0 + 1;
         i0.userName = "sounto" + String(index0 + 1);
         i0.index = 0;
         i0.nickName = "沃龙先生" + String(index0 + 1);
         i0.contribution = int(Math.random() * 100000 + 250000);
         i0.active_time = String(int(Math.random() * 10000000 + 10));
         var e0:MemberExtra = new MemberExtra();
         e0.dps = Math.ceil(Math.random() * 10000000000);
         e0.bt = "2022-7-31 16:45:44";
         e0.lt = NumberMethod.toFixed(20 + Math.random() * 10,2);
         e0.mp = Gaming.defineGroup.union.battle.getRandomMapName();
         e0.playerName = "沃龙" + index0;
         i0.extraObj = e0;
         i0.refreshToExtra();
         return i0;
      }
      
      private static function getSimulatedBattle(index0:int) : MemberInfo
      {
         var i0:MemberInfo = SDATA;
         i0.rank = index0 + 1;
         i0.gameId = 0;
         i0.unionId = index0 + 1;
         i0.userName = "sounto" + String(index0 + 1);
         i0.index = 0;
         i0.nickName = "沃龙先生" + String(index0 + 1);
         i0.contribution = int(Math.random() * 100000 + 250000);
         i0.active_time = String(int(Math.random() * 10000000 + 10));
         if(i0.extraObj.playerName == "")
         {
            i0.extraObj.playerName = "【废材】沃龙";
            i0.refreshToExtra();
         }
         return i0;
      }
      
      public static function setSimulated(extra0:String) : void
      {
         SDATA.extra = extra0;
         SDATA.extraToObj();
      }
      
      public function get contribution() : Number
      {
         return this.CF.getAttribute("contribution");
      }
      
      public function set contribution(v0:Number) : void
      {
         this.CF.setAttribute("contribution",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.extraToObj();
      }
      
      public function refreshToExtra() : void
      {
         this.extra = MemberExtra.toExtra(this.extraObj);
         this.extra2 = MemberExtra.toExtra(this.extraPerObj);
      }
      
      public function extraToObj() : void
      {
         if(this.extra.indexOf("{") == -1 && this.extra != "")
         {
            this.extraObj.inData_byObj(MemberExtra.toObj(this.extra));
         }
         if(this.extra2.indexOf("{") == -1 && this.extra2 != "")
         {
            this.extraPerObj.inData_byObj(MemberExtra.toObj(this.extra2));
         }
      }
      
      public function getStringArrBy(nameArr0:Array) : Array
      {
         var name0:* = null;
         var str0:String = null;
         var arr0:Array = [];
         for each(name0 in nameArr0)
         {
            str0 = String(this.getProByUrl(name0));
            arr0.push(str0);
         }
         return arr0;
      }
      
      public function getStringArr() : Array
      {
         var name0:* = null;
         var str0:String = null;
         var arr0:Array = [];
         for each(name0 in LIST_ARR)
         {
            str0 = String(this.getProByUrl(name0));
            arr0.push(str0);
         }
         return arr0;
      }
      
      public function getCheckStringArr() : Array
      {
         var name0:* = null;
         var str0:String = null;
         var arr0:Array = [];
         for each(name0 in CHECK_ARR)
         {
            str0 = String(this.getProByUrl(name0));
            arr0.push(str0);
         }
         return arr0;
      }
      
      public function getProByUrl(url0:String, toNumberB0:Boolean = false) : *
      {
         var f0:String = null;
         var n0:String = null;
         if(toNumberB0)
         {
            if(url0 == "getDpsString" || url0 == "getDpsStringNoColor")
            {
               return this.extraObj.dps;
            }
            if(url0 == "getLifeString" || url0 == "getLifeStringNoColor")
            {
               return this.extraObj.life;
            }
         }
         var index0:int = int(url0.indexOf("."));
         if(index0 == -1)
         {
            f0 = url0;
            if(this[f0] is Function)
            {
               return this[f0]();
            }
            return this[f0];
         }
         f0 = url0.substring(0,index0);
         n0 = url0.substring(index0 + 1);
         return this[f0][n0];
      }
      
      public function getRoleCnNameInList() : String
      {
         return getRoleCnNameById(this.roleId);
      }
      
      public function getRoleCnName(kingUid0:Number) : String
      {
         if(this.uId == kingUid0)
         {
            return getRoleCnNameById(-1);
         }
         return getRoleCnNameById(this.roleId);
      }
      
      public function haveRoleB(kingUid0:Number) : Boolean
      {
         return this.uId == kingUid0 || this.roleId != 0;
      }
      
      public function haveRoleID() : Boolean
      {
         return this.roleId != 0;
      }
      
      public function getRoleID(kingUid0:Number) : int
      {
         if(this.uId == kingUid0)
         {
            return UnionRole.kingId;
         }
         if(this.roleId != 0)
         {
            return this.roleId;
         }
         return 0;
      }
      
      public function setRoleId(id0:int) : void
      {
         this.roleId = id0;
         this.roleName = getRoleCnNameById(id0);
      }
      
      public function getWeekContribution() : int
      {
         return UnionData.getWeekContributionBy(Gaming.api.save.getNowServerDate(),this.extraObj.conObj);
      }
      
      public function getWeekContribution_1() : int
      {
         return UnionData.getWeekContributionBy(Gaming.api.save.getNowServerDate(),this.extraObj.conObj,-1);
      }
      
      public function getWeekContribution_2() : int
      {
         return UnionData.getWeekContributionBy(Gaming.api.save.getNowServerDate(),this.extraObj.conObj,-2);
      }
      
      public function getLoginTime() : String
      {
         var da0:StringDate = null;
         if(this.extraObj.loginTime != "")
         {
            da0 = new StringDate(this.extraObj.loginTime);
            return da0.getMonthDayStr();
         }
         return "";
      }
      
      public function getNickName() : String
      {
         if(this.extraObj.playerName == "")
         {
            return this.nickName;
         }
         return this.extraObj.playerName;
      }
      
      public function getMilitaryCnName() : String
      {
         return Gaming.defineGroup.union.military.getCnNameByContribution(this.contribution);
      }
      
      public function getMilitaryDefine() : MilitaryDefine
      {
         return Gaming.defineGroup.union.military.getDefineByContribution(this.contribution);
      }
      
      public function getDpsString() : String
      {
         return ComMethod.numberToSmall(this.extraObj.dps,"#00FFFF");
      }
      
      public function getDpsStringNoColor() : String
      {
         return ComMethod.numberToSmall(this.extraObj.dps,"");
      }
      
      public function getLifeString() : String
      {
         return ComMethod.numberToSmall(this.extraObj.life,"#00FFFF");
      }
      
      public function getLifeStringNoColor() : String
      {
         return ComMethod.numberToSmall(this.extraObj.life,"");
      }
      
      public function getBattleString() : String
      {
         var me0:MemberInfo = Gaming.PG.da.union.nowMember;
         if(Boolean(me0))
         {
            if(me0.samePan(this))
            {
               return me0.getBattleScoreStr();
            }
         }
         return this.getBattleScoreStr();
      }
      
      public function getBattleScoreStr() : String
      {
         if(this.attackB)
         {
            return this.getBattleScore() + "";
         }
         return "";
      }
      
      public function getContributionString(d0:MilitaryDefine) : String
      {
         return this.contribution + ComMethod.color("/" + d0.totalMust);
      }
      
      public function getDpsMul() : Number
      {
         var d0:MilitaryDefine = this.getMilitaryDefine();
         return d0.dpsMul;
      }
      
      public function samePan(info0:MemberInfo) : Boolean
      {
         if(Boolean(info0))
         {
            return this.uId == info0.uId && this.index == info0.index;
         }
         return false;
      }
      
      public function inBattleExtra(info0:MemberInfo) : void
      {
         this.attackB = info0.attackB;
         this.extraObj.inBattleExtra(info0.extraObj);
         this.refreshToExtra();
      }
      
      public function inBeforeInfo(info0:MemberInfo) : void
      {
         if(Boolean(info0))
         {
            if(info0.uId == this.uId && info0.index == this.index)
            {
               if(info0.attackB)
               {
                  this.attackB = info0.attackB;
                  this.setBattleRank(info0.getBattleRank());
               }
            }
         }
      }
      
      public function chooseMapEvent(a0:UBattleMapAgent, severTime0:String) : void
      {
         this.extraObj.mp = a0.name;
         this.extraObj.bt = severTime0;
         this.extraObj.lt = 0;
         this.refreshToExtra();
      }
      
      public function clearMapData() : void
      {
         this.extraObj.mp = "";
         this.extraObj.bt = "";
         this.refreshToExtra();
      }
      
      public function isAttackB() : Boolean
      {
         return this.attackB;
      }
      
      public function setAttackB(bb0:Boolean) : void
      {
         this.attackB = bb0;
      }
      
      public function getBattleRank() : Number
      {
         return this.CF.getAttribute("battleRank");
      }
      
      public function setBattleRank(v0:Number) : void
      {
         this.CF.setAttribute("battleRank",v0);
      }
      
      public function getBattleScore() : Number
      {
         if(this.attackB)
         {
            return UBattleAgent.getScore(this.extraObj.lt);
         }
         return 0;
      }
      
      public function getBatTime() : Number
      {
         return this.extraObj.lt;
      }
      
      public function getBatDps() : Number
      {
         var mul0:Number = this.getBatDpsFirst();
         var max0:Number = this.getBatDpsMax();
         if(mul0 > max0)
         {
            mul0 = max0;
         }
         return mul0;
      }
      
      public function getBatDpsFirst() : Number
      {
         if(this.attackB == false)
         {
            return 0;
         }
         var score0:Number = this.getBattleScore();
         return UBattleAgent.getDpsMul(score0);
      }
      
      public function getBatDpsMax() : Number
      {
         var d0:UnionBattleMapDefine = this.getBattleMapDefine();
         var rank0:int = this.getBattleRank();
         if(Boolean(d0) && rank0 >= 1)
         {
            return d0.getDpsMax(rank0);
         }
         return 0;
      }
      
      public function getBatLife() : Number
      {
         if(this.attackB == false)
         {
            return 0;
         }
         var score0:Number = this.getBattleScore();
         return UBattleAgent.getLifeMul(score0);
      }
      
      public function getBattleMapDefine() : UnionBattleMapDefine
      {
         var map0:String = null;
         var d0:UnionBattleMapDefine = null;
         if(this.attackB)
         {
            map0 = this.extraObj.mp;
            if(map0 != "")
            {
               d0 = Gaming.defineGroup.union.battle.getDefine(this.extraObj.mp);
               if(Boolean(d0))
               {
                  return d0;
               }
            }
         }
         return null;
      }
      
      public function getBattleMapCn(noStr0:String = "") : String
      {
         var d0:UnionBattleMapDefine = this.getBattleMapDefine();
         if(Boolean(d0))
         {
            return d0.cnName;
         }
         return noStr0;
      }
      
      public function getMemberBattleTip() : String
      {
         var s0:String = "";
         if(this.attackB)
         {
            s0 += "\n争霸通关时间：<blue " + this.extraObj.lt + "/>\n";
            s0 += "争霸分数：<blue " + this.getBattleScore() + "/>\n";
            s0 += "争霸地图：<blue " + this.getBattleMapCn() + "/>\n";
         }
         return s0;
      }
      
      public function getBattleTrace(meInfo0:MemberInfo) : String
      {
         var s0:String = "";
         if(meInfo0 == this)
         {
            s0 = "【自己】";
         }
         return s0 + (this.extraObj.playerName + "，" + this.extraObj.lt + "，" + this.extraObj.bt + "，" + this.extraObj.mp);
      }
   }
}


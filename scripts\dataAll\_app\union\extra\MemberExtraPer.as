package dataAll._app.union.extra
{
   import com.sounto.utils.ClassProperty;
   
   public class MemberExtraPer
   {
      public static var pro_arr:Array = [];
      
      public var job:String = "";
      
      public function MemberExtraPer()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
      }
   }
}


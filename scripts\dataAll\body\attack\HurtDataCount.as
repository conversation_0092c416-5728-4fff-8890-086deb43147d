package dataAll.body.attack
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import gameAll.body.IO_NormalBody;
   
   public class HurtDataCount
   {
      private var targetName:String = "";
      
      private var cnObj:Object = null;
      
      private var t:Number = 0;
      
      private var all_t:Number = 0;
      
      private var T:Number = 5;
      
      public function HurtDataCount(targetName0:String)
      {
         super();
         this.targetName = targetName0;
      }
      
      public function hurt(h0:HurtData, b0:IO_NormalBody, b1:IO_NormalBody) : void
      {
         var cn0:String = null;
         if(h0.hurtRatio > 0)
         {
            cn0 = h0.getHurtCn(b1);
            if(<PERSON><PERSON><PERSON>(b1))
            {
               if(b1.getDefine().name == this.targetName)
               {
                  this.add(h0.hurtRatio,h0.getHurtCn(),true);
               }
            }
            if(b0.getDefine().name == this.targetName)
            {
            }
         }
      }
      
      private function add(v0:Number, cn0:String, traceB0:Boolean = false) : void
      {
         if(!this.cnObj)
         {
            this.cnObj = {};
         }
         if(!this.cnObj.hasOwnProperty(cn0))
         {
            this.cnObj[cn0] = 0;
         }
         v0 = Math.round(v0);
         this.cnObj[cn0] += v0;
         if(traceB0)
         {
            INIT.TRACE(ComMethod.numberToSmall(v0,"") + "——" + cn0);
         }
      }
      
      public function FTimer() : void
      {
         if(Boolean(this.cnObj))
         {
            this.t += 0.03333333333333333;
            this.all_t += 0.03333333333333333;
            if(this.t >= this.T)
            {
               this.t = 0;
               this.traceCount();
            }
         }
      }
      
      private function traceCount() : void
      {
         var n:* = undefined;
         var allT0:Number = NaN;
         var v0:Number = NaN;
         var newV0:Number = NaN;
         var cn0:String = null;
         var numObj0:Object = {};
         var numArr0:Array = [];
         for(n in this.cnObj)
         {
            v0 = Number(this.cnObj[n]);
            numObj0[v0] = n;
            numArr0.push(v0);
         }
         ArrayMethod.sortNumberArray(numArr0);
         allT0 = this.all_t;
         if(allT0 < 1)
         {
            allT0 = 1;
         }
         var s0:String = "【" + numArr0.length + "项攻击方式】当前时间（" + Number(this.all_t).toFixed(2) + "）@@@@@@@@@@@@@@@@@@@";
         for each(v0 in numArr0)
         {
            newV0 = v0 / allT0;
            cn0 = numObj0[v0];
            s0 += "\n" + ComMethod.numberToSmall(newV0,"") + "——" + cn0;
         }
         s0 += "\n@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@";
         INIT.TRACE(s0);
      }
   }
}


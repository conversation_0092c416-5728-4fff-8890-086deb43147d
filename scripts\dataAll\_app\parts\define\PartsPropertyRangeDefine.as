package dataAll._app.parts.define
{
   import com.sounto.utils.ClassProperty;
   
   public class PartsPropertyRangeDefine
   {
      public static var pro_arr:Array = [];
      
      public var maxLevel:int = 0;
      
      public var max:Number = 0;
      
      public function PartsPropertyRangeDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
   }
}


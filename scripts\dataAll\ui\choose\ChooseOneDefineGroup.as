package dataAll.ui.choose
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.Sounto64;
   
   public class ChooseOneDefineGroup
   {
      public var name:String = "";
      
      public var cnName:String = "";
      
      private var _max:String = "";
      
      public var arr:Array = [];
      
      public var obj:Object = {};
      
      public function ChooseOneDefineGroup()
      {
         super();
         this.max = 1;
      }
      
      public function set max(v0:Number) : void
      {
         this._max = Sounto64.encode(String(v0));
      }
      
      public function get max() : Number
      {
         return Number(Sounto64.decode(this._max));
      }
      
      public function add(d0:ChooseOneDefine) : void
      {
         this.arr.push(d0);
         d0.index = this.arr.length - 1;
         this.obj[d0.name] = d0;
      }
      
      public function addBy(name0:String, cnName0:String) : void
      {
         var d0:ChooseOneDefine = new ChooseOneDefine();
         d0.name = name0;
         d0.cnName = cnName0;
         this.add(d0);
      }
      
      public function getSelectedString(arr0:Array) : String
      {
         var str0:String = null;
         var n:* = undefined;
         var name0:String = null;
         var d0:ChooseOneDefine = null;
         str0 = "";
         var now0:int = int(arr0.length);
         var bb0:Boolean = now0 >= this.max;
         str0 = this.cnName + ComMethod.mustColor(now0,this.max,true) + "：";
         var str2:String = "";
         for(n in arr0)
         {
            name0 = arr0[n];
            d0 = this.obj[name0];
            str2 += ComMethod.link(d0.cnName,name0);
            if(n < arr0.length - 1)
            {
               str2 += "  ";
            }
         }
         return str0 + ("<b>" + str2 + "</b>");
      }
      
      public function getChooseString(arr0:Array) : String
      {
         var n:* = undefined;
         var d0:ChooseOneDefine = null;
         var haveB0:Boolean = false;
         var str0:String = "";
         for(n in this.arr)
         {
            d0 = this.arr[n];
            haveB0 = arr0.indexOf(d0.name) >= 0;
            if(haveB0)
            {
               str0 += ComMethod.color(d0.cnName,"#666666");
            }
            else
            {
               str0 += ComMethod.link(d0.cnName,d0.name);
            }
            if(n < this.arr.length - 1)
            {
               str0 += "  ";
            }
         }
         return str0;
      }
      
      public function getCnName(name0:String) : String
      {
         var d0:ChooseOneDefine = this.obj[name0];
         return d0.cnName;
      }
   }
}


package dataAll.gift.define
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   
   public class ExchangeGiftAddDefineGroup extends GiftAddDefineGroup
   {
      public var codeExchangeId:String = "";
      
      public var codeUrl:String = "";
      
      public var apiType:String = "";
      
      public var giftSave:String = "";
      
      public var startTime:String = "";
      
      public var endTime:String = "";
      
      public var hideB:Boolean = false;
      
      public var hideOutTimeB:Boolean = false;
      
      public function ExchangeGiftAddDefineGroup()
      {
         super();
      }
      
      override public function inData_byOneXML(xml0:XML, father0:String) : void
      {
         super.inData_byOneXML(xml0,father0);
         this.codeExchangeId = xml0.@codeExchangeId;
         this.endTime = xml0.@endTime;
         this.startTime = xml0.@startTime;
         this.hideB = Boolean(int(xml0.@hideB));
         this.hideOutTimeB = Boolean(int(xml0.@hideOutTimeB));
         this.apiType = xml0.@apiType;
         this.codeUrl = xml0.codeUrl;
         this.giftSave = xml0.@giftSave;
      }
      
      public function getTip(startB0:Boolean) : String
      {
         var str0:String = "";
         if(!startB0)
         {
            str0 += "活动开始日期：" + this.startTime + ComMethod.color("\n（日期未到）","#FF0000");
         }
         if(this.endTime != "")
         {
            if(str0 != "")
            {
               str0 += "\n";
            }
            str0 += "活动结束日期：" + this.endTime;
         }
         return str0;
      }
      
      public function isStartB(now0:StringDate) : Boolean
      {
         if(this.startTime == "")
         {
            return true;
         }
         var thisD0:StringDate = new StringDate();
         thisD0.inData_byStr(this.startTime);
         return thisD0.compareDateValue(now0) >= 0;
      }
      
      public function isEndB(now0:StringDate) : Boolean
      {
         if(this.endTime == "")
         {
            return false;
         }
         var thisD0:StringDate = new StringDate();
         thisD0.inData_byStr(this.endTime);
         return thisD0.compareDateValue(now0) >= 0;
      }
   }
}


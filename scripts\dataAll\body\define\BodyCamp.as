package dataAll.body.define
{
   public class BodyCamp
   {
      public static const ENEMY:String = "enemy";
      
      public static const WE:String = "we";
      
      public static const ALL:String = "all";
      
      public function BodyCamp()
      {
         super();
      }
      
      public static function getEnemyCamp(str0:String) : String
      {
         return str0 == WE ? ENEMY : WE;
      }
   }
}


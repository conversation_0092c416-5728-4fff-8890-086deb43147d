package dataAll.equip
{
   import com.sounto.math.Maths;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ObjectMethod;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._player.IO_PlayerLevelGetter;
   import dataAll._player.PlayerData;
   import dataAll._player.base.PlayerBaseData;
   import dataAll._player.role.RoleName;
   import dataAll.equip.add.EquipAddChild;
   import dataAll.equip.add.IO_EquipAddGetter;
   import dataAll.equip.add.IO_EquipAddTipGetter;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.device.DeviceData;
   import dataAll.equip.save.EquipSave;
   import dataAll.equip.save.EquipSaveGroup;
   import dataAll.equip.save.FashionSave;
   import dataAll.equip.suit.SuitCtreator;
   import dataAll.equip.suit.SuitEquipDataObj;
   import dataAll.equip.vehicle.VehicleData;
   import dataAll.equip.vehicle.VehicleDefine;
   import dataAll.equip.weapon.WeaponData;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.ItemsMoreOrder;
   import dataAll.items.save.ItemsSave;
   import dataAll.items.save.ItemsSaveGroup;
   import dataAll.skill.SkillAddData;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.ui.tip.CheckData;
   
   public class EquipDataGroup extends ItemsDataGroup implements IO_EquipAddTipGetter, IO_EquipAddGetter
   {
      public var saveGroup:EquipSaveGroup = null;
      
      public var mergeData:EquipPropertyData = new EquipPropertyData();
      
      public var suitObj:SuitEquipDataObj = null;
      
      public var suitEquipDataGroupingArr:Array = [];
      
      public var keepBodyEquipShowB:Boolean = true;
      
      public var wearShowFashionDef:EquipDefine = null;
      
      private var tempTipObj:Object = null;
      
      public function EquipDataGroup()
      {
         super();
         dataType = ItemsDataGroup.TYPE_EQUIP;
      }
      
      public static function getImageMcObjByImgNameArr(arr0:Array, ignoreHeadB0:Boolean = false) : Object
      {
         var i:* = undefined;
         var d0:EquipDefine = null;
         var imgObj0:Object = null;
         var n:* = undefined;
         var label0:String = null;
         var bb0:Boolean = false;
         var obj0:Object = {};
         for(i in arr0)
         {
            d0 = Gaming.defineGroup.equip.getDefine(arr0[i]);
            if(d0 is EquipDefine)
            {
               imgObj0 = d0.imgObj;
               for(n in imgObj0)
               {
                  label0 = imgObj0[n];
                  if(!obj0[n])
                  {
                     bb0 = false;
                     if(d0.type == EquipType.FASHION && n == "head" && ignoreHeadB0 == false)
                     {
                        bb0 = !d0.keepRoleHeadB();
                     }
                     else
                     {
                        bb0 = true;
                     }
                     if(bb0)
                     {
                        obj0[n] = Gaming.swfLoaderManager.getResourceFull(label0);
                     }
                  }
               }
            }
         }
         return obj0;
      }
      
      public static function filterCanEvoDataArr(marr0:Array) : Array
      {
         var da0:EquipData = null;
         var arr0:Array = [];
         for each(da0 in marr0)
         {
            if(da0.save.getDefine().isCanEvoB())
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      public static function equipSwapTo(dg1:EquipDataGroup, dg2:EquipDataGroup, site1:int, site2:int, wearCompareLevel:int = 9999) : CheckData
      {
         var type0:String = null;
         var numSwapB0:Boolean = false;
         var check0:CheckData = new CheckData();
         check0.bb = false;
         var da1:EquipData = dg1.getDataBySite(site1) as EquipData;
         var da2:EquipData = dg2.getDataBySite(site2) as EquipData;
         if(!(dg1.placeType == ItemsDataGroup.PLACE_WEAR && dg1 == dg2))
         {
            if(dg1.placeType == ItemsDataGroup.PLACE_WEAR || dg2.placeType == ItemsDataGroup.PLACE_WEAR)
            {
               type0 = "";
               if(dg1.placeType == ItemsDataGroup.PLACE_WEAR)
               {
                  if(Boolean(da2))
                  {
                     check0 = dg1.getSwapWearSiteCheckData(da2,site1);
                  }
                  else
                  {
                     check0.bb = true;
                  }
               }
               else if(Boolean(da1))
               {
                  check0 = dg2.getSwapWearSiteCheckData(da1,site2);
               }
               else
               {
                  check0.bb = true;
               }
            }
            else
            {
               check0.bb = true;
            }
         }
         if(check0.bb)
         {
            numSwapB0 = true;
            check0 = ItemsDataGroup.swapTo(dg1,dg2,site1,site2,wearCompareLevel,numSwapB0);
         }
         return check0;
      }
      
      override public function inData_bySaveGroup(sg0:ItemsSaveGroup) : void
      {
         var n:* = undefined;
         var s0:EquipSave = null;
         var da0:EquipData = null;
         this.clearData();
         this.saveGroup = sg0 as EquipSaveGroup;
         for(n in sg0.arr)
         {
            s0 = sg0.arr[n];
            da0 = s0.getDataClass();
            da0.setPlaceType(placeType);
            da0.inData_bySave(s0,normalPlayerData,this);
            setIDIfNo(da0);
            this.fleshWearEquipSite(da0);
            dataArr[n] = da0;
            _siteDataArr = null;
         }
      }
      
      override public function inCloneData(dg0:ItemsDataGroup) : void
      {
         var n:* = undefined;
         var da0:EquipData = null;
         super.inCloneData(dg0);
         var dg2:EquipDataGroup = dg0 as EquipDataGroup;
         this.keepBodyEquipShowB = dg2.keepBodyEquipShowB;
         this.saveGroup = dg2.saveGroup.clone();
         this.suitObj = null;
         for(n in dg2.dataArr)
         {
            da0 = dg2.dataArr[n].clone();
            dataArr.push(da0);
         }
         fleshSaveGroup();
      }
      
      override public function newDayCtrl(timeStr0:String) : void
      {
         var da0:EquipData = null;
         var bb0:Boolean = false;
         super.newDayCtrl(timeStr0);
         var delArr0:Array = [];
         for each(da0 in dataArr)
         {
            if(EquipType.haveLifeArr.indexOf(da0.save.partType) >= 0)
            {
               bb0 = da0.inNowTimeCountHaveDay(timeStr0);
               if(bb0)
               {
                  delArr0.push(da0);
               }
            }
         }
         removeDataArr(delArr0);
      }
      
      public function startLevel(pg0:IO_PlayerLevelGetter) : void
      {
         var da0:EquipData = null;
         for each(da0 in dataArr)
         {
            da0.startLevel(pg0);
         }
      }
      
      override public function addData(da0:IO_ItemsData, fleshSaveGroupB0:Boolean = true) : void
      {
         this.fleshWearEquipSite(da0);
         super.addData(da0,fleshSaveGroupB0);
      }
      
      public function getProAddObj() : Object
      {
         var n:* = undefined;
         var da0:EquipData = null;
         var saveObj0:Object = null;
         var suitAddObj0:Object = null;
         var obj0:Object = {};
         var haveAddEquipTypeObj0:Object = {};
         var tipObj0:Object = {};
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(!haveAddEquipTypeObj0.hasOwnProperty(da0.save.partType))
            {
               saveObj0 = da0.save.getTrueObj();
               ObjectMethod.addNumberObj(obj0,saveObj0);
               EquipAddChild.addTipInObj(saveObj0,tipObj0,da0.getCnName());
               haveAddEquipTypeObj0[da0.save.partType] = true;
            }
         }
         this.fleshSuitObj();
         if(Boolean(this.suitObj))
         {
            suitAddObj0 = this.suitObj.getProAddObj();
            ObjectMethod.addNumberObj(obj0,suitAddObj0);
            EquipAddChild.addTipInObj(suitAddObj0,tipObj0,this.suitObj.getColorCnName() + "套装");
         }
         this.tempTipObj = tipObj0;
         return obj0;
      }
      
      public function getProAddMaxObj(pro0:String) : Object
      {
         return null;
      }
      
      public function getProAddTipObj() : Object
      {
         return this.tempTipObj;
      }
      
      public function fleshMergeData() : *
      {
         var nameArr0:Array = null;
         var name0:* = null;
         var getter0:IO_EquipAddGetter = null;
         this.mergeData.initData();
         if(Boolean(normalPlayerData))
         {
            nameArr0 = normalPlayerData.getEquipAddDataNameArr();
            for each(name0 in nameArr0)
            {
               getter0 = normalPlayerData.getEquipAddGetter(name0);
               this.mergeData.addData(getter0.getProAddObj());
            }
         }
         if(Boolean(playerData))
         {
            this.mergeData.vipDef = playerData.vip.def;
         }
      }
      
      public function fleshMergeDataOnlyMe() : *
      {
         this.mergeData.initData();
         this.mergeData.addData(this.getProAddObj());
      }
      
      public function getEquipProSum(proName0:String, equipType0:String = "") : Number
      {
         var da0:EquipData = null;
         var obj0:Object = null;
         var v0:* = undefined;
         var sum0:Number = 0;
         for each(da0 in dataArr)
         {
            if(equipType0 == "" || da0.save.getPartType() == equipType0)
            {
               obj0 = da0.save.getTrueObj();
               if(obj0.hasOwnProperty(proName0))
               {
                  v0 = obj0[proName0];
                  if(v0 is int || v0 is Number)
                  {
                     sum0 += v0;
                  }
               }
            }
         }
         return sum0;
      }
      
      private function getEquipProObj(proArr0:Array, equipType0:String = "") : Object
      {
         var pro0:* = null;
         var sum0:Number = NaN;
         var obj0:Object = {};
         for each(pro0 in proArr0)
         {
            sum0 = this.getEquipProSum(pro0,equipType0);
            if(sum0 > 0)
            {
               obj0[pro0] = sum0;
            }
         }
         return obj0;
      }
      
      override public function clearData() : void
      {
         super.clearData();
         this.mergeData.initData();
      }
      
      override public function addSave(s0:ItemsSave, fleshSaveGroupB0:Boolean = true) : IO_ItemsData
      {
         var da0:EquipData = (s0 as EquipSave).getDataClass();
         da0.inData_bySave(s0 as EquipSave,normalPlayerData,this);
         if(da0.isCanNumSwapB())
         {
            this.addHaveNumData(da0);
         }
         else
         {
            this.addData(da0,fleshSaveGroupB0);
         }
         return da0;
      }
      
      private function fleshSuitObj() : void
      {
         this.suitObj = SuitCtreator.getObjByEquipDataGroup(this);
      }
      
      public function getSuitTitleText() : String
      {
         if(this.suitObj is SuitEquipDataObj)
         {
            return this.suitObj.getTitleText();
         }
         return "";
      }
      
      public function getSuitColor() : String
      {
         if(this.suitObj is SuitEquipDataObj)
         {
            return this.suitObj.color;
         }
         return "";
      }
      
      public function getSuitConver(index0:int) : EquipDataGroup
      {
         return SuitCtreator.getSuitConver(this,index0);
      }
      
      public function getStrengthenLightLvObj() : Object
      {
         var da0:EquipData = null;
         var arr0:Array = EquipType.SUIT_TYPE_ARR;
         var obj0:Object = null;
         for each(da0 in dataArr)
         {
            if(arr0.indexOf(da0.save.partType) >= 0)
            {
               if(!obj0)
               {
                  obj0 = {};
               }
               obj0[da0.save.partType] = da0.getStrengthenLightLv();
            }
         }
         return obj0;
      }
      
      public function getStrengthenLightLvTop() : String
      {
         var type0:* = null;
         var lv0:int = 0;
         var str0:String = "";
         var obj0:Object = this.getStrengthenLightLvObj();
         for each(type0 in EquipType.SUIT_TYPE_ARR)
         {
            lv0 = 0;
            if(Boolean(obj0))
            {
               if(obj0.hasOwnProperty(type0))
               {
                  lv0 = int(obj0[type0]);
               }
            }
            str0 += lv0 + "";
         }
         return str0;
      }
      
      public function getRoleFashionShow(roleName0:String) : String
      {
         var da0:EquipData = null;
         var s0:FashionSave = null;
         for each(da0 in dataArr)
         {
            s0 = da0.save as FashionSave;
            if(Boolean(s0))
            {
               if(s0.showHero == roleName0)
               {
                  return s0.getDefine().name;
               }
            }
         }
         return "";
      }
      
      public function setRoleFashionShow(roleName0:String, fashionDa0:EquipData) : void
      {
         var da0:EquipData = null;
         var s0:FashionSave = null;
         for each(da0 in dataArr)
         {
            s0 = da0.save as FashionSave;
            if(Boolean(s0))
            {
               if(s0.showHero == roleName0)
               {
                  s0.showHero = "";
               }
            }
         }
         if(Boolean(fashionDa0))
         {
            fashionDa0.setFashionShowRoleName(roleName0);
         }
      }
      
      public function getOnlyFourEquipImgObj() : Object
      {
         var nameArr0:Array = this.getFourEquipImgNameArr(false);
         return getImageMcObjByImgNameArr(nameArr0);
      }
      
      public function getOnlyFourEquipImgObjNoHead() : Object
      {
         var nameArr0:Array = this.getFourEquipImgNameArr(false);
         var obj0:Object = getImageMcObjByImgNameArr(nameArr0);
         obj0["head"] = null;
         return obj0;
      }
      
      public function getFourEquipImgNameArr(haveFashionB0:Boolean = true) : Array
      {
         var type0:* = null;
         var da0:EquipData = null;
         var s0:EquipSave = null;
         var d0:EquipDefine = null;
         var bb0:Boolean = false;
         var arr0:Array = [];
         var nameArr0:Array = EquipType.SHOW_ARR;
         var fashionB0:Boolean = this.saveGroup.showFashionB && haveFashionB0;
         var fashionShowName0:String = this.getFashionShowName();
         if(fashionShowName0 != "" && fashionB0)
         {
            arr0.push(fashionShowName0);
         }
         for each(type0 in nameArr0)
         {
            da0 = this.getOneDataByType(type0);
            if(da0 is EquipData)
            {
               s0 = da0.save;
               d0 = s0.getDefine();
               bb0 = true;
               if(type0 == EquipType.FASHION)
               {
                  bb0 = fashionB0 && fashionShowName0 == "";
               }
               else
               {
                  bb0 = RoleName.haveEquipShowB(da0,normalPlayerData);
               }
               if(bb0)
               {
                  arr0.push(d0.name);
               }
               else
               {
                  arr0.push("");
               }
            }
         }
         return arr0;
      }
      
      public function isGreatSageB() : Boolean
      {
         var fashionDefine0:EquipDefine = this.getWearShowFashionDef();
         if(Boolean(fashionDefine0))
         {
            if(fashionDefine0.name == "greatSage" || fashionDefine0.name == "greatSage2")
            {
               return true;
            }
         }
         return false;
      }
      
      public function getWearShowFashionDef() : EquipDefine
      {
         var now0:EquipData = null;
         var name0:String = "";
         if(Boolean(this.saveGroup) && this.saveGroup.showFashionB)
         {
            name0 = this.getFashionShowName();
            if(name0 == "")
            {
               now0 = this.getOneDataByType(EquipType.FASHION);
               if(Boolean(now0))
               {
                  name0 = now0.save.getDefine().name;
               }
            }
         }
         var d0:EquipDefine = null;
         if(name0 != "")
         {
            d0 = Gaming.defineGroup.equip.getDefine(name0);
         }
         this.wearShowFashionDef = d0;
         return d0;
      }
      
      private function getFashionShowName() : String
      {
         var pd0:PlayerData = getMainPlayerData();
         if(Boolean(pd0))
         {
            return pd0.equipBag.getRoleFashionShow(normalPlayerData.heroData.def.name);
         }
         return "";
      }
      
      public function haveFashionB() : Boolean
      {
         return this.getOneDataByType(EquipType.FASHION) is EquipData;
      }
      
      public function getBlack90EquipNum() : int
      {
         var da0:EquipData = null;
         var d0:EquipDefine = null;
         var num0:int = 0;
         for each(da0 in dataArr)
         {
            d0 = da0.save.getDefine();
            if(d0.isBlack90())
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function haveTwoTypeEquipB() : Boolean
      {
         var n:* = undefined;
         var da0:EquipData = null;
         var type0:String = null;
         var haveAddEquipTypeObj0:Object = {};
         for(n in dataArr)
         {
            da0 = dataArr[n];
            type0 = da0.save.partType;
            if(!haveAddEquipTypeObj0.hasOwnProperty(type0))
            {
               haveAddEquipTypeObj0[type0] = 0;
            }
            ++haveAddEquipTypeObj0[type0];
            if(haveAddEquipTypeObj0[type0] >= 2)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getArrByPartType(partType0:String) : Array
      {
         var da0:EquipData = null;
         var arr0:Array = [];
         for each(da0 in dataArr)
         {
            if(da0.save.partType == partType0)
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      public function getSkillNameArr() : Array
      {
         var da0:EquipData = null;
         var nameArr0:Array = [];
         for each(da0 in dataArr)
         {
            if(!(da0 is VehicleData))
            {
               nameArr0 = nameArr0.concat(da0.save.getSkillArr());
            }
         }
         if(Boolean(this.wearShowFashionDef))
         {
            if(EquipType.haveSkillShowFashionArr.indexOf(this.wearShowFashionDef.name) >= 0)
            {
               nameArr0 = nameArr0.concat(this.wearShowFashionDef.skillArr);
            }
         }
         return ComMethod.clearArrayRepeatString(nameArr0);
      }
      
      public function getNormalSkillNameArr() : Array
      {
         var da0:EquipData = null;
         var normalNameArr0:Array = EquipType.NORMAL_ARR;
         var nameArr0:Array = [];
         for each(da0 in dataArr)
         {
            if(normalNameArr0.indexOf(da0.save.partType) >= 0)
            {
               nameArr0 = nameArr0.concat(da0.save.getSkillArr());
            }
         }
         return ComMethod.clearArrayRepeatString(nameArr0);
      }
      
      public function getAllHurtProNum(level0:int) : Number
      {
         var da0:EquipData = null;
         var num0:int = 0;
         for each(da0 in dataArr)
         {
            if(da0.save.getTrueLevel() == level0)
            {
               if(da0.isAllHurtProB())
               {
                  num0++;
               }
            }
         }
         return num0;
      }
      
      public function getOneDataByType(type0:String) : EquipData
      {
         var n:* = undefined;
         var da0:EquipData = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(da0.save.partType == type0)
            {
               return da0;
            }
         }
         return null;
      }
      
      override public function getBatchSellTextTip() : String
      {
         return "";
      }
      
      override public function getSortBtnText() : String
      {
         return "最佳排序";
      }
      
      override public function getSortBtnTip() : String
      {
         return "对比当前装备与背包中的装备，将属性最好的装备排在前面。";
      }
      
      override public function getSortBtnText2() : String
      {
         return "套装排序";
      }
      
      override public function getSortBtnTip2() : String
      {
         return "";
      }
      
      public function getWearEquipTypeBySite(site0:int) : String
      {
         if(site0 <= this.saveGroup.gripMaxNum - 1)
         {
            if(placeType == ItemsDataGroup.PLACE_WEAR)
            {
               return EquipType.getTypeBySite(site0);
            }
            return "";
         }
         return null;
      }
      
      public function getSiteByEquipType(type0:String) : int
      {
         return EquipType.getSite(type0);
      }
      
      public function getSwapWearSiteCheckData(da0:EquipData, site0:int) : CheckData
      {
         var bb0:Boolean = false;
         var check0:CheckData = new CheckData();
         var type0:String = this.getWearEquipTypeBySite(site0);
         if(!type0)
         {
            check0.bb = false;
         }
         else if(type0 == "")
         {
            check0.bb = true;
         }
         else
         {
            bb0 = da0.save.partType == type0;
            if(bb0)
            {
               check0 = EquipDataSwapPan.canInWearType(normalPlayerData,type0,da0);
            }
            else
            {
               check0.bb = bb0;
            }
         }
         return check0;
      }
      
      override public function swap_addData(da1:IO_ItemsData) : void
      {
         this.fleshWearEquipSite(da1);
         super.swap_addData(da1);
      }
      
      private function fleshWearEquipSite(da0:IO_ItemsData) : void
      {
         var site0:int = 0;
         var da1:EquipData = da0 as EquipData;
         if(placeType == ItemsDataGroup.PLACE_WEAR)
         {
            site0 = EquipType.getSite(da1.save.partType);
            if(!site0 == -1)
            {
               INIT.showErrorMust("不存在这个装备类型：" + da1.save.partType);
            }
            da1.save.site = site0;
         }
      }
      
      override public function sort(dg0:ItemsDataGroup) : void
      {
         this.sortByOther(dg0 as EquipDataGroup);
      }
      
      override public function sort2(dg0:ItemsDataGroup) : void
      {
         this.sortBySuit(dg0 as EquipDataGroup);
      }
      
      public function sortBySuit(dg0:EquipDataGroup) : void
      {
         this.toSuitSortId(dg0);
         sortByTempSortId();
      }
      
      public function toSuitSortId(dg0:EquipDataGroup) : void
      {
         var da0:EquipData = null;
         var da2:EquipData = null;
         for each(da0 in dataArr)
         {
            da2 = dg0.getOneDataByType(da0.save.partType);
            da0.toSuitSortId(da2);
         }
      }
      
      public function sortByOther(dg0:EquipDataGroup) : void
      {
         var da0:EquipData = null;
         var da2:EquipData = null;
         for each(da0 in dataArr)
         {
            da2 = dg0.getOneDataByType(da0.save.partType);
            da0.toOtherSortId(da2);
         }
         sortByTempSortId();
      }
      
      override public function getBatchSellDataArr(colorArr0:Array) : Array
      {
         var n:* = undefined;
         var da0:EquipData = null;
         var sell_arr0:Array = [];
         var arr0:Array = super.getBatchSellDataArr(colorArr0);
         for(n in arr0)
         {
            da0 = arr0[n];
            if(EquipType.NORMAL_ARR.indexOf(da0.save.partType) >= 0)
            {
               sell_arr0.push(da0);
            }
         }
         return sell_arr0;
      }
      
      public function getNormalDataArr() : Array
      {
         var da0:EquipData = null;
         var arr0:Array = [];
         for each(da0 in dataArr)
         {
            if(EquipType.NORMAL_ARR.indexOf(da0.save.partType) >= 0)
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      public function getVehicleData() : VehicleData
      {
         return this.getOneDataByType(EquipType.VEHICLE) as VehicleData;
      }
      
      public function getVehicleName() : String
      {
         var vehicleData0:VehicleData = this.getVehicleData();
         if(Boolean(vehicleData0))
         {
            return vehicleData0.getVehicleSave().getVehicleDefine().name;
         }
         return "";
      }
      
      public function useNumEquip(name0:String, num0:int) : void
      {
         var saveNowNum0:Number = NaN;
         var da0:EquipData = getDataBySaveName(name0) as EquipData;
         if(Boolean(da0))
         {
            saveNowNum0 = da0.save.nowNum;
            if(saveNowNum0 == num0)
            {
               removeData(da0);
            }
            else if(saveNowNum0 > num0)
            {
               saveNowNum0 -= num0;
               da0.setNowNum(saveNowNum0);
            }
            else
            {
               INIT.showErrorMust(name0 + "使用数量不能超越当前数量：" + saveNowNum0 + "-" + num0);
            }
         }
      }
      
      public function getDeviceData() : DeviceData
      {
         return this.getOneDataByType(EquipType.DEVICE) as DeviceData;
      }
      
      public function getEquipNum(name0:String) : int
      {
         var da0:EquipData = getDataBySaveName(name0) as EquipData;
         if(Boolean(da0))
         {
            return da0.getNowNum();
         }
         return 0;
      }
      
      public function addHaveNumData(da0:EquipData) : EquipData
      {
         var name0:String = da0.save.name;
         var findDa0:EquipData = getDataBySaveName(name0) as EquipData;
         if(Boolean(findDa0))
         {
            findDa0.addNowNum(da0.getNowNum());
            findDa0.newB = true;
            return findDa0;
         }
         this.addData(da0);
         return da0;
      }
      
      public function getWeaponData() : WeaponData
      {
         return this.getOneDataByType(EquipType.WEAPON) as WeaponData;
      }
      
      public function getSkillAddData(heroSkillBaseLabel0:String) : SkillAddData
      {
         var da0:EquipData = null;
         var addObj0:Object = null;
         var v0:Number = NaN;
         var skillDefine0:HeroSkillDefine = null;
         var addData0:SkillAddData = null;
         var mul0:Number = 0;
         var max0:Number = 0;
         for each(da0 in dataArr)
         {
            addObj0 = da0.save.heroSkillAddObj;
            if(addObj0.hasOwnProperty(heroSkillBaseLabel0))
            {
               v0 = Number(addObj0[heroSkillBaseLabel0]);
               if(mul0 == 0)
               {
                  mul0 = Maths.Pn(v0);
               }
               if(mul0 >= 0)
               {
                  if(max0 < v0)
                  {
                     max0 = v0;
                  }
               }
               else if(max0 > v0)
               {
                  max0 = v0;
               }
            }
         }
         if(max0 != 0)
         {
            skillDefine0 = Gaming.defineGroup.skill.getOriginalHeroDefine(heroSkillBaseLabel0);
            addData0 = new SkillAddData();
            addData0.inData(heroSkillBaseLabel0,skillDefine0.addD.pro,max0);
            return addData0;
         }
         return SkillAddData.ZERO;
      }
      
      override public function getChosenChooseOrderArr() : Array
      {
         var arr0:Array = [];
         arr0.push(ItemsMoreOrder.allEquipChoose);
         arr0.push(ItemsMoreOrder.invertEquipChoose);
         arr0.push(ItemsMoreOrder.unlockChoose);
         return arr0;
      }
      
      override public function getChosenCtrlOrderArr() : Array
      {
         var arr0:Array = super.getChosenCtrlOrderArr();
         arr0.push(ItemsMoreOrder.refining);
         arr0.push(ItemsMoreOrder.sell);
         arr0.push(ItemsMoreOrder.decompose);
         arr0.push(ItemsMoreOrder.resolve);
         return arr0;
      }
      
      override public function doChosenOrder(name0:String) : Boolean
      {
         return this[name0 + "ChosenOrder"]();
      }
      
      private function allEquipChooseChosenOrder() : Boolean
      {
         var da0:EquipData = null;
         for each(da0 in dataArr)
         {
            if(da0.isNormalEquipB())
            {
               da0.isChosen = true;
            }
         }
         return true;
      }
      
      private function invertEquipChooseChosenOrder() : Boolean
      {
         var da0:EquipData = null;
         for each(da0 in dataArr)
         {
            if(da0.isNormalEquipB())
            {
               da0.isChosen = !da0.isChosen;
            }
         }
         return true;
      }
      
      override protected function unlockChooseChosenOrder() : Boolean
      {
         var da0:EquipData = null;
         for each(da0 in dataArr)
         {
            if(da0.isNormalEquipB())
            {
               if(!da0.getSave().getLockB())
               {
                  da0.isChosen = true;
               }
            }
         }
         return true;
      }
      
      public function getAllVehiclePrice() : Number
      {
         var da0:EquipData = null;
         var da2:VehicleData = null;
         var d0:VehicleDefine = null;
         var goods_d0:GoodsDefine = null;
         var num0:int = 0;
         for each(da0 in dataArr)
         {
            if(da0 is VehicleData)
            {
               da2 = da0 as VehicleData;
               d0 = da2.getVehicleSave().getVehicleDefine();
               if(d0.shopB)
               {
                  goods_d0 = Gaming.defineGroup.goods.getDefine(d0.name);
                  if(Boolean(goods_d0))
                  {
                     if(goods_d0.priceType == "money")
                     {
                        num0 += goods_d0.price;
                     }
                  }
               }
            }
         }
         return num0;
      }
      
      public function zuobiPan(blackB0:Boolean = false) : String
      {
         var n:* = undefined;
         var da0:EquipData = null;
         var str0:String = null;
         var pc0:EquipPropertyDataCreator = Gaming.defineGroup.equipCreator.propertyCtreator;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(EquipType.NORMAL_ARR.indexOf(da0.save.partType) >= 0)
            {
               str0 = blackB0 ? pc0.zuobiPanBlackEquipSave(da0.save) : pc0.zuobiPanEquipSave(da0.save);
               if(str0 != "")
               {
                  return str0;
               }
            }
            if(da0.save.getTrueLevel() > PlayerBaseData.MAX_LEVEL)
            {
               return "装备等级超过最大等级：" + PlayerBaseData.MAX_LEVEL;
            }
         }
         return "";
      }
      
      override public function getSaveGroup() : ItemsSaveGroup
      {
         return this.saveGroup;
      }
      
      override public function getGripType() : String
      {
         return "equipGrip";
      }
      
      override public function getAllChildTypeArr() : Array
      {
         return EquipType.TYPE_ARR;
      }
      
      override public function getBlackMarketCanShowTypeArr() : Array
      {
         return EquipType.NORMAL_ARR;
      }
   }
}


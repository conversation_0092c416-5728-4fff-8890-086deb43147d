package dataAll._app.active.define
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class ActiveGiftDefine
   {
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var name:String = "";
      
      public var gift:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      public function ActiveGiftDefine()
      {
         super();
         this.must = 0;
      }
      
      public function get must() : Number
      {
         return this.CF.getAttribute("must");
      }
      
      public function set must(v0:Number) : void
      {
         this.CF.setAttribute("must",v0);
      }
      
      public function inData_byXML(x0:XML) : void
      {
         ClassProperty.inData_byXMLAt(this,x0,pro_arr);
         this.gift.inData_byXML(x0.gift);
      }
   }
}


package dataAll._app.head
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.head.define.HeadDefine;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSave;
   import dataAll.equip.vehicle.VehicleData;
   import dataAll.equip.vehicle.VehicleDefine;
   
   public class HeadConditionCheckEquip
   {
      public static var zongziArr:Array = ["ZhuTou","BaiZhang","ShuiSheng","BaiLu","XiChi"];
      
      public function HeadConditionCheckEquip()
      {
         super();
      }
      
      public static function zongzi(d0:HeadDefine, dealProgressB0:Boolean) : HeadTempData
      {
         var name0:* = null;
         var bb0:Boolean = false;
         var mapd0:WorldMapDefine = null;
         var cnName0:String = null;
         var r0:HeadTempData = new HeadTempData();
         var nameArr0:Array = zongziArr;
         var winArr0:Array = Gaming.PG.da.worldMap.saveGroup.winMaxDiffMapArr;
         var completeB0:Boolean = true;
         var progressStr0:String = "";
         var index0:int = 0;
         for each(name0 in nameArr0)
         {
            bb0 = winArr0.indexOf(name0) >= 0;
            if(!bb0)
            {
               completeB0 = false;
            }
            if(dealProgressB0)
            {
               mapd0 = Gaming.defineGroup.worldMap.getDefine(name0);
               cnName0 = mapd0.cnName;
               progressStr0 += (index0 > 0 ? "、" : "") + (bb0 ? ComMethod.color(cnName0 + "√","#00FF00") : ComMethod.color(cnName0 + "×","#FF0000"));
            }
            index0++;
         }
         r0.completeB = completeB0;
         if(dealProgressB0)
         {
            r0.compareStr = progressStr0;
         }
         return r0;
      }
      
      public static function rareArms_5(d0:HeadDefine, dealProgressB0:Boolean) : HeadTempData
      {
         var n:* = undefined;
         var name0:String = null;
         var da0:ArmsData = null;
         var armsD0:ArmsRangeDefine = null;
         var bb0:Boolean = false;
         var r0:HeadTempData = new HeadTempData();
         var nameArr0:Array = ["pistolGod","rifleWhite","sniperGreen","shotgunFire"];
         var completeB0:Boolean = true;
         for(n in nameArr0)
         {
            name0 = nameArr0[n];
            da0 = Gaming.PG.da.arms.getDataByBaseName(name0);
            armsD0 = Gaming.defineGroup.bullet.getArmsRangeDefine(name0);
            bb0 = da0 is ArmsData;
            if(!bb0)
            {
               completeB0 = false;
            }
            if(dealProgressB0)
            {
               r0.compareStr += (n > 0 ? "、" : "") + (bb0 ? ComMethod.color(armsD0.def.cnName + "√","#00FF00") : ComMethod.color(armsD0.def.cnName + "×","#FF0000"));
            }
         }
         r0.completeB = completeB0;
         return r0;
      }
      
      public static function dominating(d0:HeadDefine, dealProgressB0:Boolean) : HeadTempData
      {
         var n:* = undefined;
         var type0:String = null;
         var v0:* = undefined;
         var bb0:Boolean = false;
         var r0:HeadTempData = new HeadTempData();
         var typeArr0:Array = ["suit","fashion","deviceLevel","weaponLevel","vehicle"];
         var valueArr0:Array = ["intercessorSuit","xiaoKa",5,4,"Gaia"];
         var completeB0:Boolean = true;
         var progressStr0:String = "";
         for(n in typeArr0)
         {
            type0 = typeArr0[n];
            v0 = valueArr0[n];
            bb0 = Boolean(HeadConditionCheckEquip["pan_" + type0](v0));
            if(!bb0)
            {
               completeB0 = false;
            }
            if(dealProgressB0)
            {
               progressStr0 += (n > 0 ? "、" : "") + pan_normalInfo(type0,v0);
            }
         }
         r0.completeB = completeB0;
         if(dealProgressB0)
         {
            r0.compareStr = progressStr0;
         }
         return r0;
      }
      
      private static function pan_suit(name0:String) : Boolean
      {
         var obj0:Object = Gaming.PG.da.equip.suitObj;
         if(Boolean(obj0))
         {
            return obj0.name == name0;
         }
         return false;
      }
      
      private static function pan_deviceLevel(lv0:int) : Boolean
      {
         return pan_equipLevel(EquipType.DEVICE,lv0);
      }
      
      private static function pan_weaponLevel(lv0:int) : Boolean
      {
         return pan_equipLevel(EquipType.WEAPON,lv0);
      }
      
      private static function pan_fashion(name0:String) : Boolean
      {
         return pan_equip(EquipType.FASHION,name0);
      }
      
      private static function pan_vehicle(name0:String) : Boolean
      {
         var da0:EquipData = null;
         var vda0:VehicleData = null;
         var d0:VehicleDefine = Gaming.defineGroup.vehicle.getDefine(name0);
         var daArr0:Array = Gaming.PG.da.equip.dataArr;
         for each(da0 in daArr0)
         {
            vda0 = da0 as VehicleData;
            if(Boolean(vda0))
            {
               if(d0.panIsMeOrEvolutionB(vda0.save.imgName))
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      private static function pan_equip(type0:String, name0:String) : Boolean
      {
         var da0:EquipData = Gaming.PG.da.equip.getOneDataByType(type0);
         if(da0 is EquipData)
         {
            return da0.save.imgName == name0;
         }
         return false;
      }
      
      private static function pan_equipLevel(type0:String, lv0:int) : Boolean
      {
         var da0:EquipData = Gaming.PG.da.equip.getOneDataByType(type0);
         if(da0 is EquipData)
         {
            return da0.save.itemsLevel >= lv0;
         }
         return false;
      }
      
      private static function pan_normalInfo(type0:String, v0:*) : String
      {
         var suit_f0:EquipFatherDefine = null;
         var fashion_d0:EquipDefine = null;
         var vehicle_d0:VehicleDefine = null;
         var cnName0:String = "";
         if(type0 == "suit")
         {
            suit_f0 = Gaming.defineGroup.equip.getFatherDefine(v0);
            cnName0 = suit_f0.getTitleText();
         }
         else if(type0 == EquipType.FASHION)
         {
            fashion_d0 = Gaming.defineGroup.equip.getDefine(v0);
            cnName0 = fashion_d0.cnName;
         }
         else if(type0 == "deviceLevel")
         {
            cnName0 = TextWay.numToCn(v0) + "级装置";
         }
         else if(type0 == "weaponLevel")
         {
            cnName0 = TextWay.numToCn(v0) + "级副手";
         }
         else if(type0 == "vehicle")
         {
            vehicle_d0 = Gaming.defineGroup.vehicle.getDefine(v0);
            cnName0 = vehicle_d0.cnName;
         }
         var bb0:Boolean = Boolean(HeadConditionCheckEquip["pan_" + type0](v0));
         return ComMethod.color(cnName0 + (bb0 ? "√" : "×"),bb0 ? "#00FF00" : "#FF0000");
      }
      
      public static function weapon_4(d0:HeadDefine, dealProgressB0:Boolean) : HeadTempData
      {
         return equip_4(d0,dealProgressB0,EquipType.WEAPON);
      }
      
      public static function device_4(d0:HeadDefine, dealProgressB0:Boolean) : HeadTempData
      {
         return equip_4(d0,dealProgressB0,EquipType.DEVICE);
      }
      
      private static function equip_4(d0:HeadDefine, dealProgressB0:Boolean, type0:String) : HeadTempData
      {
         var da0:EquipData = null;
         var s0:EquipSave = null;
         var equipD0:EquipDefine = null;
         var name0:String = null;
         var r0:HeadTempData = new HeadTempData();
         var dataArr0:Array = Gaming.PG.da.getEquipDataArr(true,true,true);
         var nameArr0:Array = Gaming.defineGroup[type0].getNormalBaseNameArr();
         var haveArr0:Array = [];
         for each(da0 in dataArr0)
         {
            s0 = da0.save;
            if(s0.partType == type0)
            {
               if(s0.itemsLevel >= 4)
               {
                  equipD0 = da0.save.getDefine();
                  name0 = s0.imgName;
                  if(equipD0.hasOwnProperty("baseLabel"))
                  {
                     name0 = equipD0["baseLabel"];
                  }
                  if(haveArr0.indexOf(name0) == -1)
                  {
                     haveArr0.push(name0);
                  }
               }
            }
         }
         r0.completeB = haveArr0.length >= nameArr0.length;
         if(dealProgressB0)
         {
            r0.compareStr = getProgressStrBy(nameArr0,haveArr0,type0);
         }
         return r0;
      }
      
      private static function getProgressStrBy(nameArr0:Array, haveArr0:Array, type0:String) : String
      {
         var d0:EquipDefine = null;
         var name0:* = null;
         var bb0:Boolean = false;
         var str0:String = "";
         var n0:int = 0;
         for each(name0 in nameArr0)
         {
            if(type0 == EquipType.WEAPON || type0 == EquipType.DEVICE)
            {
               d0 = Gaming.defineGroup[type0].getDefine(name0 + "_1");
            }
            if(d0 is EquipDefine)
            {
               bb0 = haveArr0.indexOf(name0) >= 0;
               str0 += (n0 > 0 ? "、" : "") + (bb0 ? ComMethod.color(d0.cnName + "√","#00FF00") : ComMethod.color(d0.cnName + "×","#FF0000"));
            }
            n0++;
         }
         return str0;
      }
   }
}


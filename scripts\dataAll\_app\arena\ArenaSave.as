package dataAll._app.arena
{
   import UI.arena.ArenaTopCtrl;
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.cf.StringCF;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.arena.record.ArenaRecordSave;
   
   public class ArenaSave
   {
      public static var pro_arr:Array = [];
      
      public static var MAX_NUM:String = TextWay.toCode("2");
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      private var stringCF:StringCF = new StringCF();
      
      public var topSetB:Boolean = false;
      
      public var phaseGiftB:Boolean = false;
      
      public var phaseOverTipB:Boolean = false;
      
      public var record:ArenaRecordSave = new ArenaRecordSave();
      
      private var _uidObj:String = "";
      
      private var _unameObj:String = "";
      
      public var tip284:Boolean = false;
      
      public function ArenaSave()
      {
         super();
         this.topName = "";
         this.allNum = 0;
         this.before = 0;
         this.score = 1;
         this.todayNum = Number(TextWay.getText(MAX_NUM));
         this.streakNum = 0;
         this.challengeNum = 0;
         this.winNum = 0;
         this.maxScore = 0;
         this.arenaStampNum = 0;
         this.uidObj = {};
         this.unameObj = {};
         this.nowPhase = 0;
         this.phaseGiftB = false;
         this.seasonWinNum = 0;
         this.seasonFailNum = 0;
         this.newDayPhase = 6;
      }
      
      public function get topName() : String
      {
         return this.stringCF.getAttribute("topName") as String;
      }
      
      public function set topName(str0:String) : void
      {
         this.stringCF.setAttribute("topName",str0);
      }
      
      public function get nowPhase() : Number
      {
         return this.CF.getAttribute("nowPhase");
      }
      
      public function set nowPhase(v0:Number) : void
      {
         this.CF.setAttribute("nowPhase",v0);
      }
      
      public function get before() : Number
      {
         return this.CF.getAttribute("before");
      }
      
      public function set before(v0:Number) : void
      {
         this.CF.setAttribute("before",v0);
      }
      
      public function get score() : Number
      {
         return this.CF.getAttribute("score");
      }
      
      public function set score(v0:Number) : void
      {
         this.CF.setAttribute("score",v0);
      }
      
      public function get arenaStampNum() : Number
      {
         return this.CF.getAttribute("arenaStampNum");
      }
      
      public function set arenaStampNum(v0:Number) : void
      {
         this.CF.setAttribute("arenaStampNum",v0);
      }
      
      public function get allNum() : Number
      {
         return this.CF.getAttribute("allNum");
      }
      
      public function set allNum(v0:Number) : void
      {
         this.CF.setAttribute("allNum",v0);
      }
      
      public function get todayNum() : Number
      {
         return this.CF.getAttribute("todayNum");
      }
      
      public function set todayNum(v0:Number) : void
      {
         this.CF.setAttribute("todayNum",v0);
      }
      
      public function get streakNum() : Number
      {
         return this.CF.getAttribute("streakNum");
      }
      
      public function set streakNum(v0:Number) : void
      {
         this.CF.setAttribute("streakNum",v0);
      }
      
      public function get challengeNum() : Number
      {
         return this.CF.getAttribute("challengeNum");
      }
      
      public function set challengeNum(v0:Number) : void
      {
         this.CF.setAttribute("challengeNum",v0);
      }
      
      public function get winNum() : Number
      {
         return this.CF.getAttribute("winNum");
      }
      
      public function set winNum(v0:Number) : void
      {
         this.CF.setAttribute("winNum",v0);
      }
      
      public function get canAddNum() : Number
      {
         return this.CF.getAttribute("canAddNum");
      }
      
      public function set canAddNum(v0:Number) : void
      {
         this.CF.setAttribute("canAddNum",v0);
      }
      
      public function get maxScore() : Number
      {
         return this.CF.getAttribute("maxScore");
      }
      
      public function set maxScore(v0:Number) : void
      {
         this.CF.setAttribute("maxScore",v0);
      }
      
      public function get seasonWinNum() : Number
      {
         return this.CF.getAttribute("seasonWinNum");
      }
      
      public function set seasonWinNum(v0:Number) : void
      {
         this.CF.setAttribute("seasonWinNum",v0);
      }
      
      public function get seasonFailNum() : Number
      {
         return this.CF.getAttribute("seasonFailNum");
      }
      
      public function set seasonFailNum(v0:Number) : void
      {
         this.CF.setAttribute("seasonFailNum",v0);
      }
      
      public function get uidObj() : Object
      {
         return Base64.decodeObject(this._uidObj);
      }
      
      public function set uidObj(obj0:Object) : void
      {
         this._uidObj = Base64.encodeObject(obj0);
      }
      
      public function get unameObj() : Object
      {
         return Base64.decodeObject(this._unameObj);
      }
      
      public function set unameObj(obj0:Object) : void
      {
         this._unameObj = Base64.encodeObject(obj0);
      }
      
      public function get newDayPhase() : Number
      {
         return this.CF.getAttribute("newDayPhase");
      }
      
      public function set newDayPhase(v0:Number) : void
      {
         this.CF.setAttribute("newDayPhase",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         if(this.score < 1)
         {
            this.score = 1;
         }
         this.uidObj = ClassProperty.copyObj(obj0["uidObj"]);
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.allNum += this.challengeNum;
         this.todayNum = Number(TextWay.getText(MAX_NUM));
         this.streakNum = 0;
         this.challengeNum = 0;
         this.winNum = 0;
         this.uidObj = {};
         this.unameObj = {};
         this.topSetB = false;
         this.newDayPhase = ArenaTopCtrl.getPhaseByTime(timeStr0);
      }
      
      private function newPhase() : void
      {
         this.before = this.score;
         this.seasonFailNum = 0;
         this.seasonWinNum = 0;
         this.score = 1;
         this.phaseGiftB = false;
         this.phaseOverTipB = false;
      }
      
      public function inNowPhase(phase0:int) : void
      {
         var now0:int = this.nowPhase;
         if(phase0 > now0)
         {
            this.nowPhase = phase0;
            this.newPhase();
         }
      }
      
      public function newDayIsTruePhaseB() : Boolean
      {
         return this.newDayPhase == this.nowPhase;
      }
      
      public function addUid(uid0:String) : void
      {
         var obj0:Object = this.uidObj;
         obj0[uid0] = 1;
         this.uidObj = obj0;
      }
      
      public function getUid(uid0:String) : Boolean
      {
         return this.uidObj.hasOwnProperty(uid0);
      }
      
      public function addUname(uname0:String) : void
      {
         var obj0:Object = this.unameObj;
         obj0[uname0] = 1;
         this.unameObj = obj0;
      }
      
      public function getUname(uname0:String) : Boolean
      {
         return this.unameObj.hasOwnProperty(uname0);
      }
      
      public function panUidAndUname(uid0:String, uname0:String) : Boolean
      {
         return this.getUid(uid0) || this.getUname(uname0);
      }
   }
}


package UI.bag
{
   import UI.bag.ItemsEditCtrl;
   import UI.bag.ItemsGripBox;
   import flash.utils.setTimeout;
   
   /**
    * 物品编辑功能管理器
    * 负责在合适的时机启用编辑功能，避免影响游戏加载性能
    */
   public class ItemsEditManager
   {
      private static var instance:ItemsEditManager;
      private static var isGameFullyLoaded:Boolean = false;
      private static var pendingBoxes:Array = [];
      private static var enabledBoxes:Array = [];
      
      public function ItemsEditManager()
      {
         if(instance != null)
         {
            throw new Error("ItemsEditManager is a singleton!");
         }
      }
      
      public static function getInstance():ItemsEditManager
      {
         if(instance == null)
         {
            instance = new ItemsEditManager();
         }
         return instance;
      }
      
      /**
       * 注册需要编辑功能的ItemsGripBox
       * 如果游戏已完全加载，立即启用；否则加入待处理队列
       */
      public static function registerBox(box:ItemsGripBox):void
      {
         if(!box) return;
         
         // 避免重复注册
         if(pendingBoxes.indexOf(box) >= 0 || enabledBoxes.indexOf(box) >= 0) {
            return;
         }
         
         if(isGameFullyLoaded) {
            // 游戏已加载完成，立即启用编辑功能
            enableEditForBox(box);
         } else {
            // 游戏还在加载，加入待处理队列
            pendingBoxes.push(box);
         }
      }
      
      /**
       * 为单个Box启用编辑功能
       */
      private static function enableEditForBox(box:ItemsGripBox):void
      {
         try {
            ItemsEditCtrl.addEvent_byItemsGripBox(box);
            enabledBoxes.push(box);
            
            // 从待处理队列中移除
            var index:int = pendingBoxes.indexOf(box);
            if(index >= 0) {
               pendingBoxes.splice(index, 1);
            }
         } catch(e:Error) {
            // 启用失败不影响游戏运行
         }
      }
      
      /**
       * 标记游戏已完全加载，启用所有待处理的编辑功能
       */
      public static function markGameFullyLoaded():void
      {
         if(isGameFullyLoaded) return;
         
         isGameFullyLoaded = true;
         
         // 延迟启用编辑功能，确保不影响加载流程
         setTimeout(function():void {
            enableAllPendingBoxes();
         }, 2000); // 2秒延迟，确保游戏完全稳定
      }
      
      /**
       * 启用所有待处理的编辑功能
       */
      private static function enableAllPendingBoxes():void
      {
         var box:ItemsGripBox;
         var tempBoxes:Array = pendingBoxes.concat(); // 创建副本避免修改原数组时出错
         
         for each(box in tempBoxes) {
            enableEditForBox(box);
         }
         
         // 清空待处理队列
         pendingBoxes.length = 0;
      }
      
      /**
       * 移除Box的编辑功能
       */
      public static function unregisterBox(box:ItemsGripBox):void
      {
         if(!box) return;
         
         try {
            ItemsEditCtrl.removeEvent_byItemsGripBox(box);
         } catch(e:Error) {
            // 移除失败不影响游戏运行
         }
         
         // 从所有列表中移除
         var index:int = pendingBoxes.indexOf(box);
         if(index >= 0) {
            pendingBoxes.splice(index, 1);
         }
         
         index = enabledBoxes.indexOf(box);
         if(index >= 0) {
            enabledBoxes.splice(index, 1);
         }
      }
      
      /**
       * 获取当前状态信息
       */
      public static function getStatusInfo():String
      {
         var info:String = "编辑功能管理器状态:\n";
         info += "游戏已完全加载: " + (isGameFullyLoaded ? "是" : "否") + "\n";
         info += "待处理Box数量: " + pendingBoxes.length + "\n";
         info += "已启用Box数量: " + enabledBoxes.length;
         return info;
      }
      
      /**
       * 强制启用所有编辑功能（调试用）
       */
      public static function forceEnableAll():void
      {
         isGameFullyLoaded = true;
         enableAllPendingBoxes();
      }
      
      /**
       * 检查游戏是否已完全加载
       */
      public static function isGameReady():Boolean
      {
         return isGameFullyLoaded;
      }

      /**
       * 显示状态信息到游戏中（调试用）
       */
      public static function showStatus():void
      {
         try {
            var statusInfo:String = getStatusInfo();
            Gaming.uiGroup.alertBox.showInfo(statusInfo);
         } catch(e:Error) {
            // 显示失败不影响游戏运行
         }
      }
   }
}

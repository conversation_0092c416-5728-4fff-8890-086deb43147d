package dataAll._app.top.define
{
   import com.sounto.utils.ClassProperty;
   
   public class TopBarDefine
   {
      public static var pro_arr:Array = [];
      
      public var cnName:String = "";
      
      public var proUrl:String = "";
      
      public function TopBarDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
   }
}


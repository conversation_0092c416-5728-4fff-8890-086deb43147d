package dataAll._app.active
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.TextMethod;
   import dataAll._app.active.define.ActiveGiftDefine;
   import dataAll._app.active.define.ActiveTask;
   import dataAll._app.active.define.ActiveTaskDefine;
   import dataAll._app.union.define.DonationDefine;
   import dataAll._player.PlayerData;
   import dataAll.gift.save.GiftSave;
   
   public class ActiveData
   {
      public static var TEST_ACTIVE:int = 0;
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var playerData:PlayerData;
      
      public var save:ActiveSave;
      
      public function ActiveData()
      {
         super();
         this.nowActive = 0;
      }
      
      public function get nowActive() : Number
      {
         return this.CF.getAttribute("nowActive");
      }
      
      public function set nowActive(v0:Number) : void
      {
         this.CF.setAttribute("nowActive",v0);
      }
      
      public function inData_bySave(s0:ActiveSave) : void
      {
         this.save = s0;
      }
      
      public function newDayCtrl() : void
      {
         this.save.newDayCtrl();
      }
      
      public function getTaskDataArr() : Array
      {
         var d0:ActiveTaskDefine = null;
         var da0:ActiveTaskData = null;
         var dataArr0:Array = [];
         var defineArr0:Array = Gaming.defineGroup.active.taskArr;
         var nowActive0:int = 0;
         for each(d0 in defineArr0)
         {
            da0 = new ActiveTaskData();
            da0.def = d0;
            da0.now = this.getTaskValueByLabel(d0.name);
            dataArr0.push(da0);
            if(da0.getCompleteB())
            {
               nowActive0 += d0.active;
            }
         }
         if(TEST_ACTIVE > 0)
         {
            this.nowActive = TEST_ACTIVE;
         }
         else
         {
            this.nowActive = nowActive0;
         }
         this.save.getAcc().activityPan(this.nowActive);
         return dataArr0;
      }
      
      public function getTaskValueByLabel(label0:String) : int
      {
         var taskLabel0:String = null;
         var giftSave0:GiftSave = this.playerData.getSave().gift;
         if(label0 == ActiveTask.dailySign)
         {
            return giftSave0.daily.todayHaveGetGiftB() ? 1 : 0;
         }
         if(label0 == ActiveTask.vipDay)
         {
            return this.playerData.vip.getDayGiftB() ? 1 : 0;
         }
         if(label0 == ActiveTask.loveGift)
         {
            return this.playerData.moreWay.getLoveGiftNumDay();
         }
         if(label0 == ActiveTask.ask)
         {
            return this.playerData.ask.getNowState() == "end" ? 1 : 0;
         }
         if(label0.indexOf(ActiveTask.Task) > 0)
         {
            taskLabel0 = label0.replace(ActiveTask.Task,"");
            return this.playerData.task.saveGroup.getTrueTodayCompleteNum(taskLabel0);
         }
         if(label0 == ActiveTask.arena)
         {
            return this.playerData.arena.getOutWinNum();
         }
         if(label0 == ActiveTask.treasure)
         {
            return giftSave0.treasureNum;
         }
         if(label0 == ActiveTask.unionCoin)
         {
            return this.playerData.union.save.getDonationNum(DonationDefine.OTHER);
         }
         if(label0 == ActiveTask.unionMoney)
         {
            return this.playerData.union.save.getDonationNum(DonationDefine.MONEY);
         }
         if(label0 == ActiveTask.unionFederal)
         {
            return this.playerData.union.save.building.federalState > 0 ? 1 : 0;
         }
         if(label0 == ActiveTask.endlessLevel)
         {
            return this.playerData.worldMap.saveGroup.todayEndlessNum;
         }
         if(label0 == ActiveTask.normalLevel)
         {
            return this.playerData.getSave().getCount().normalLevelNum;
         }
         if(label0 == ActiveTask.useMoney)
         {
            return this.playerData.getSave().getCount().todayUseMoney > 0 ? 1 : 0;
         }
         if(label0 == ActiveTask.useArenaStamp)
         {
            return this.playerData.getSave().getCount().todayArenaStamp > 0 ? 1 : 0;
         }
         if(label0 == ActiveTask.gm3)
         {
            return this.playerData.gift.save.anniverGm.useNum;
         }
         if(label0 == ActiveTask.useExploitCards)
         {
            return this.playerData.getSave().getCount().todayExploitCards > 0 ? 1 : 0;
         }
         if(label0 == ActiveTask.strength)
         {
            return this.playerData.getSave().getCount().todayStrengthNum;
         }
         if(label0 == ActiveTask.petDispatch)
         {
            return this.playerData.pet.dispatch.getTodayB() ? 1 : 0;
         }
         if(label0 == ActiveTask.smelt)
         {
            return this.playerData.city.save.num;
         }
         if(label0 == ActiveTask.wilder)
         {
            return this.playerData.wilder.getDayNum();
         }
         if(label0 == ActiveTask.wilderKey)
         {
            return this.playerData.wilder.getDayExchangeNum();
         }
         return 0;
      }
      
      public function getGiftDataArr() : Array
      {
         var d0:ActiveGiftDefine = null;
         var da0:ActiveGiftData = null;
         var value0:int = this.nowActive;
         var dataArr0:Array = [];
         var defineArr0:Array = Gaming.defineGroup.active.giftArr;
         for each(d0 in defineArr0)
         {
            da0 = new ActiveGiftData();
            da0.init(d0);
            da0.enoughB = value0 >= d0.must;
            da0.haveGiftB = this.save.getHaveGiftB(d0.name);
            dataArr0.push(da0);
         }
         return dataArr0;
      }
      
      public function getMaxActiveValue() : int
      {
         var d0:ActiveTaskDefine = null;
         var defineArr0:Array = Gaming.defineGroup.active.taskArr;
         var max0:int = 0;
         for each(d0 in defineArr0)
         {
            max0 += d0.active;
         }
         return max0;
      }
      
      public function getActivityText() : String
      {
         var save0:AccActiveSave = this.save.getAcc();
         var day0:int = save0.day;
         var max0:int = AccActiveSave.getActivityDayMax();
         var str0:String = AccActiveSave.getDayBeforeStr() + "，累计" + max0 + "天活跃值超过" + AccActiveSave.getActivityMustMax();
         str0 += save0.dayAddB ? TextMethod.color("(今日已完成)","#0099CC") : "";
         str0 += "，将获得" + AccActiveSave.getActivityGiftStr() + "。";
         return str0 + ("当前完成" + TextMethod.colorMustNum(day0,max0) + "天。");
      }
      
      public function getActivityBtnState() : int
      {
         var day0:int = 0;
         var max0:int = 0;
         var save0:AccActiveSave = this.save.getAcc();
         if(save0.giftB)
         {
            return 2;
         }
         day0 = save0.day;
         max0 = AccActiveSave.getActivityDayMax();
         if(day0 < max0)
         {
            return 0;
         }
         return 1;
      }
      
      public function getActivityGiftEvent() : void
      {
         var save0:AccActiveSave = this.save.getAcc();
         save0.giftB = true;
         ++save0.giftNum;
      }
   }
}


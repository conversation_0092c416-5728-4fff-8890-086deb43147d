package dataAll._app.union.building.define
{
   import com.sounto.cf.NiuBiCF;
   
   public class UnionGeologyDefine
   {
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      private var xml:XML = null;
      
      public var arr:Array = [];
      
      public var obj:Object = {};
      
      public var hangingB:Boolean = false;
      
      public var hangingChooseB:Boolean = false;
      
      public var mapScale:Number = 0.3;
      
      public function UnionGeologyDefine()
      {
         super();
         this.treaHitNum = 2;
         this.treaGap = 10;
         this.speedMul = 0;
      }
      
      public function get treaHitNum() : Number
      {
         return this.CF.getAttribute("treaHitNum");
      }
      
      public function set treaHitNum(v0:Number) : void
      {
         this.CF.setAttribute("treaHitNum",v0);
      }
      
      public function get treaGap() : Number
      {
         return this.CF.getAttribute("treaGap");
      }
      
      public function set treaGap(v0:Number) : void
      {
         this.CF.setAttribute("treaGap",v0);
      }
      
      public function get speedMul() : Number
      {
         return this.CF.getAttribute("speedMul");
      }
      
      public function set speedMul(v0:Number) : void
      {
         this.CF.setAttribute("speedMul",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var x0:XML = null;
         var d0:UnionGeologyThingsDefine = null;
         this.xml = xml0;
         if(!xml0)
         {
            return;
         }
         var xml_list0:XMLList = xml0.things;
         for(i in xml_list0)
         {
            x0 = xml_list0[i];
            d0 = new UnionGeologyThingsDefine();
            d0.inData_byXML(x0);
            this.arr.push(d0);
            this.obj[d0.name] = d0;
         }
      }
      
      public function getThings(name0:String) : UnionGeologyThingsDefine
      {
         return this.obj[name0];
      }
      
      public function copy() : UnionGeologyDefine
      {
         var d0:UnionGeologyDefine = new UnionGeologyDefine();
         d0.inData_byXML(this.xml);
         return d0;
      }
      
      public function inLv(lv0:int) : void
      {
         var oreMul0:Number = 1;
         var boxMul0:Number = 1;
         var boxDropAdd0:Number = 0;
         if(lv0 >= 2)
         {
            oreMul0 = 1.5;
         }
         if(lv0 >= 6)
         {
            oreMul0 = 2;
         }
         if(lv0 >= 10)
         {
            oreMul0 = 3;
         }
         if(lv0 >= 11)
         {
            oreMul0 = 3.5;
         }
         if(lv0 >= 3)
         {
            boxMul0 = 1.1;
         }
         if(lv0 >= 7)
         {
            boxMul0 = 1.2;
         }
         if(lv0 >= 10)
         {
            boxMul0 = 1.3;
         }
         if(lv0 >= 13)
         {
            boxMul0 = 2;
         }
         if(lv0 >= 9)
         {
            this.treaHitNum = 3;
         }
         if(lv0 >= 4)
         {
            this.treaGap = 5;
         }
         if(lv0 >= 5)
         {
            this.hangingB = true;
         }
         if(lv0 >= 8)
         {
            this.hangingChooseB = true;
         }
         if(lv0 >= 12)
         {
            this.speedMul = 0.25;
         }
         if(lv0 >= 14)
         {
            boxDropAdd0 = 1;
         }
         if(lv0 >= 15)
         {
            this.mapScale = 0.1;
         }
         if(lv0 >= 16)
         {
            this.getThings("fineStone").max = this.getThings("fineStone").max + 2;
         }
         if(lv0 >= 17)
         {
            this.getThings("lightStone").max = this.getThings("lightStone").max + 2;
         }
         if(lv0 >= 18)
         {
            this.treaGap = 1;
         }
         if(lv0 >= 19)
         {
            this.getThings("bossCardStamp").max = this.getThings("bossCardStamp").max + 2;
         }
         if(lv0 >= 20)
         {
            this.treaHitNum = 4;
         }
         if(lv0 >= 21)
         {
            oreMul0 = 4.5;
         }
         if(lv0 >= 22)
         {
            this.treaHitNum = 5;
         }
         this.inOreProMul(oreMul0 * 3,1);
         this.inTreaBoxProMul(boxMul0,boxDropAdd0);
      }
      
      public function getLevelStrArr() : Array
      {
         var arr0:Array = [];
         arr0.push("无限联邦铁铲和铁锹");
         arr0.push("1.5倍挖矿成功率");
         arr0.push("1.5倍宝箱爆率");
         arr0.push("寻宝等待时间减少");
         arr0.push("自动挖矿功能");
         arr0.push("2倍挖矿成功率");
         arr0.push("2倍宝箱爆率");
         arr0.push("高级挖矿功能");
         arr0.push("寻宝摸索次数+1");
         arr0.push("3倍宝箱爆率");
         arr0.push("3倍挖矿成功率");
         arr0.push("人物移动速度+25%");
         arr0.push("4倍宝箱爆率");
         arr0.push("提高宝箱掉落上限+1");
         arr0.push("寻宝雷达范围扩大");
         arr0.push("挖矿可挖出精石");
         arr0.push("光能石掉落上限+2");
         arr0.push("寻宝等待时间大幅降低");
         arr0.push("寻宝有几率出现魂卡券");
         arr0.push("寻宝摸索次数+1");
         arr0.push("4倍挖矿成功率");
         arr0.push("寻宝摸索次数+1");
         return arr0;
      }
      
      private function inOreProMul(mul0:Number, dropMul0:Number = 1) : void
      {
         var d0:UnionGeologyThingsDefine = null;
         for each(d0 in this.obj)
         {
            if(d0.max > 0)
            {
               d0.orePro *= mul0;
               if(dropMul0 > 0)
               {
                  if(d0.name.indexOf("Chest") == -1)
                  {
                     d0.max = Math.round(d0.max * dropMul0);
                  }
               }
            }
         }
      }
      
      private function inTreaBoxProMul(mul0:Number, dropAdd0:int) : void
      {
         var d0:UnionGeologyThingsDefine = this.getThings("normalChest");
         var d1:UnionGeologyThingsDefine = this.getThings("magicChest");
         d0.treaPro *= mul0;
         d1.treaPro *= mul0;
         d0.max += dropAdd0;
         d1.max += dropAdd0;
      }
   }
}


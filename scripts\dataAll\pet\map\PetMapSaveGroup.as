package dataAll.pet.map
{
   import com.sounto.utils.ClassProperty;
   import dataAll._player.IO_PlayerLevelGetter;
   import dataAll.level.LevelDiffGetting;
   import dataAll.pet.PetCount;
   import dataAll.pet.gene.define.GeneDefine;
   
   public class PetMapSaveGroup
   {
      public static var pro_arr:Array = [];
      
      public var obj:Object = {};
      
      public function PetMapSaveGroup()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.obj = ClassProperty.copySaveObj(obj0["obj"],PetMapSave);
      }
      
      public function getDropPro(name0:String) : Number
      {
         var s0:PetMapSave = null;
         if(this.obj.hasOwnProperty(name0))
         {
            s0 = this.obj[name0];
            return s0.getDropPro();
         }
         return 0.001;
      }
      
      public function setDropPro(name0:String, v0:Number, addB0:Boolean = false) : void
      {
         var s0:PetMapSave = null;
         if(!this.obj.hasOwnProperty(name0))
         {
            s0 = new PetMapSave();
            s0.name = name0;
            this.obj[name0] = s0;
         }
         else
         {
            s0 = this.obj[name0];
         }
         if(addB0)
         {
            s0.nowDropNum += v0;
         }
         else
         {
            s0.nowDropNum = v0;
         }
      }
      
      public function enemyDropPan(unitName0:String, enemyLife0:Number, enemyLv0:int, heroLv0:int, diff0:int, lg0:IO_PlayerLevelGetter) : Boolean
      {
         var name0:String = null;
         var pro0:Number = NaN;
         var bb0:Boolean = false;
         var num0:Number = NaN;
         var mustLv0:int = PetCount.openLevel;
         if(enemyLv0 < mustLv0 || heroLv0 < mustLv0)
         {
            return false;
         }
         var geneDefine0:GeneDefine = Gaming.defineGroup.gene.getDefineByTargetName(unitName0);
         if(geneDefine0 is GeneDefine)
         {
            if(!geneDefine0.superB)
            {
               name0 = geneDefine0.name;
               pro0 = this.getDropPro(name0);
               bb0 = Math.random() <= pro0;
               if(bb0)
               {
                  this.setDropPro(name0,0,false);
                  return true;
               }
               num0 = this.countDropNum(enemyLife0,enemyLv0,heroLv0,diff0,lg0);
               this.setDropPro(name0,num0,true);
               return false;
            }
            return false;
         }
         return false;
      }
      
      private function countDropNum(enemyLife0:Number, enemyLv0:int, heroLv0:int, diff0:int, lg0:IO_PlayerLevelGetter) : Number
      {
         if(enemyLv0 < heroLv0)
         {
            enemyLv0 = heroLv0;
         }
         var diffMul0:Number = LevelDiffGetting.getLifeDiffBy(diff0,lg0.getMapMode(),lg0.getNowWorldMapName());
         if(diffMul0 < 1)
         {
            diffMul0 = 1;
         }
         enemyLife0 = enemyLife0 / diffMul0 * ((diffMul0 - 1) / 2 + 1);
         var normalLife0:Number = Gaming.defineGroup.normal.getEnemyLife(heroLv0);
         var num0:Number = enemyLife0 / normalLife0;
         if(num0 > 50)
         {
            num0 = 50;
         }
         if(num0 < 0.05)
         {
            num0 = 0.05;
         }
         return num0;
      }
   }
}


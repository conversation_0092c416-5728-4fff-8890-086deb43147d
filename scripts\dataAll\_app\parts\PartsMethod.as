package dataAll._app.parts
{
   import UI.bag.ItemsGripBtnListCtrl;
   import UI.bag.ItemsGripMoveCtrl;
   import UI.base.CheckText;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.parts.define.PartsConst;
   import dataAll._app.parts.define.PartsRare;
   import dataAll._app.parts.define.PartsType;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.SwapFail;
   import dataAll.must.define.MustDefine;
   import dataAll.things.ThingsData;
   import dataAll.things.define.ThingsDefine;
   import dataAll.things.save.ThingsSave;
   import dataAll.ui.tip.CheckData;
   
   public class PartsMethod
   {
      public function PartsMethod()
      {
         super();
      }
      
      public static function canB() : Boolean
      {
         return Gaming.LG.state == "no" && Gaming.PG.da.level >= 15;
      }
      
      public static function levelCanB() : Boolean
      {
         return Gaming.PG.da.level >= 15;
      }
      
      public static function getSellMaxLv() : int
      {
         var mlv0:int = Gaming.PG.da.level;
         if(mlv0 > 84)
         {
            mlv0 = 84;
         }
         var lv0:int = mlv0 - 15;
         if(lv0 < 1)
         {
            lv0 = 1;
         }
         return lv0;
      }
      
      public static function decompose(da0:IO_ItemsData, dg0:ItemsDataGroup, partsBag0:PartsDataGroup) : CheckText
      {
         var sameNum0:int = 0;
         var checkText0:CheckText = new CheckText();
         var saveArr0:Array = PartsCreator.getDecomposeArr(da0,true);
         saveArr0 = saveArr0.concat();
         var bagStr0:String = partsBag0.bagSpacePanBySaveArr(saveArr0);
         if(bagStr0 == "")
         {
            sameNum0 = dg0.getSameSaveNum(da0.getSave());
            if(sameNum0 >= 2)
            {
               checkText0.state = CheckText.FAIL;
               checkText0.tipStr0 = "当前物品无法拆解。";
            }
            else
            {
               partsBag0.addBySaveArr(saveArr0);
               dg0.removeData(da0);
               Gaming.TG.dat.remove(da0,dg0);
               checkText0.tipStr0 = "拆解成功！获得以下零件：\n" + getAlertTextBySaveArr(saveArr0);
            }
         }
         else
         {
            checkText0.state = CheckText.FAIL;
            checkText0.tipStr0 = bagStr0;
         }
         return checkText0;
      }
      
      public static function batchDecompose(dg0:ItemsDataGroup, partsBag0:PartsDataGroup, stateArr0:Array, setArr0:Array = null) : CheckText
      {
         var da0:IO_ItemsData = null;
         var partsSaveArr0:Array = null;
         var checkText0:CheckText = new CheckText();
         var sellArr0:Array = setArr0;
         if(!setArr0)
         {
            sellArr0 = dg0.getBatchSellDataArr(stateArr0);
         }
         if(sellArr0.length == 0)
         {
            checkText0.tipStr0 = "没有可用于一键拆解的物品。";
            checkText0.state = CheckText.NO_DATA;
            return checkText0;
         }
         var delArr0:Array = [];
         var arr_len0:int = int(sellArr0.length);
         var bagStr0:String = "";
         var decomposedNum0:int = 0;
         for(var i:int = 0; i < arr_len0; i++)
         {
            da0 = sellArr0[i];
            if(!PartsCreator.canDecomposePan(da0))
            {
               break;
            }
            partsSaveArr0 = PartsCreator.getDecomposeArr(da0,true);
            bagStr0 = partsBag0.bagSpacePanBySaveArr(partsSaveArr0);
            if(bagStr0 != "")
            {
               break;
            }
            partsBag0.addBySaveArr(partsSaveArr0);
            delArr0.push(da0);
            decomposedNum0++;
         }
         dg0.removeDataArr(delArr0);
         checkText0.tipStr0 = "已拆解物品 " + ComMethod.color(decomposedNum0 + "","#00FF00") + " 个。";
         if(bagStr0 != "")
         {
            checkText0.state = CheckText.BREACK;
            checkText0.tipStr0 = "当前零件背包不足，拆解中止。\n" + checkText0.tipStr0;
         }
         else
         {
            checkText0.tipStr0 = "拆解完成！\n" + checkText0.tipStr0;
         }
         if(Boolean(setArr0))
         {
            Gaming.uiGroup.alertBox.showSuccess(checkText0.tipStr0);
         }
         return checkText0;
      }
      
      public static function getAlertTextBySaveArr(arr0:Array) : String
      {
         var n:* = undefined;
         var s0:ThingsSave = null;
         var tip0:String = null;
         var line0:int = arr0.length > 3 ? 2 : 1;
         var str0:String = "";
         for(n in arr0)
         {
            s0 = arr0[n];
            tip0 = s0.getPartsAlertText();
            str0 += tip0;
            if(n < arr0.length - 1)
            {
               if((n + 1) % line0 == 1)
               {
                  str0 += "、";
               }
               else
               {
                  str0 += "\n";
               }
            }
         }
         return str0;
      }
      
      public static function getComposeMustDefine(da0:ThingsData, num0:int = 1) : MustDefine
      {
         var s0:ThingsSave = da0.save;
         var d0:MustDefine = new MustDefine();
         var coin0:Number = getComposeMustCoin(da0,num0);
         var mustNum0:int = getComposeMustNum(s0,num0);
         d0.lv = s0.getPartsNextLv();
         d0.coin = coin0;
         d0.inThingsDataByArr([s0.name + ";" + mustNum0]);
         return d0;
      }
      
      public static function getComposeMustCoin(da0:ThingsData, num0:int = 1) : Number
      {
         var coin0:Number = 0;
         var s0:ThingsSave = da0.save;
         var nextLv0:int = s0.getPartsNextLv();
         var heroLv0:int = nextLv0;
         if(da0.playerData is NormalPlayerData)
         {
            heroLv0 = da0.playerData.level;
         }
         if(nextLv0 < 25)
         {
            coin0 = Math.ceil(nextLv0 * nextLv0 * 2 * num0);
         }
         else
         {
            coin0 = Math.ceil(nextLv0 * 50 * num0);
         }
         var mul0:Number = getComposeMustCoinMul(heroLv0,nextLv0);
         return Math.ceil(coin0 * mul0 * 0.1);
      }
      
      private static function getComposeMustCoinMul(heroLv0:int, lv0:int) : Number
      {
         var v0:Number = NaN;
         if(heroLv0 <= lv0 + 5)
         {
            return 1;
         }
         v0 = 3 / (heroLv0 - lv0 - 2);
         if(v0 < 0.1)
         {
            v0 = 0.1;
         }
         return v0;
      }
      
      public static function getComposeMustNum(s0:ThingsSave, num0:int = 1) : int
      {
         if(s0.isPartsSpecialB())
         {
            return PartsConst.getSpecialComposeMustNum(s0.getTrueLevel()) * num0;
         }
         if(s0.isPartsNormalB())
         {
            return PartsConst.getComposeMustNum(s0.getTrueLevel()) * num0;
         }
         if(s0.isPartsRareB())
         {
            return PartsRare.getComposeMustNum(s0.getTrueLevel(),s0.getDefine()) * num0;
         }
         return 999999;
      }
      
      public static function batchCompose(partsBag0:PartsDataGroup) : CheckText
      {
         return null;
      }
      
      public static function unload(da0:ThingsData, dg0:PartsDataGroup) : void
      {
         var d0:ThingsDefine = da0.save.getDefine();
         var bag_pg0:PartsDataGroup = Gaming.PG.da.partsBag;
         var same_da0:ThingsData = bag_pg0.getDataBySaveName(da0.save.name) as ThingsData;
         var bag_site0:int = -1;
         if(Boolean(same_da0))
         {
            bag_site0 = same_da0.save.site;
         }
         else
         {
            bag_site0 = bag_pg0.spaceSiteOf();
         }
         if(bag_site0 == -1)
         {
            Gaming.uiGroup.alertBox.showNormal("零件背包不足，还需要" + ComMethod.color(" 1 ","#00FF00") + "个空位。","yes",null,null,"no");
         }
         else
         {
            ItemsGripMoveCtrl.swap(dg0,bag_pg0,da0.save.site,bag_site0);
            ItemsGripBtnListCtrl.fleshAllBy(dg0);
         }
      }
      
      public static function unloadCanUnload(dg0:PartsDataGroup) : CheckData
      {
         var arr0:Array = dg0.getCanUnloadArr();
         return unloadArr(arr0,dg0);
      }
      
      public static function unloadArr(arr0:Array, dg0:PartsDataGroup) : CheckData
      {
         var check0:CheckData = new CheckData();
         var bag_dg0:PartsDataGroup = Gaming.PG.da.partsBag;
         var str0:String = bag_dg0.bagSpacePanByDataArr(arr0);
         if(str0 != "")
         {
            check0.bb = false;
            check0.info = str0;
         }
         else
         {
            dg0.removeDataArr(arr0);
            bag_dg0.addByDataArr(arr0);
         }
         return check0;
      }
      
      public static function unloadAll(dg0:PartsDataGroup) : CheckData
      {
         var check0:CheckData = new CheckData();
         var bag_dg0:PartsDataGroup = Gaming.PG.da.partsBag;
         var str0:String = bag_dg0.bagSpacePanBySaveArr(dg0.saveGroup.arr);
         if(str0 != "")
         {
            check0.bb = false;
            check0.info = str0;
         }
         else
         {
            bag_dg0.addBySaveArr(dg0.saveGroup.arr);
            dg0.clearData();
         }
         return check0;
      }
      
      public static function load(da0:ThingsData, dg0:PartsDataGroup) : void
      {
         var arms_pg0:PartsDataGroup = Gaming.uiGroup.partsUI.assemblyBox.nowArmsData.partsData;
         var wear_site0:int = arms_pg0.getWearSiteByThingsType(da0.save.getPartsType());
         if(wear_site0 >= 0)
         {
            ItemsGripMoveCtrl.swap(dg0,arms_pg0,da0.save.site,wear_site0);
            ItemsGripBtnListCtrl.fleshAllBy(dg0);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("该零件无法装配！");
         }
      }
      
      public static function loadCan(da0:ArmsData) : CheckData
      {
         var armsLv0:int = 0;
         var bag_dg0:PartsDataGroup = null;
         var typeArr0:Array = null;
         var site0:int = 0;
         var gripType0:* = null;
         var supportB0:Boolean = false;
         var bestDa0:ThingsData = null;
         var dg0:PartsDataGroup = da0.partsData;
         var check0:CheckData = unloadCanUnload(dg0);
         if(check0.bb)
         {
            armsLv0 = da0.save.getTrueLevel();
            bag_dg0 = Gaming.PG.da.partsBag;
            typeArr0 = PartsType.canLoadArr;
            site0 = 0;
            for each(gripType0 in typeArr0)
            {
               supportB0 = PartsType.supportType(da0,gripType0);
               if(supportB0)
               {
                  bestDa0 = bag_dg0.getBestByGripType(gripType0,armsLv0);
                  if(bestDa0 is ThingsData)
                  {
                     check0 = PartsDataGroup.partsSwapTo(bag_dg0,dg0,bestDa0.save.site,site0,armsLv0);
                     if(check0.name == SwapFail.wearLevelTooHigh)
                     {
                        check0.info = "零件等级不能超过武器等级。操作中止。";
                     }
                     if(!check0.bb)
                     {
                        break;
                     }
                  }
               }
               site0++;
            }
         }
         return check0;
      }
   }
}


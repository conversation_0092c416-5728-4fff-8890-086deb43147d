package dataAll._app.city.dress
{
   import dataAll._app.city.define.CityBodyDefine;
   
   public class CityDressData
   {
      private var mould:CityDressMould;
      
      private var save:CityDressSave;
      
      private var def:CityBodyDefine;
      
      public function CityDressData()
      {
         super();
      }
      
      public function inData(mould0:CityDressMould, save0:CityDressSave) : void
      {
         this.mould = mould0;
         this.save = save0;
         this.def = mould0.getDefine();
      }
      
      public function getDataId() : String
      {
         return this.save.saveId;
      }
      
      public function getDefine() : CityBodyDefine
      {
         return this.def;
      }
      
      public function getMould() : CityDressMould
      {
         return this.mould;
      }
      
      public function getSave() : CityDressSave
      {
         return this.save;
      }
      
      public function getImgUrl() : String
      {
         return this.mould.getImgUrl();
      }
      
      public function fleshMouldAddNum() : void
      {
         this.mould.addAddNum();
      }
      
      public function getUseSpace() : int
      {
         return this.mould.getUseSpace();
      }
   }
}


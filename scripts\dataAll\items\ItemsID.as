package dataAll.items
{
   import com.common.text.TextWay;
   import dataAll.equip.define.EquipColor;
   
   public class ItemsID
   {
      public function ItemsID()
      {
         super();
      }
      
      public static function getID(da0:IO_ItemsData, lastId0:Number) : String
      {
         var str0:String = "";
         str0 += da0.getTypeId();
         str0 += "_" + EquipColor.getID_byType(da0.getColor());
         str0 += "_" + TextWay.toNum(String(999 - da0.getSave().getTrueLevel()),3);
         str0 += "_" + da0.getChildTypeId();
         return str0 + ("_" + TextWay.toNum(String(lastId0),8));
      }
      
      public static function getID2(da0:IO_ItemsData, lastId0:Number) : String
      {
         var str0:String = "";
         str0 += da0.getTypeId() + da0.getChildTypeId();
         str0 += "_" + EquipColor.getID_byType(da0.getColor());
         str0 += "_" + TextWay.toNum(String(999 - da0.getSave().getTrueLevel()),3);
         return str0 + ("_" + TextWay.toNum(String(lastId0),8));
      }
   }
}


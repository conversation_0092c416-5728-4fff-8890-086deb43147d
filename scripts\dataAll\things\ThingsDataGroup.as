package dataAll.things
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.utils.ArrayMethod;
   import dataAll._player.PlayerData;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.equip.define.EquipDefine;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.find.IO_CnNameFinder;
   import dataAll.items.save.ItemsSave;
   import dataAll.items.save.ItemsSaveGroup;
   import dataAll.things.define.ThingsDefine;
   import dataAll.things.define.ThingsEffectDefine;
   import dataAll.things.define.ThingsName;
   import dataAll.things.define.ThingsSmeltType;
   import dataAll.things.save.ThingsSave;
   import dataAll.things.save.ThingsSaveGroup;
   import gameAll.level.endless.EndlessModelCtrl;
   
   public class ThingsDataGroup extends ItemsDataGroup implements IO_CnNameFinder
   {
      public var saveGroup:ThingsSaveGroup;
      
      public var useNumObj:Object = {};
      
      public var dayUseNumObj:Object = {};
      
      private var sortFunIndex:int = 0;
      
      private var sortFunArr:Array;
      
      public function ThingsDataGroup()
      {
         this.sortFunArr = [this.sortByIndex,this.sortByNum];
         super();
         dataType = ItemsDataGroup.TYPE_THINGS;
         placeType = ItemsDataGroup.PLACE_BAG;
      }
      
      override public function inData_bySaveGroup(sg0:ItemsSaveGroup) : void
      {
         var n:* = undefined;
         var s0:ThingsSave = null;
         var da0:ThingsData = null;
         clearData();
         this.saveGroup = sg0 as ThingsSaveGroup;
         for(n in sg0.arr)
         {
            s0 = sg0.arr[n];
            da0 = new ThingsData();
            da0.inData_bySave(s0,normalPlayerData,this);
            da0.setPlaceType(placeType);
            dataArr[n] = da0;
            _siteDataArr = null;
         }
      }
      
      override public function addSave(s0:ItemsSave, fleshSaveGroupB0:Boolean = true) : IO_ItemsData
      {
         var da0:ThingsData = new ThingsData();
         da0.inData_bySave(s0 as ThingsSave,playerData,this);
         this.addData(da0,fleshSaveGroupB0);
         return da0;
      }
      
      override public function addData(da0:IO_ItemsData, fleshSaveGroupB0:Boolean = true) : void
      {
         super.addData(da0,fleshSaveGroupB0);
      }
      
      override protected function getID(da0:IO_ItemsData) : String
      {
         var da2:ThingsData = da0 as ThingsData;
         var str0:String = "";
         str0 += da0.getTypeId() + da0.getChildTypeId();
         str0 += "_" + TextWay.toNum(da2.save.getDefine().index + "",3);
         return str0 + ("_" + TextWay.toNum(this.getSaveGroup().lastId + "",8));
      }
      
      override public function newDayCtrl(timeStr0:String) : void
      {
         this.dayUseNumObj = {};
      }
      
      public function addGiftGroup(g0:GiftAddDefineGroup) : void
      {
         var gift0:GiftAddDefine = null;
         for each(gift0 in g0.arr)
         {
            if(gift0.type == "things")
            {
               this.addDataByName(gift0.name,gift0.num);
            }
         }
      }
      
      public function removeGiftGroup(g0:GiftAddDefineGroup) : void
      {
         var gift0:GiftAddDefine = null;
         for each(gift0 in g0.arr)
         {
            if(gift0.type == "things")
            {
               this.useThings(gift0.name,gift0.num);
            }
         }
      }
      
      public function addDataByName(name0:String, num0:int = 1) : IO_ItemsData
      {
         var da0:ThingsData = null;
         var d0:ThingsDefine = Gaming.defineGroup.things.getDefine(name0);
         if(playerData.isMainPlayerB())
         {
            playerData.drop.addThingsName(d0.name,num0);
         }
         if(!d0.noOverlayB)
         {
            da0 = getDataBySaveName(name0) as ThingsData;
            if(Boolean(da0))
            {
               da0.save.nowNum += num0;
               da0.newB = true;
               return da0;
            }
         }
         var s0:ThingsSave = new ThingsSave();
         s0.inData_byDefine(d0);
         s0.nowNum = num0;
         return this.addSave(s0);
      }
      
      public function addDataByNameNew(name0:String, num0:int = 1) : ThingsData
      {
         var d0:ThingsDefine = Gaming.defineGroup.things.getDefine(name0);
         var s0:ThingsSave = new ThingsSave();
         s0.inData_byDefine(d0);
         s0.nowNum = num0;
         return this.addSave(s0) as ThingsData;
      }
      
      public function useThings(name0:String, num0:int = 1, doEffectB0:Boolean = false, setCountB0:Boolean = true) : String
      {
         var d0:ThingsDefine = null;
         var saveNowNum0:Number = NaN;
         var limitNum0:int = 0;
         var i:int = 0;
         if(num0 < 0)
         {
            return "noThings";
         }
         var da0:ThingsData = getDataBySaveName(name0) as ThingsData;
         if(Boolean(da0))
         {
            d0 = da0.save.getDefine();
            saveNowNum0 = da0.save.nowNum;
            if(saveNowNum0 == num0)
            {
               saveNowNum0 -= num0;
               da0.save.nowNum = saveNowNum0;
               removeData(da0);
            }
            else if(saveNowNum0 > num0)
            {
               saveNowNum0 -= num0;
               da0.save.nowNum = saveNowNum0;
            }
            else
            {
               INIT.showErrorMust("使用数量不能超越当前数量：" + saveNowNum0 + "-" + num0);
            }
            if(normalPlayerData.isMainPlayerB())
            {
               ThingsUseCtrl.playerUseThings(this,da0,num0);
            }
            if(d0.name == "normalChest")
            {
               if(playerData.isMainPlayerB())
               {
                  playerData.drop.addThingsName(d0.name,-num0);
               }
            }
            else if(d0.name == ThingsName.bossSumCard)
            {
               playerData.main.bossSumCardUseEvent(Gaming.LG.isDemonOnlyThingsB(),num0);
            }
            limitNum0 = this.getThingsLevelUseLimitNum(d0.name);
            if(ThingsEffectDefine.haveLimitPan(limitNum0))
            {
               this.addLevelUseNum(d0.name,num0);
            }
            if(ThingsEffectDefine.haveLimitPan(d0.effectD.dayUseLimit))
            {
               this.addDayUseNum(d0.name,num0);
            }
            if(doEffectB0 && d0.effectD.type != "")
            {
               for(i = 0; i < num0; i++)
               {
                  ThingsProps.doEffect(this,d0);
               }
            }
            if(setCountB0)
            {
               Gaming.api.rzxt.useThings(da0,num0,saveNowNum0);
            }
         }
         else
         {
            INIT.showErrorMust("在背包中找不到物品：" + name0);
         }
         return "";
      }
      
      private function getThingsLevelUseLimitNum(name0:String) : int
      {
         if(!(playerData is PlayerData))
         {
            return -1;
         }
         return playerData.getThingsLevelUseLimitNum(name0);
      }
      
      public function getLevelCanUseNum(name0:String) : int
      {
         var d0:ThingsDefine = Gaming.defineGroup.things.getDefine(name0);
         var limit0:int = this.getThingsLevelUseLimitNum(name0);
         if(ThingsEffectDefine.haveLimitPan(limit0))
         {
            return limit0 - this.getLevelUseNum(name0);
         }
         return ThingsEffectDefine.NO_LIMIT;
      }
      
      public function getLevelUseNum(name0:String) : int
      {
         if(this.useNumObj.hasOwnProperty(name0))
         {
            return int(Sounto64.decode(this.useNumObj[name0]));
         }
         return 0;
      }
      
      protected function addLevelUseNum(name0:String, v0:int) : void
      {
         var now0:int = this.getLevelUseNum(name0);
         this.useNumObj[name0] = Sounto64.encode(v0 + now0 + "");
      }
      
      public function clearAllUseNum() : void
      {
         this.useNumObj = {};
      }
      
      public function getDayUseNum(name0:String) : int
      {
         if(this.dayUseNumObj.hasOwnProperty(name0))
         {
            return int(Sounto64.decode(this.dayUseNumObj[name0]));
         }
         return 0;
      }
      
      public function getDayCanUseNum(name0:String) : int
      {
         var d0:ThingsDefine = Gaming.defineGroup.things.getDefine(name0);
         var limit0:int = d0.effectD.dayUseLimit;
         if(ThingsEffectDefine.haveLimitPan(limit0))
         {
            return limit0 - this.getDayUseNum(name0);
         }
         return ThingsEffectDefine.NO_LIMIT;
      }
      
      public function addDayUseNum(name0:String, v0:int = 1) : void
      {
         var now0:int = this.getDayUseNum(name0);
         now0 += v0;
         this.dayUseNumObj[name0] = Sounto64.encode(now0 + "");
      }
      
      public function getThingsNum(name0:String) : Number
      {
         var da0:ThingsData = getDataBySaveName(name0) as ThingsData;
         if(Boolean(da0))
         {
            return da0.save.nowNum;
         }
         return 0;
      }
      
      public function getDataArrByFather(father0:String, secType0:String = "") : Array
      {
         var da0:ThingsData = null;
         var d0:ThingsDefine = null;
         var arr0:Array = [];
         for each(da0 in dataArr)
         {
            d0 = da0.save.getDefine();
            if(d0.father == father0 && (secType0 == "" || d0.secType == secType0))
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      public function getChipConverArr() : Array
      {
         var da0:ThingsData = null;
         var newArr0:Array = [];
         var arr0:Array = this.getDataArrByFather("blackChip","equip");
         for each(da0 in arr0)
         {
            if(da0.save.getDefine().canConverB())
            {
               newArr0.push(da0);
            }
         }
         return newArr0;
      }
      
      public function autoUseCondition(c0:String) : void
      {
         var da0:ThingsData = null;
         var d0:ThingsDefine = null;
         var useNumB0:Boolean = false;
         for each(da0 in dataArr)
         {
            d0 = da0.save.getDefine();
            if(d0.effectD.autoUseCondition == c0 && da0.save.autoUseB)
            {
               useNumB0 = true;
               if(d0.effectD.endlessAutoUseOneB)
               {
                  useNumB0 = !EndlessModelCtrl.dat.getAutoUseNumB(d0.name);
                  EndlessModelCtrl.dat.autoUseThings(d0.name);
               }
               if(!useNumB0)
               {
                  this.addDataByName(d0.name,1);
               }
               this.useThings(d0.name,1,true);
            }
         }
      }
      
      public function getAllPropsSkillArr() : Array
      {
         var da0:ThingsData = null;
         var d0:ThingsDefine = null;
         var skillArr0:Array = null;
         var arr0:Array = [];
         for each(da0 in dataArr)
         {
            d0 = da0.save.getDefine();
            skillArr0 = d0.effectD.skillArr;
            if(skillArr0.length > 0)
            {
               arr0 = arr0.concat(skillArr0);
            }
            if(Boolean(d0.stateD))
            {
               if(d0.stateD.skill != "")
               {
                  arr0.push(d0.stateD.skill);
               }
            }
         }
         return arr0;
      }
      
      override public function getSortBtnText() : String
      {
         return "紧凑排序";
      }
      
      override public function getSortBtnText2() : String
      {
         return "类别排序";
      }
      
      override public function sort(dg0:ItemsDataGroup) : void
      {
         var fun0:Function = ArrayMethod.getElementLimit(this.sortFunArr,this.sortFunIndex);
         fun0();
         this.sortFunIndex = (this.sortFunIndex + 1) % this.sortFunArr.length;
         _siteDataArr = null;
      }
      
      override public function sort2(dg0:ItemsDataGroup) : void
      {
         this.sortByType();
      }
      
      public function sortByIndex() : void
      {
         var n:* = undefined;
         var da0:ThingsData = null;
         var arr0:Array = dataArr.concat([]);
         arr0.sort(this.sortByIndexFun);
         for(n in arr0)
         {
            da0 = arr0[n];
            da0.save.site = n;
         }
      }
      
      private function sortByIndexFun(da0:ThingsData, da1:ThingsData) : int
      {
         var index0:Number = da0.save.getDefine().index;
         var index1:Number = da1.save.getDefine().index;
         if(index0 > index1)
         {
            return 1;
         }
         if(index0 == index1)
         {
            return 0;
         }
         return -1;
      }
      
      private function sortByNum() : void
      {
         var n:* = undefined;
         var da0:ThingsData = null;
         var arr0:Array = dataArr.concat([]);
         arr0.sort(this.sortByNumFun);
         for(n in arr0)
         {
            da0 = arr0[n];
            da0.save.site = n;
         }
      }
      
      private function sortByNumFun(da0:ThingsData, da1:ThingsData) : int
      {
         var index0:Number = da0.save.nowNum;
         var index1:Number = da1.save.nowNum;
         if(index0 < index1)
         {
            return 1;
         }
         if(index0 == index1)
         {
            return this.sortByIndexFun(da0,da1);
         }
         return -1;
      }
      
      private function sortByType() : Boolean
      {
         var arr0:Array = null;
         var bagYMax0:int = 0;
         var y0:int = 0;
         var site0:int = 0;
         var typeArr0:Array = null;
         var objId:* = undefined;
         var type0:* = null;
         var mustY0:int = 0;
         var da0:ThingsData = null;
         var newSite0:int = 0;
         var arrObj0:Object = this.getTypeSortArrObj();
         var ymax0:int = 0;
         for each(arr0 in arrObj0)
         {
            y0 = Math.ceil(arr0.length / 6);
            ymax0 += y0;
         }
         bagYMax0 = Math.floor(this.saveGroup.gripMaxNum / 6);
         if(ymax0 > bagYMax0)
         {
            return false;
         }
         site0 = 1;
         typeArr0 = ThingsSmeltType.arr.concat();
         for(objId in arrObj0)
         {
            if(typeArr0.indexOf(objId) == -1)
            {
               typeArr0.push(objId);
            }
         }
         for each(type0 in typeArr0)
         {
            arr0 = arrObj0[type0];
            if(Boolean(arr0))
            {
               mustY0 = Math.ceil(arr0.length / 6);
               arr0.sort(this.sortByIndexFun);
               for each(da0 in arr0)
               {
                  da0.save.site = site0;
                  site0++;
               }
               newSite0 = (Math.ceil(site0 / 6) + 0) * 6 + 1;
               if(newSite0 < site0)
               {
                  newSite0 = site0;
               }
               site0 = newSite0;
            }
         }
         _siteDataArr = null;
         return true;
      }
      
      private function getTypeSortArrObj() : Object
      {
         var da0:ThingsData = null;
         var d0:ThingsDefine = null;
         var type0:String = null;
         var arr0:Array = null;
         var arrObj0:Object = {};
         for each(da0 in dataArr)
         {
            d0 = da0.save.getDefine();
            type0 = d0.getSortType();
            arr0 = arrObj0[type0];
            if(!arr0)
            {
               arr0 = [];
               arrObj0[type0] = arr0;
            }
            arr0.push(da0);
         }
         return arrObj0;
      }
      
      override public function getSaveGroup() : ItemsSaveGroup
      {
         return this.saveGroup;
      }
      
      override public function getGripType() : String
      {
         return "equipGrip";
      }
      
      public function getBlackArmsNum86() : Number
      {
         var da0:ThingsData = null;
         var d0:ThingsDefine = null;
         var armsD0:ArmsRangeDefine = null;
         var num0:Number = 0;
         for each(da0 in dataArr)
         {
            d0 = da0.save.getDefine();
            if(d0.smeltD.grade == 2 && d0.smeltD.type == ThingsSmeltType.armsChip)
            {
               armsD0 = Gaming.defineGroup.bullet.getArmsRangeDefine(d0.name);
               if(armsD0.def.isCanEvoB())
               {
                  num0 += da0.getNowNum();
               }
            }
         }
         return num0;
      }
      
      public function getBlackEquipNum86() : Number
      {
         var da0:ThingsData = null;
         var d0:ThingsDefine = null;
         var equipD0:EquipDefine = null;
         var num0:Number = 0;
         for each(da0 in dataArr)
         {
            d0 = da0.save.getDefine();
            if(d0.smeltD.grade == 2 && d0.smeltD.type == ThingsSmeltType.equipChip)
            {
               equipD0 = Gaming.defineGroup.equip.getDefine(d0.name);
               if(equipD0.isCanEvoB())
               {
                  num0 += da0.getNowNum();
               }
            }
         }
         return num0;
      }
      
      public function zuobiPan() : String
      {
         var n:* = undefined;
         var da0:ThingsData = null;
         var d0:ThingsDefine = null;
         var num0:int = 0;
         var bb0:Boolean = false;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            d0 = da0.save.getDefine();
            if(Boolean(d0))
            {
               num0 = da0.save.nowNum;
               bb0 = false;
               if(d0.isPartsB())
               {
                  bb0 = num0 > 500000000;
               }
               else
               {
                  bb0 = num0 > 200000000;
               }
               if(bb0)
               {
                  return da0.save.name + "：" + da0.save.nowNum;
               }
            }
         }
         return "";
      }
      
      public function getCnArrByFind(str0:String) : Array
      {
         return Gaming.defineGroup.things.getCnArrByFind(str0);
      }
   }
}


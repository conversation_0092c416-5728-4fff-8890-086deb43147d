package com.sounto.sound
{
   public class SoundTweenOne
   {
      public var soundData:SoundData = null;
      
      public var state:String = "no";
      
      private var first:Number = -1;
      
      private var end:Number = 0;
      
      private var t:Number = 0;
      
      private var n_t:Number = 0;
      
      private var completeStopB:Boolean = true;
      
      public function SoundTweenOne()
      {
         super();
      }
      
      public function start(t0:Number, end0:Number, first0:Number = -1, completeStopB0:Boolean = true) : void
      {
         this.n_t = 0;
         this.t = t0;
         this.end = end0;
         this.completeStopB = completeStopB0;
         if(first0 != -1)
         {
            this.first = first0;
         }
         else
         {
            this.first = this.soundData.getNowVolume();
         }
         this.state = "ing";
      }
      
      public function FTimer() : void
      {
         var cx:Number = NaN;
         var v0:Number = NaN;
         this.state = "ing";
         if(true)
         {
            if(this.n_t >= this.t)
            {
               this.state = "over";
               this.soundData.setNowVolume(this.end);
               if(this.completeStopB && this.end == 0)
               {
                  this.soundData.stop();
               }
            }
            else
            {
               this.n_t += 0.03333333333333333;
               cx = this.end - this.first;
               if(cx != 0)
               {
                  v0 = this.first + cx * (this.n_t / this.t);
                  this.soundData.setNowVolume(v0);
               }
            }
         }
      }
   }
}


package UI.skill
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripMoveCtrl;
   import UI.bag.ItemsGripTipCtrl;
   import UI.bag.ItemsGripUnlockCtrl;
   import UI.bag.ItemsEditCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import dataAll.skill.HeroSkillData;
   import dataAll.skill.define.HeroSkillDefine;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class SkillWearUI extends NormalUI
   {
      public var wearBox:ItemsGripBox = new ItemsGripBox();
      
      public var bagBox:ItemsGripBox = new ItemsGripBox();
      
      private var wearTag:Sprite;
      
      private var bagTag:Sprite;
      
      private var pageBoxTag:Sprite;
      
      public function SkillWearUI()
      {
         super();
         this.wearBox.imgType = "equipGrip";
         this.wearBox.arg.init(5,2,5,5);
         this.wearBox.evt.setWantEvent(true,true,true,true,true,true);
         addChild(this.wearBox);
         this.bagBox.imgType = "equipGrip";
         this.bagBox.arg.init(5,3,5,5);
         this.bagBox.evt.setWantEvent(true,true,true,true,true,true);
         addChild(this.bagBox);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["wearTag","bagTag","pageBoxTag"];
         super.setImg(img0);
         NormalUICtrl.setTag(this.wearBox,this.wearTag);
         NormalUICtrl.setTag(this.bagBox,this.bagTag);
         this.bagBox.pageBox.x = this.pageBoxTag.x - this.bagBox.x;
         this.bagBox.pageBox.y = this.pageBoxTag.y - this.bagBox.y;
         ItemsGripMoveCtrl.addEvent_byItemsGripBox(this.wearBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.wearBox);
         ItemsGripUnlockCtrl.addEvent_byItemsGripBox(this.wearBox);
         // ItemsEditCtrl.addEvent_byItemsGripBox(this.wearBox); // 暂时禁用以提高加载性能
         ItemsGripMoveCtrl.addEvent_byItemsGripBox(this.bagBox);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.bagBox);
         // ItemsEditCtrl.addEvent_byItemsGripBox(this.bagBox); // 暂时禁用以提高加载性能
         this.bagBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.bagBox.addEventListener(ClickEvent.ON_DOUBLE_CLICK,this.gripDoubleClick);
         this.wearBox.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
         this.wearBox.addEventListener(ClickEvent.ON_DOUBLE_CLICK,this.gripDoubleClick);
         this.bagBox.pageBox.setToNormalBtn();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      public function fleshData() : void
      {
         this.wearBox.inData_byDataGroup(Gaming.PG.DATA.skill);
         this.wearBox.setAllPro("doubleClickEnabled",true);
         this.wearBox.setAllLevelTxtBySite();
         this.bagBox.inData_byDataGroup(Gaming.PG.DATA.skillBag);
         this.bagBox.setAllPro("doubleClickEnabled",true);
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var grip0:ItemsGrid = e.child as ItemsGrid;
         var skillUpgradeUI:SkillUpgradeUI = Gaming.uiGroup.skillUI.upgradeBox;
         if(skillUpgradeUI.visible)
         {
            this.gripDoubleClick(e);
         }
         else
         {
            // 显示编辑按钮
            this.showEditButton(grip0);
         }
      }
      
      public function chooseGripByData(data0:HeroSkillData) : ItemsGrid
      {
         var grip0:ItemsGrid = this.bagBox.findGripByData(data0);
         if(Boolean(grip0))
         {
            this.bagBox.setChoose_byIndex(grip0.index);
            this.wearBox.setChoose_byIndex(-1);
         }
         else
         {
            grip0 = this.wearBox.findGripByData(data0);
            if(Boolean(grip0))
            {
               this.bagBox.setChoose_byIndex(-1);
               this.wearBox.setChoose_byIndex(grip0.index);
            }
         }
         return grip0;
      }
      
      private function gripDoubleClick(e:ClickEvent) : void
      {
         var skillUpgradeUI:SkillUpgradeUI = null;
         var grip0:ItemsGrid = e.child as ItemsGrid;
         if(Boolean(grip0.itemsData))
         {
            skillUpgradeUI = Gaming.uiGroup.skillUI.upgradeBox;
            skillUpgradeUI.nowData = grip0.itemsData as HeroSkillData;
            Gaming.uiGroup.skillUI.showBox("upgrade");
            if(e.target == this.bagBox)
            {
               this.bagBox.setChoose_byIndex(e.index);
               this.wearBox.setChoose_byIndex(-1);
            }
            else
            {
               this.bagBox.setChoose_byIndex(-1);
               this.wearBox.setChoose_byIndex(e.index);
            }
         }
      }

      private var editButton:NormalBtn = null;
      private var currentEditGrip:ItemsGrid = null;

      private function showEditButton(grip0:ItemsGrid) : void
      {
         if(!grip0 || !grip0.itemsData)
         {
            this.hideEditButton();
            return;
         }

         this.currentEditGrip = grip0;

         // 创建编辑按钮（如果不存在）
         if(!this.editButton)
         {
            this.editButton = new NormalBtn();
            this.editButton.addEventListener(MouseEvent.CLICK, this.editButtonClick);

            // 先添加到显示列表
            addChild(this.editButton);

            // 设置按钮文字和样式
            try
            {
               this.editButton.setName("编辑");

               // 设置按钮大小
               this.editButton.width = 40;
               this.editButton.height = 20;

               // 设置按钮背景色
               this.editButton.graphics.clear();
               this.editButton.graphics.beginFill(0xFF6600, 0.9); // 橙色背景
               this.editButton.graphics.drawRoundRect(0, 0, 40, 20, 5, 5);
               this.editButton.graphics.endFill();

               // 设置文字样式
               if(this.editButton.textField)
               {
                  this.editButton.textField.textColor = 0xFFFFFF; // 白色文字
                  this.editButton.textField.size = 10;
                  this.editButton.textField.text = "编辑";
               }
            }
            catch(styleError:Error)
            {
               // 如果样式设置失败，至少确保有文字
               this.editButton.setName("编辑");
            }
         }

         // 设置按钮位置（在技能图标右侧，避免遮挡）
         var targetX:Number = grip0.x + grip0.width + 5; // 放在技能图标右侧
         var targetY:Number = grip0.y + (grip0.height - 20) / 2; // 垂直居中

         // 确保按钮在可见区域内
         var maxX:Number = this.width - 50; // 确保不超出界面右边界
         if(targetX > maxX)
         {
            targetX = grip0.x - 45; // 如果右侧放不下，放在左侧
         }
         if(targetY < 0) targetY = grip0.y + 5;

         this.editButton.x = targetX;
         this.editButton.y = targetY;
         this.editButton.visible = true;

         // 将按钮置于最前面
         this.setChildIndex(this.editButton, this.numChildren - 1);
      }

      private function hideEditButton() : void
      {
         if(this.editButton)
         {
            this.editButton.visible = false;
         }
         this.currentEditGrip = null;
      }

      private function editButtonClick(e:MouseEvent) : void
      {
         if(!this.currentEditGrip || !this.currentEditGrip.itemsData)
         {
            return;
         }

         var skillData:HeroSkillData = this.currentEditGrip.itemsData as HeroSkillData;
         if(skillData)
         {
            this.showSkillEditDialog(skillData);
         }
      }

      private function showSkillEditDialog(skillData:HeroSkillData) : void
      {
         var skillDefine:HeroSkillDefine = skillData.save.getDefine();
         var currentLevel:int = skillData.save.lv;
         var maxLevel:int = skillDefine.getMaxLevel();

         var editText:String = "编辑技能: " + skillDefine.cnName + "\n";
         editText += "当前等级: " + currentLevel + "/" + maxLevel + "\n\n";
         editText += "请选择操作:\n";
         editText += "1. 设置等级 (输入数字)\n";
         editText += "2. 满级 (输入 max)\n";
         editText += "3. 重置 (输入 reset)";

         Gaming.uiGroup.alertBox.textInput.showTextInput(editText, currentLevel.toString(), this.processSkillEdit, "yesAndNo", 999);
      }

      private function processSkillEdit(inputValue:String) : void
      {
         if(!this.currentEditGrip || !this.currentEditGrip.itemsData)
         {
            Gaming.uiGroup.alertBox.showError("❌ 没有选中的技能");
            return;
         }

         var skillData:HeroSkillData = this.currentEditGrip.itemsData as HeroSkillData;
         if(!skillData)
         {
            Gaming.uiGroup.alertBox.showError("❌ 技能数据无效");
            return;
         }

         var skillDefine:HeroSkillDefine = skillData.save.getDefine();
         if(!skillDefine)
         {
            Gaming.uiGroup.alertBox.showError("❌ 技能定义无效");
            return;
         }

         var maxLevel:int = skillDefine.getMaxLevel();
         if(maxLevel <= 0) maxLevel = 99; // 默认最大等级

         try
         {
            var originalLevel:int = skillData.save.lv;

            if(inputValue.toLowerCase() == "max")
            {
               // 设置为满级
               skillData.save.lv = maxLevel;
               Gaming.uiGroup.alertBox.showSuccess("✅ 技能 " + skillDefine.cnName + " 已设置为满级 (" + maxLevel + ")");
            }
            else if(inputValue.toLowerCase() == "reset")
            {
               // 重置为1级
               skillData.save.lv = 1;
               Gaming.uiGroup.alertBox.showSuccess("✅ 技能 " + skillDefine.cnName + " 已重置为1级");
            }
            else
            {
               // 设置为指定等级
               var newLevel:int = parseInt(inputValue);
               if(isNaN(newLevel) || newLevel < 1)
               {
                  Gaming.uiGroup.alertBox.showError("❌ 请输入有效的等级数字 (1-" + maxLevel + ")");
                  return;
               }

               if(newLevel > maxLevel)
               {
                  Gaming.uiGroup.alertBox.showCheck("等级 " + newLevel + " 超过最大值，已自动调整为 " + maxLevel, "yes");
                  newLevel = maxLevel;
               }

               skillData.save.lv = newLevel;
               Gaming.uiGroup.alertBox.showSuccess("✅ 技能 " + skillDefine.cnName + " 等级已设置为 " + newLevel + " (原等级: " + originalLevel + ")");
            }

            // 强制刷新技能数据
            try
            {
               // 刷新技能背包显示
               if(Gaming.PG.DATA && Gaming.PG.DATA.skillBag)
               {
                  Gaming.PG.DATA.skillBag.fleshData();
               }

               // 刷新装备技能显示
               if(Gaming.PG.DATA && Gaming.PG.DATA.skill)
               {
                  Gaming.PG.DATA.skill.fleshData();
               }

               // 刷新UI显示
               this.fleshData();

               // 刷新主界面
               if(Gaming.uiGroup.mainUI)
               {
                  Gaming.uiGroup.mainUI.fleshBtn();
               }
            }
            catch(refreshError:Error)
            {
               // 刷新失败不影响主要功能
            }

            this.hideEditButton();
         }
         catch(e:Error)
         {
            Gaming.uiGroup.alertBox.showError("❌ 编辑技能失败: " + e.message + "\n堆栈: " + e.getStackTrace());
         }
      }
   }
}


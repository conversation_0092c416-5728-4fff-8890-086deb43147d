package dataAll._app.union.battle
{
   import com.sounto.utils.ArrayMethod;
   import dataAll.level.define.LevelDefine;
   
   public class UnionBattleDefine
   {
      public var mapObj:Object = {};
      
      public var mapArr:Array = [];
      
      public function UnionBattleDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         this.inMapXML(xml0.mapAll[0]);
      }
      
      private function inMapXML(xml0:XML) : void
      {
         var x0:XML = null;
         var d0:UnionBattleMapDefine = null;
         var xl0:XMLList = xml0.map;
         for each(x0 in xl0)
         {
            d0 = new UnionBattleMapDefine();
            d0.inData_byXML(x0);
            this.mapObj[d0.name] = d0;
            this.mapArr.push(d0);
         }
      }
      
      public function getDefine(name0:String) : UnionBattleMapDefine
      {
         return this.mapObj[name0];
      }
      
      public function getRandomMapName() : String
      {
         var d0:UnionBattleMapDefine = ArrayMethod.getRandomOne(this.mapArr);
         return d0.name;
      }
      
      public function traceAllBossCn() : void
      {
         var d0:UnionBattleMapDefine = null;
         var levelD0:LevelDefine = null;
         var boss0:String = null;
         for each(d0 in this.mapObj)
         {
            levelD0 = Gaming.defineGroup.level.getDefine(d0.levelName);
            boss0 = levelD0.unitG.getBossCnStr();
         }
      }
   }
}


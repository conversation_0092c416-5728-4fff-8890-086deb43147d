package com.sounto.oldUtils
{
   public class Positive64
   {
      public static var ran:Number = int(Math.random() * 1000000);
      
      public function Positive64()
      {
         super();
      }
      
      public static function encode(num0:Number) : String
      {
         var xx:int = 0;
         num0 = Math.round(num0);
         if(num0 > 3536094000)
         {
            xx = 0;
         }
         var r0:Number = Math.random() * 3 + 1;
         var r1:Number = Math.ceil(num0 * r0);
         var s0:String = String(r1 - num0);
         var len0:int = s0.length;
         var len1:String = String(len0);
         if(len0 == 1)
         {
         }
         len1 = "0" + len1;
         return s0 + (r1 + ran) + len1;
      }
      
      public static function decode(str0:String) : Number
      {
         var xx:int = 0;
         if(str0 == "")
         {
            return 0;
         }
         var str_len0:int = str0.length;
         var len0:int = int(str0.substr(str_len0 - 2));
         var s0:Number = Number(str0.substr(0,len0));
         var r1:Number = Number(str0.substr(len0,str_len0 - len0 - 2));
         var v0:Number = r1 - s0 - ran;
         if(v0 > 3536094000)
         {
            xx = 0;
         }
         return v0;
      }
   }
}


package dataAll.equip.creator
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll.drop.define.EquipColorDefine;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipDefineGroup;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.define.EquipRangeDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.device.DeviceDataCreator;
   import dataAll.equip.device.DeviceDefine;
   import dataAll.equip.jewelry.JewelryDataCreator;
   import dataAll.equip.jewelry.JewelryDefine;
   import dataAll.equip.save.EquipSave;
   import dataAll.equip.save.FashionSave;
   import dataAll.equip.shield.ShieldDataCreator;
   import dataAll.equip.shield.ShieldDefine;
   import dataAll.equip.vehicle.VehicleDataCreator;
   import dataAll.equip.vehicle.VehicleDefine;
   import dataAll.equip.weapon.WeaponDataCreator;
   import dataAll.equip.weapon.WeaponDefine;
   import dataAll.pro.PropertyArrayDefine;
   
   public class EquipDataCreator
   {
      public var DG:EquipDefineGroup = null;
      
      public var propertyCtreator:EquipPropertyDataCreator = new EquipPropertyDataCreator();
      
      public function EquipDataCreator()
      {
         super();
         EquipUpgradeCtrl.init();
         EquipStrengthenCtrl.init();
      }
      
      public function getFullSaveArray() : Array
      {
         var n:* = undefined;
         var type0:String = null;
         var arr0:Array = [];
         var typeArr0:Array = EquipType.IMG_ARR;
         for(n in typeArr0)
         {
            type0 = typeArr0[n];
            arr0.push(this.getSave("blue",1,type0,"diff_0"));
         }
         return arr0;
      }
      
      public function getSaveByBody(unitType0:String, bodyLv0:int, dropName0:String, setType0:String = "", dropMul0:Number = 0) : EquipSave
      {
         var cd0:EquipColorDefine = Gaming.defineGroup.dropColor.getByName(dropName0).equip;
         var color0:String = cd0.getOneDrop(unitType0,0,dropMul0);
         return this.getSaveByColor(color0,bodyLv0,dropName0,setType0);
      }
      
      public function getSaveByColor(color0:String, bodyLv0:int, dropName0:String, setType0:String = "") : EquipSave
      {
         var cd0:EquipColorDefine = Gaming.defineGroup.dropColor.getByName(dropName0).equip;
         var index0:int = int(cd0.name.indexOf(color0));
         var itemsLvRange0:Array = cd0.itemsLvRange[index0];
         var lv0:int = bodyLv0 + itemsLvRange0[0] + Math.random() * (itemsLvRange0[1] - itemsLvRange0[0] + 1);
         if(lv0 < 1)
         {
            lv0 = 1;
         }
         if(lv0 > cd0.MAX_LEVEL)
         {
            lv0 = cd0.MAX_LEVEL;
         }
         var typeArr0:Array = EquipType.getTypeArrByHeroLv(lv0);
         var type0:String = typeArr0[int(Math.random() * typeArr0.length)];
         if(setType0 != "")
         {
            type0 = setType0;
         }
         return this.getSave(color0,lv0,type0,dropName0);
      }
      
      public function getSave(color0:String, lv0:int, type0:String, dropName0:String) : EquipSave
      {
         var d0:EquipDefine = this.getImageDefine(lv0,type0);
         return this.getSaveByEquipDefine(color0,lv0,d0,dropName0);
      }
      
      public function getSaveByEquipDefine(color0:String, lv0:int, d0:EquipDefine, dropName0:String) : EquipSave
      {
         var s0:EquipSave = null;
         var obj0:Object = this.propertyCtreator.getObj(color0,d0.type,lv0,dropName0);
         if(Boolean(d0))
         {
            s0 = new EquipSave();
            s0.inDataByDefine(d0);
            s0.obj = obj0;
            s0.color = color0;
            s0.itemsLevel = lv0;
            EquipSkillCreator.setSkill(s0);
            return s0;
         }
         return null;
      }
      
      public function getSuperSave(color0:String, lv0:int, imageName0:String, skillB0:Boolean = false) : EquipSave
      {
         var s0:EquipSave = null;
         var d0:EquipDefine = Gaming.defineGroup.equip.getDefine(imageName0);
         var obj0:Object = this.propertyCtreator.getSuperObj(color0,d0.type,color0 == "red" ? lv0 + 1 : lv0,d0.addArmsType);
         if(Boolean(d0))
         {
            s0 = new EquipSave();
            s0.inDataByDefine(d0);
            s0.obj = obj0;
            s0.color = color0;
            s0.itemsLevel = lv0;
            if(skillB0)
            {
               EquipSkillCreator.setSkill(s0);
            }
            return s0;
         }
         INIT.showError("找不到装备图像定义：lv:" + lv0);
         return null;
      }
      
      public function getBlackSaveByLv(lv0:int) : EquipSave
      {
         var f0:EquipFatherDefine = null;
         var rD0:EquipDefine = null;
         var r0:String = null;
         var d0:EquipDefine = null;
         var arr0:Array = Gaming.defineGroup.equip.blackFatherArr;
         var rArr0:Array = [];
         for each(f0 in arr0)
         {
            for each(d0 in f0.partObj)
            {
               rArr0.push(d0);
            }
         }
         rD0 = rArr0[int(Math.random() * rArr0.length)];
         r0 = rD0.name;
         return this.getBlackSave(lv0,r0,true);
      }
      
      public function getBlackSave(lv0:int, imageName0:String, randomB0:Boolean) : EquipSave
      {
         var s0:EquipSave = null;
         var normalProArr0:Array = null;
         var normalObj0:Object = null;
         var blackObj0:Object = null;
         var obj0:Object = null;
         var d0:EquipDefine = Gaming.defineGroup.equip.getDefine(imageName0);
         if(Boolean(d0))
         {
            s0 = new EquipSave();
            s0.inDataByDefine(d0);
            normalProArr0 = this.propertyCtreator.getBlackNormalProArr(d0.type);
            normalObj0 = this.propertyCtreator.getRandomValueObj(normalProArr0,[0,0],lv0 + 1);
            blackObj0 = d0.getOtherObj();
            if(randomB0)
            {
               this.propertyCtreator.randomBlackValue(blackObj0);
            }
            obj0 = ComMethod.fixedObj(normalObj0,blackObj0);
            s0.obj = obj0;
            s0.color = EquipColor.BLACK;
            s0.itemsLevel = lv0;
            if(randomB0)
            {
               EquipSkillCreator.setSkill(s0);
               EquipSkillAddCtreator.addSkillAddData(s0);
            }
            else
            {
               s0.skillArr = d0.skillArr;
            }
            return s0;
         }
         INIT.showErrorMust("找不到装备图像定义：lv:" + lv0);
         return null;
      }
      
      public function getBlackChipObjTip(d0:EquipDefine) : String
      {
         var n:* = undefined;
         var proD0:PropertyArrayDefine = null;
         var v0:Number = NaN;
         var valueStr0:String = null;
         var range0:Array = null;
         var lv0:int = d0.itemsLevel;
         var str0:String = "";
         var normalProArr0:Array = this.propertyCtreator.getBlackNormalProArr(d0.type);
         var normalObj0:Object = this.propertyCtreator.getRandomValueObj(normalProArr0,[0,0],lv0 + 1);
         var blackObj0:Object = d0.getOtherObj();
         var proConstB0:Boolean = d0.getComposeProConstB();
         str0 += EquipPropertyDataCreator.getText_byObj(normalObj0);
         for(n in blackObj0)
         {
            proD0 = Gaming.defineGroup.getPropertyArrayDefine(n);
            v0 = Number(blackObj0[n]);
            valueStr0 = "";
            if(proConstB0)
            {
               valueStr0 = proD0.getValueString(v0);
            }
            else
            {
               range0 = proD0.getBlackValueRange(v0);
               valueStr0 = proD0.getValueString(range0[0]);
               if(proD0.unit.length >= 2)
               {
                  valueStr0 = range0[0];
               }
               valueStr0 += "~" + proD0.getValueString(range0[1]);
            }
            str0 += "<redness2 " + proD0.cnName + "/>|<redness2 " + valueStr0 + "/>\n";
         }
         return str0;
      }
      
      public function getDarkgoldSave(imageName0:String) : EquipSave
      {
         var lv0:int = 0;
         var s0:EquipSave = null;
         var normalProArr0:Array = null;
         var normalObj0:Object = null;
         var blackObj0:Object = null;
         var obj0:Object = null;
         var d0:EquipDefine = Gaming.defineGroup.equip.getDefine(imageName0);
         if(Boolean(d0))
         {
            lv0 = d0.itemsLevel;
            s0 = new EquipSave();
            s0.inDataByDefine(d0);
            normalProArr0 = this.propertyCtreator.getBlackNormalProArr(d0.type);
            normalObj0 = this.propertyCtreator.getRandomValueObj(normalProArr0,[0,0],lv0 + 1);
            blackObj0 = d0.getOtherObj();
            obj0 = ComMethod.fixedObj(normalObj0,blackObj0);
            s0.obj = obj0;
            s0.color = EquipColor.DARKGOLD;
            s0.itemsLevel = lv0;
            s0.lockB = true;
            return s0;
         }
         INIT.showErrorMust("找不到装备图像定义：lv:" + lv0);
         return null;
      }
      
      private function getImageDefine(lv0:int, type0:String) : EquipDefine
      {
         var n:* = undefined;
         var img_d0:EquipDefine = null;
         var d0:EquipRangeDefine = null;
         var per0:Number = NaN;
         var img_arr0:Array = null;
         var arr0:Array = this.DG.rangeArr;
         var r_arr0:Array = [];
         for(n in arr0)
         {
            d0 = arr0[n];
            per0 = this.getRandomPer(lv0,d0);
            if(Math.random() < per0)
            {
               img_arr0 = d0[type0];
               r_arr0 = r_arr0.concat(img_arr0);
            }
         }
         return r_arr0[int(r_arr0.length * Math.random())];
      }
      
      private function getRandomPer(lv0:int, d0:EquipRangeDefine) : Number
      {
         var per0:Number = 0;
         if(!(lv0 < d0.startLevel || lv0 > d0.endLevel))
         {
            if(lv0 >= d0.maxLevel)
            {
               if(d0.endLevel == d0.maxLevel)
               {
                  per0 = 1;
               }
               else
               {
                  per0 = (d0.endLevel - lv0) / (d0.endLevel - d0.maxLevel);
               }
            }
            else if(lv0 >= d0.minLevel)
            {
               per0 = 1;
            }
            else if(d0.startLevel == d0.minLevel)
            {
               per0 = 1;
            }
            else
            {
               per0 = (d0.minLevel - lv0) / (d0.minLevel - d0.startLevel);
            }
         }
         return per0;
      }
      
      public function getFashionSave(name0:String) : FashionSave
      {
         var d0:EquipDefine = Gaming.defineGroup.equip.getDefine(name0);
         if(!d0)
         {
            INIT.showError("找不到定义EquipDefine：" + name0);
         }
         var s0:FashionSave = new FashionSave();
         s0.inDataByDefine(d0);
         return s0;
      }
      
      public function getTempDataByName(name0:String) : EquipData
      {
         var da0:EquipData = null;
         var s0:EquipSave = null;
         var d0:EquipDefine = Gaming.defineGroup.getAllEquipDefine(name0);
         if(Boolean(d0))
         {
            da0 = new EquipData();
            s0 = null;
            if(d0.type == EquipType.FASHION)
            {
               s0 = this.getFashionSave(d0.name);
               da0.inData_bySave(s0,Gaming.PG.da);
            }
            else if(d0.type == EquipType.DEVICE)
            {
               da0 = DeviceDataCreator.getTempData(d0 as DeviceDefine,Gaming.PG.da);
            }
            else if(d0.type == EquipType.WEAPON)
            {
               da0 = WeaponDataCreator.getTempData(d0 as WeaponDefine,Gaming.PG.da);
            }
            else if(d0.type == EquipType.JEWELRY)
            {
               da0 = JewelryDataCreator.getTempData(d0 as JewelryDefine,Gaming.PG.da);
            }
            else if(d0.type == EquipType.SHIELD)
            {
               da0 = ShieldDataCreator.getTempData(d0 as ShieldDefine,Gaming.PG.da);
            }
            else if(d0.type == EquipType.VEHICLE)
            {
               da0 = VehicleDataCreator.getTempData(d0 as VehicleDefine,Gaming.PG.da);
            }
            else
            {
               da0 = null;
            }
            return da0;
         }
         return null;
      }
   }
}


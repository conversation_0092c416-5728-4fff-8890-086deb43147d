package dataAll._app.head
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.head.define.HeadDefine;
   
   public class HeadSave
   {
      public static var pro_arr:Array = [];
      
      public var nowHead:String = "";
      
      public var obj:Object = {};
      
      public var nowIndex:int = 0;
      
      public function HeadSave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.obj = ClassProperty.copySaveObj(obj0["obj"],HeadOneSave);
      }
      
      public function addHead(name0:String, timeStr0:String) : void
      {
         var s0:HeadOneSave = null;
         s0 = this.obj[name0];
         if(s0 is HeadOneSave)
         {
            s0.inData(name0,timeStr0,this.nowIndex);
            ++this.nowIndex;
         }
         else
         {
            s0 = new HeadOneSave();
            s0.inData(name0,timeStr0,this.nowIndex);
            this.obj[name0] = s0;
            ++this.nowIndex;
         }
      }
      
      public function getHead(name0:String) : HeadOneSave
      {
         return this.obj[name0];
      }
      
      public function useHead(name0:String) : void
      {
         this.nowHead = name0;
      }
      
      public function panHaveHead(name0:String) : Boolean
      {
         return this.obj.hasOwnProperty(name0);
      }
      
      public function getNowHeadDefine() : HeadDefine
      {
         if(this.nowHead == "")
         {
            return null;
         }
         return Gaming.defineGroup.head.getDefine(this.nowHead);
      }
      
      public function getOneSaveSortArr() : Array
      {
         var nowS0:HeadOneSave = null;
         var s0:HeadOneSave = null;
         var arr0:Array = ComMethod.objToArr(this.obj);
         var arr2:Array = [];
         arr0.sort(this.sortByIndex);
         if(this.nowHead != "")
         {
            for each(s0 in arr0)
            {
               if(s0.name != this.nowHead)
               {
                  arr2.push(s0);
               }
               else
               {
                  nowS0 = s0;
               }
            }
            if(Boolean(nowS0))
            {
               arr2.unshift(nowS0);
            }
         }
         else
         {
            arr2 = arr0;
         }
         return arr2;
      }
      
      private function getMaxIndex() : int
      {
         var s0:HeadOneSave = null;
         var max0:int = 0;
         for each(s0 in this.obj)
         {
            if(s0.index > max0)
            {
               max0 = s0.index;
            }
         }
         return max0;
      }
      
      public function topHead(name0:String) : Boolean
      {
         var max0:int = 0;
         var s0:HeadOneSave = this.getHead(name0);
         if(Boolean(s0))
         {
            max0 = this.getMaxIndex();
            if(s0.index < max0)
            {
               if(max0 < 1000)
               {
                  max0 = 1000;
               }
               s0.index = max0 + 1;
               return true;
            }
         }
         return false;
      }
      
      private function sortByIndex(a0:HeadOneSave, b0:HeadOneSave) : int
      {
         if(a0.index > b0.index)
         {
            return -1;
         }
         if(a0.index < b0.index)
         {
            return 1;
         }
         return 0;
      }
      
      public function repair6_1() : void
      {
         var n:* = undefined;
         var obj0:Object = {};
         for(n in this.obj)
         {
            if(n != "almighty_10")
            {
               obj0[n] = this.obj[n];
            }
         }
         this.obj = obj0;
         if(this.nowHead == "almighty_10")
         {
            this.nowHead = "";
         }
      }
   }
}


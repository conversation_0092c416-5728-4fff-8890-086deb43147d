package dataAll.scene
{
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._base.NormalDefine;
   
   public class WeatherDefine extends NormalDefine
   {
      public static const RAIN:String = "rain";
      
      public static const HEAT:String = "heat";
      
      public static const SNOW:String = "snow";
      
      public static const SAND:String = "sand";
      
      public static const THUNDER:String = "thunder";
      
      private static const OBJ:Object = {};
      
      private static const ARR:Array = [];
      
      private static const normalNoMapLabelArr:Array = ["室内","地下"];
      
      public var cnMulArr:Array = null;
      
      public var shootLen:Number = 0;
      
      public var shootSpeed:Number = 0;
      
      public var bulletGravity:Number = 0;
      
      public var weaponAnger:Number = 0;
      
      public var addObj:Object = null;
      
      public var downObj:Object = null;
      
      public var ff:Number = 0;
      
      public var noMapLabelArr:Array = null;
      
      public var mainEffect:String = "";
      
      public var hitEffect:String = "";
      
      public var startSoundUrl:String = "";
      
      public var soundUrl:String = "";
      
      public function WeatherDefine()
      {
         super();
      }
      
      public static function staticInit() : void
      {
         var w0:WeatherDefine = new WeatherDefine();
         w0.name = RAIN;
         w0.cnName = "雨";
         w0.cnMulArr = ["小雨","中雨","大雨"];
         w0.ff = -0.3;
         w0.bulletGravity = 0.2;
         w0.shootLen = -0.15;
         w0.shootSpeed = 0.2;
         w0.weaponAnger = -0.4;
         w0.addObj = {
            "electric":0.2,
            "water":0.2
         };
         w0.downObj = {
            "fire":-0.3,
            "particle":-0.3,
            "poison":-0.2
         };
         w0.mainEffect = "rainSputtering";
         w0.hitEffect = "rainHit";
         w0.startSoundUrl = "sound/thunder";
         w0.soundUrl = "sound/rain";
         w0.noMapLabelArr = normalNoMapLabelArr;
         addDefine(w0);
         w0 = new WeatherDefine();
         w0.name = HEAT;
         w0.cnName = "高温";
         w0.cnMulArr = ["高温","炎热","酷热"];
         w0.shootSpeed = -0.1;
         w0.weaponAnger = 0.3;
         w0.addObj = {
            "fire":0.4,
            "boom":0.3,
            "hit":0.2,
            "particle":0.1
         };
         w0.downObj = {"water":-0.3};
         w0.mainEffect = "heatSputtering";
         w0.noMapLabelArr = ["冰雪"];
         addDefine(w0);
      }
      
      private static function addDefine(d0:WeatherDefine) : void
      {
         ARR.push(d0);
         OBJ[d0.name] = d0;
      }
      
      public static function getDefine(name0:String) : WeatherDefine
      {
         return OBJ[name0];
      }
      
      public static function getAllEffectArr() : Array
      {
         var d0:WeatherDefine = null;
         var arr0:Array = [];
         for each(d0 in OBJ)
         {
            ArrayMethod.addNoRepeatArrInArr(arr0,d0.getEffectArr());
         }
         return arr0;
      }
      
      public static function getRandomByMap(d0:WorldMapDefine) : String
      {
         var wd0:WeatherDefine = null;
         var pro0:Number = NaN;
         for each(wd0 in ARR)
         {
            pro0 = getOneMapPro(wd0,d0);
            if(Math.random() <= pro0)
            {
               return wd0.name;
            }
         }
         return "";
      }
      
      private static function getOneMapPro(wd0:WeatherDefine, d0:WorldMapDefine) : Number
      {
         if(ArrayMethod.onlyOneElementSamePan(d0.labelArr,wd0.noMapLabelArr))
         {
            return 0;
         }
         if(d0.isSpaceB())
         {
            return 0;
         }
         return WeatherDefine[wd0.name + "_getMapPro"](d0);
      }
      
      private static function rain_getMapPro(d0:WorldMapDefine) : Number
      {
         var v0:Number = 1;
         if(d0.labelArr.indexOf("沙漠") >= 0)
         {
            v0 = 0.1;
         }
         else if(d0.isGreenIsB())
         {
            v0 = 2.5;
         }
         else if(d0.geogType == "hill")
         {
            v0 = 1.5;
         }
         else if(d0.geogType == "lab")
         {
            v0 = 0.5;
         }
         return v0 * 0.005;
      }
      
      private static function heat_getMapPro(d0:WorldMapDefine) : Number
      {
         var v0:Number = 1;
         if(d0.labelArr.indexOf("沙漠") >= 0)
         {
            v0 = 2.5;
         }
         else if(d0.geogType == "sea")
         {
            v0 = 0.5;
         }
         return v0 * 0.005;
      }
      
      public static function getMulLv(mul0:Number) : int
      {
         if(mul0 < 1.2)
         {
            return 1;
         }
         if(mul0 < 2.4)
         {
            return 2;
         }
         return 3;
      }
      
      public static function getKindMulInObj(kind0:String, obj0:Object) : Number
      {
         if(Boolean(obj0))
         {
            if(obj0.hasOwnProperty(kind0))
            {
               return obj0[kind0];
            }
         }
         return 0;
      }
      
      public function getCnByMul(mul0:Number) : String
      {
         var lv0:int = 0;
         if(Boolean(this.cnMulArr))
         {
            lv0 = getMulLv(mul0);
            return this.cnMulArr[lv0 - 1];
         }
         return cnName;
      }
      
      public function getFrameLabelByMul(mul0:Number) : String
      {
         var lv0:int = getMulLv(mul0);
         return name + lv0;
      }
      
      public function getEffectArr() : Array
      {
         var arr0:Array = [];
         if(this.mainEffect != "")
         {
            arr0.push(this.mainEffect);
         }
         if(this.hitEffect != "")
         {
            arr0.push(this.hitEffect);
         }
         return arr0;
      }
      
      public function getKindAdd(kind0:String) : Number
      {
         var v1:Number = getKindMulInObj(kind0,this.addObj);
         var v2:Number = getKindMulInObj(kind0,this.downObj);
         return v1 + v2;
      }
   }
}


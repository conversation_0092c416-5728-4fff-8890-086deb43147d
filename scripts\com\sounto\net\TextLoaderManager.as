package com.sounto.net
{
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.IOErrorEvent;
   import flash.events.ProgressEvent;
   
   public class TextLoaderManager extends EventDispatcher
   {
      public var loadState:String = "no";
      
      private var waiting_arr:Array = new Array();
      
      private var nowLoader:TextLoader = null;
      
      private var loader_arr:Array = new Array();
      
      private var perNum:Number = 0;
      
      public function TextLoaderManager()
      {
         super();
      }
      
      public function SWFLoaderManager() : *
      {
      }
      
      public function addTextLoader(url0:String, label0:String) : *
      {
         var sl:TextLoader = new TextLoader();
         sl.url = url0;
         sl.label = label0;
         this.waiting_arr.push(sl);
      }
      
      public function startLoad() : void
      {
         if(this.waiting_arr.length > 0)
         {
            this.loadState = "ing";
            this.nowLoader = this.waiting_arr[0];
            this.nowLoader.loadMe();
            this.nowLoader.addEventListener(Event.COMPLETE,this.loadCompleteHandler);
            this.nowLoader.addEventListener(ProgressEvent.PROGRESS,this.loadProcessHandler);
            this.nowLoader.addEventListener(IOErrorEvent.IO_ERROR,this.loadErrorHandler);
         }
         else
         {
            this.loadState = "no";
         }
      }
      
      public function stopLoad() : void
      {
         if(this.nowLoader != null)
         {
            this.nowLoader.close();
            this.nowLoader.removeEventListener(Event.COMPLETE,this.loadCompleteHandler);
            this.nowLoader.removeEventListener(ProgressEvent.PROGRESS,this.loadProcessHandler);
            this.nowLoader.removeEventListener(IOErrorEvent.IO_ERROR,this.loadErrorHandler);
            this.waiting_arr.length = 0;
         }
         this.loadState = "no";
      }
      
      public function getResource(name_label:String) : *
      {
         var sl:TextLoader = null;
         var n:* = undefined;
         var sl0:TextLoader = null;
         for(n in this.loader_arr)
         {
            sl0 = this.loader_arr[n];
            if(sl0.label == name_label)
            {
               sl = sl0;
               break;
            }
         }
         return sl;
      }
      
      public function getEnemyXML(label0:String) : XML
      {
         var n:* = undefined;
         var xml0:XML = XML(this.getResource("enemy").data);
         var xmlL:* = xml0.enemy;
         for(n in xmlL)
         {
            if(xmlL[n].@id == label0)
            {
               return xmlL[n];
            }
         }
         return null;
      }
      
      private function loadCompleteHandler(event:Event) : *
      {
         var completeEvent:Event = null;
         this.nowLoader.removeEventListener(Event.COMPLETE,this.loadCompleteHandler);
         this.nowLoader.removeEventListener(ProgressEvent.PROGRESS,this.loadProcessHandler);
         this.nowLoader.removeEventListener(IOErrorEvent.IO_ERROR,this.loadErrorHandler);
         this.waiting_arr.shift();
         this.loader_arr.unshift(this.nowLoader);
         this.nowLoader = null;
         if(this.waiting_arr.length > 0)
         {
            this.startLoad();
         }
         else
         {
            completeEvent = new Event(Event.COMPLETE);
            this.perNum = 1;
            this.loadState = "no";
            dispatchEvent(completeEvent);
         }
      }
      
      private function loadProcessHandler(event:ProgressEvent) : *
      {
         var wnum:int = int(this.waiting_arr.length);
         var lnum:int = this.loader_arr.length + 1;
         this.perNum = 1 / (wnum + lnum) * (event.bytesLoaded / event.bytesTotal + lnum - 1);
      }
      
      private function loadErrorHandler(event:IOErrorEvent) : *
      {
      }
      
      public function getLoadingText() : String
      {
         return "正在加载文本";
      }
      
      public function getLoadingPer() : Number
      {
         return this.perNum;
      }
   }
}


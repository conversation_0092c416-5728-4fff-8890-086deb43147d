package com.sounto.pool
{
   public class Pool
   {
      private var obj:Object = {};
      
      private var numObj:Object = {};
      
      public var maxNum:int = 20;
      
      public function Pool()
      {
         super();
      }
      
      public function setNum(father0:String, name0:String, num0:int) : void
      {
         if(!this.obj.hasOwnProperty(father0))
         {
            this.obj[father0] = {};
         }
         var child0:Object = this.obj[father0];
         if(!child0.hasOwnProperty(name0))
         {
            child0[name0] = 0;
         }
         child0[name0] = num0;
      }
      
      private function getNum(father0:String, name0:String) : int
      {
         var child0:Object = null;
         if(this.obj.hasOwnProperty(father0))
         {
            child0 = this.obj[father0];
            if(child0.hasOwnProperty(name0))
            {
               return child0[name0];
            }
         }
         return this.maxNum;
      }
      
      public function addObject(b0:Object, father0:String, name0:String) : Boolean
      {
         var child0:Object = null;
         var arr0:Array = null;
         var max0:int = this.getNum(father0,name0);
         if(max0 <= 0)
         {
            return false;
         }
         if(!this.obj.hasOwnProperty(father0))
         {
            this.obj[father0] = {};
         }
         child0 = this.obj[father0];
         if(!child0.hasOwnProperty(name0))
         {
            child0[name0] = [];
         }
         arr0 = child0[name0];
         if(arr0.length < max0)
         {
            arr0.push(b0);
            return true;
         }
         return false;
      }
      
      public function getObject(father0:String, name0:String, delB0:Boolean) : Object
      {
         var child0:Object = null;
         var arr0:Array = null;
         var len0:int = 0;
         var b0:Object = null;
         if(this.obj.hasOwnProperty(father0))
         {
            child0 = this.obj[father0];
            if(child0.hasOwnProperty(name0))
            {
               arr0 = child0[name0];
               len0 = int(arr0.length);
               if(len0 == 0)
               {
                  return null;
               }
               b0 = arr0[len0 - 1];
               if(delB0)
               {
                  arr0.pop();
               }
               return b0;
            }
         }
         return null;
      }
   }
}


package dataAll.equip.jewelry
{
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.EquipData;
   import dataAll.equip.save.EquipSave;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.must.define.MustDefine;
   
   public class JewelryData extends EquipData
   {
      public var jewelryDefine:JewelryDefine = null;
      
      public function JewelryData()
      {
         super();
      }
      
      public function getJewelrySave() : JewelrySave
      {
         return save as JewelrySave;
      }
      
      override public function inData_bySave(s0:EquipSave, pd0:NormalPlayerData, dg0:ItemsDataGroup = null) : void
      {
         super.inData_bySave(s0,pd0,dg0);
         this.jewelryDefine = this.getJewelrySave().getJewelryDefine();
         save.obj = this.jewelryDefine.getAddObj();
      }
      
      override public function clone() : EquipData
      {
         var da0:JewelryData = super.clone() as JewelryData;
         da0.jewelryDefine = this.jewelryDefine;
         return da0;
      }
      
      override public function isCanNumSwapB() : Boolean
      {
         return true;
      }
      
      override public function isCanOverlayB() : Boolean
      {
         return true;
      }
      
      override public function getNowNum() : int
      {
         return save.nowNum;
      }
      
      override public function setNowNum(num0:int) : void
      {
         save.nowNum = num0;
      }
      
      override public function addNowNum(num0:int, otherDa0:IO_ItemsData = null) : void
      {
         addNowTrueNum(num0,otherDa0);
      }
      
      override public function getSellPrice() : Number
      {
         return save.nowNum * 2000 * save.itemsLevel;
      }
      
      override public function canResolveB() : Boolean
      {
         if(this.jewelryDefine.baseLabel == "longGlasses")
         {
            return false;
         }
         return super.canResolveB();
      }
      
      public function getUpradeData() : JewelryData
      {
         var upgradeName0:String = this.jewelryDefine.getUpgradeName();
         var s0:JewelrySave = JewelryDataCreator.getSave(upgradeName0,1);
         var da0:JewelryData = new JewelryData();
         da0.inData_bySave(s0,normalPlayerData);
         return da0;
      }
      
      public function getUpradeMust() : MustDefine
      {
         return JewelryDataCreator.getUpgradeMust(this.jewelryDefine,this.jewelryDefine.lv);
      }
      
      public function changeToOneData(da0:JewelryData) : void
      {
         var name0:* = null;
         var proArr0:Array = ["skillArr","imgName","nowNum","itemsLevel","name","cnName","newB","lockB"];
         for each(name0 in proArr0)
         {
            save[name0] = da0.save[name0];
         }
         this.jewelryDefine = this.getJewelrySave().getJewelryDefine();
         save.obj = this.jewelryDefine.getAddObj();
      }
      
      override public function getGatherTip(compareDa0:EquipData = null, tipType0:String = "", showSkillB:Boolean = true) : String
      {
         return this.jewelryDefine.getGatherTip();
      }
   }
}


package dataAll._app.city.dress
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ObjectMethod;
   import dataAll._app.city.define.CityBodyDefine;
   import dataAll._app.city.define.CityBodyType;
   import dataAll._player.PlayerData;
   import dataAll.must.define.MustDefine;
   
   public class CityDressDataGroup
   {
      public var playerData:PlayerData;
      
      private var save:CityDressSaveGroup;
      
      private var arr:Array = null;
      
      private var mouldObj:Object = {};
      
      public function CityDressDataGroup()
      {
         super();
      }
      
      public function inData_bySave(s0:CityDressSaveGroup) : void
      {
         this.save = s0;
      }
      
      public function openUI() : void
      {
         this.fleshMould();
         this.fleshDataArr();
      }
      
      private function fleshDataArr() : void
      {
         if(Boolean(this.arr))
         {
            this.fleshDataByDataArr();
         }
         else
         {
            this.fleshDataBySaveArr();
         }
      }
      
      private function fleshDataBySaveArr() : void
      {
         var s0:CityDressSave = null;
         var m0:CityDressMould = null;
         var da0:CityDressData = null;
         var newArr0:Array = [];
         var saveArr0:Array = [];
         for each(s0 in this.save.arr)
         {
            m0 = this.getMould(s0.type,s0.id);
            if(Boolean(m0))
            {
               da0 = new CityDressData();
               da0.inData(m0,s0);
               newArr0.push(da0);
               saveArr0.push(s0);
            }
         }
         this.arr = newArr0;
         this.save.arr = saveArr0;
      }
      
      private function fleshDataByDataArr() : void
      {
         var da0:CityDressData = null;
         var mm0:CityDressMould = null;
         var m0:CityDressMould = null;
         var newArr0:Array = [];
         var saveArr0:Array = [];
         for each(da0 in this.arr)
         {
            mm0 = da0.getMould();
            m0 = this.getMould(mm0.getDressType(),mm0.getId());
            if(Boolean(m0))
            {
               newArr0.push(da0);
               saveArr0.push(da0.getSave());
            }
         }
         this.arr = newArr0;
         this.save.arr = saveArr0;
      }
      
      public function addDataBy(mould0:CityDressMould, x0:int, y0:int) : CityDressData
      {
         var s0:CityDressSave = new CityDressSave();
         s0.type = mould0.getDressType();
         s0.id = mould0.getId();
         s0.saveId = this.save.getNewId();
         s0.x = x0;
         s0.y = y0;
         var da0:CityDressData = new CityDressData();
         da0.inData(mould0,s0);
         this.arr.push(da0);
         return da0;
      }
      
      public function removeData(da0:CityDressData) : void
      {
         var f0:int = ArrayMethod.remove(this.arr,da0);
         if(f0 >= 0)
         {
            this.fleshSaveGroup();
         }
      }
      
      private function fleshSaveGroup() : void
      {
         var saveArr0:Array = null;
         var da0:CityDressData = null;
         if(Boolean(this.arr))
         {
            saveArr0 = [];
            for each(da0 in this.arr)
            {
               saveArr0.push(da0.getSave());
            }
            this.save.arr = saveArr0;
         }
         else
         {
            this.save.arr.length = 0;
         }
      }
      
      public function getDataArr() : Array
      {
         return this.arr;
      }
      
      public function clearAllData() : void
      {
         if(Boolean(this.arr))
         {
            this.arr.length = 0;
         }
         this.fleshSaveGroup();
      }
      
      public function getMaxSpace(type0:String) : int
      {
         var v0:int = this.save.space.getAttribute(type0);
         if(type0 == CityDressType.decora)
         {
            v0 += 10;
         }
         else
         {
            v0 += 1;
         }
         return v0;
      }
      
      public function addMaxSpace(type0:String) : void
      {
         this.save.space.addNum(type0,1);
      }
      
      public function getUseSpace(type0:String) : int
      {
         var da0:CityDressData = null;
         var num0:int = 0;
         for each(da0 in this.arr)
         {
            if(da0.getMould().getDressType() == type0)
            {
               num0 += da0.getUseSpace();
            }
         }
         return num0;
      }
      
      public function getLimitSpace(type0:String) : int
      {
         if(type0 == CityDressType.decora)
         {
            return 300;
         }
         return 20;
      }
      
      public function getAddSpaceMust(type0:String) : MustDefine
      {
         var nowMax0:int = this.getMaxSpace(type0);
         if(type0 == CityDressType.decora)
         {
            return this.getDecoraAddSpaceMust(type0,nowMax0);
         }
         return this.getUnitAddSpaceMust(type0,nowMax0);
      }
      
      private function getUnitAddSpaceMust(type0:String, now0:int) : MustDefine
      {
         var m0:MustDefine = new MustDefine();
         var normal0:int = Gaming.defineGroup.dataList.getValue("dressStampMust",now0 + 1);
         var gold0:int = Gaming.defineGroup.dataList.getValue("dressStampGoldMust_unit",now0 + 1);
         var thingArr0:Array = [];
         thingArr0.push("dressStamp;" + normal0);
         if(gold0 > 0)
         {
            thingArr0.push("dressStampGold;" + gold0);
         }
         m0.inThingsDataByArr(thingArr0);
         return m0;
      }
      
      private function getDecoraAddSpaceMust(type0:String, now0:int) : MustDefine
      {
         var m0:MustDefine = new MustDefine();
         var normal0:int = Gaming.defineGroup.dataList.getValue("dressStampHighMust",now0 + 1);
         var gold0:int = Gaming.defineGroup.dataList.getValue("dressStampGoldMust_decora",now0 + 1);
         var thingArr0:Array = [];
         thingArr0.push("dressStampHigh;" + normal0);
         if(gold0 > 0)
         {
            thingArr0.push("dressStampGold;" + gold0);
         }
         m0.inThingsDataByArr(thingArr0);
         return m0;
      }
      
      public function fleshMould() : void
      {
         var pd0:PlayerData = this.playerData;
         this.addDecoraMould(Gaming.defineGroup.cityBody.getArrByType(CityBodyType.decora));
         this.fleshMouldByMakerArr(pd0.pet.arr,CityDressType.pet);
         this.fleshMouldByMakerArr(pd0.getAllUseVehicleDataArr(),CityDressType.vehicle);
         this.fleshMouldByMakerArr(pd0.getPlayerDataArr(),CityDressType.player);
      }
      
      public function getMould(type0:String, id0:String) : CityDressMould
      {
         if(Boolean(this.mouldObj[type0]))
         {
            return this.mouldObj[type0][id0];
         }
         return null;
      }
      
      private function addDecoraMould(darr0:Array) : void
      {
         var newObj0:Object = null;
         var index0:int = 0;
         var d0:CityBodyDefine = null;
         var mould0:CityDressMould = null;
         var type0:String = this.mouldObj[CityDressType.decora];
         if(!this.mouldObj[type0])
         {
            newObj0 = {};
            index0 = 0;
            for each(d0 in darr0)
            {
               mould0 = new CityDressMould();
               mould0.inDataByDefine(d0,index0);
               newObj0[mould0.getId()] = mould0;
               index0++;
            }
            this.mouldObj[CityDressType.decora] = newObj0;
         }
      }
      
      private function fleshMouldByMakerArr(arr0:Array, type0:String) : void
      {
         var maker0:IO_CityDressMouldMaker = null;
         var id0:String = null;
         var mould0:CityDressMould = null;
         var baseObj0:Object = this.mouldObj[type0];
         if(!baseObj0)
         {
            baseObj0 = {};
         }
         var newObj0:Object = {};
         var index0:int = 0;
         for each(maker0 in arr0)
         {
            id0 = maker0.getId();
            mould0 = baseObj0[id0];
            if(!mould0)
            {
               mould0 = new CityDressMould();
               mould0.inDataByMaker(maker0,index0);
            }
            else
            {
               mould0.uiIndex = index0 + 1;
            }
            newObj0[id0] = mould0;
            index0++;
         }
         this.mouldObj[type0] = newObj0;
      }
      
      public function getMouldArr(type0:String) : Array
      {
         var arr0:Array = null;
         var obj0:Object = this.mouldObj[type0];
         if(Boolean(obj0))
         {
            arr0 = ObjectMethod.toArr(obj0);
            ArrayMethod.sortArrByProName(arr0,"uiIndex");
            return arr0;
         }
         return [];
      }
      
      public function fleshAddNum() : void
      {
         var typeObj0:Object = null;
         var da0:CityDressData = null;
         var mould0:CityDressMould = null;
         for each(typeObj0 in this.mouldObj)
         {
            for each(mould0 in typeObj0)
            {
               mould0.clearAddNum();
            }
         }
         for each(da0 in this.arr)
         {
            da0.fleshMouldAddNum();
         }
      }
   }
}


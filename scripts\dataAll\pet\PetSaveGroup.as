package dataAll.pet
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.pet.dispatch.PetDispatchSave;
   import dataAll.pet.map.PetMapSaveGroup;
   
   public class PetSaveGroup
   {
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var idIndex:int = 0;
      
      public var arr:Array = [];
      
      public var map:PetMapSaveGroup = new PetMapSaveGroup();
      
      public var dispatch:PetDispatchSave = new PetDispatchSave();
      
      public function PetSaveGroup()
      {
         super();
         this.lockLen = 5;
      }
      
      public function get lockLen() : Number
      {
         return this.CF.getAttribute("lockLen");
      }
      
      public function set lockLen(v0:Number) : void
      {
         this.CF.setAttribute("lockLen",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.arr = ClassProperty.copySaveArray(obj0["arr"],PetSave);
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.dispatch.newDayCtrl(timeStr0);
      }
      
      public function getIdIndexAdd() : int
      {
         ++this.idIndex;
         if(this.idIndex > 1000000)
         {
            this.idIndex = 0;
         }
         return this.idIndex;
      }
   }
}


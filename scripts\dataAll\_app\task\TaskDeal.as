package dataAll._app.task
{
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.task.define.TaskFatherDefine;
   import dataAll._app.task.define.TaskType;
   import dataAll._app.task.save.TaskSave;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll._player.PlayerData;
   import dataAll._player.base.PlayerBaseData;
   import dataAll.arms.ArmsData;
   import dataAll.level.define.LevelDefine;
   
   public class TaskDeal
   {
      private static var randomLvArr:Array = [0,-1,-2,-3];
      
      private static var tempRandomLvArr:Array = randomLvArr.concat([]);
      
      private static var randomLvFatherArr:Array = [TaskType.DAY,TaskType.EXTRA];
      
      private static var excludeMapArr:Array = [];
      
      public function TaskDeal()
      {
         super();
      }
      
      public static function dealOne(da0:TaskData) : Boolean
      {
         var d0:TaskDefine = da0.def;
         if(TaskType.dayTypeArr.indexOf(d0.father) >= 0)
         {
            return dealDayTaskData(da0);
         }
         if(d0.father == TaskType.DEPUTY)
         {
            return dealDeputyTaskData(da0);
         }
         return true;
      }
      
      private static function dealDeputyTaskData(da0:TaskData) : Boolean
      {
         var heroLv0:int = 0;
         var taskLv0:int = 0;
         if(da0.save.name == "NanTang_XiaoYing")
         {
            heroLv0 = da0.playerData.level;
            taskLv0 = da0.def.lv;
            da0.save.lv = taskLv0 + int((heroLv0 - taskLv0) / 1.3);
         }
         return true;
      }
      
      private static function getMaxTaskLv() : int
      {
         return 95;
      }
      
      public static function addExcludeMap(mapName0:String) : void
      {
         if(!excludeMapArr)
         {
            excludeMapArr = [];
         }
         ArrayMethod.addNoRepeatInArr(excludeMapArr,mapName0);
      }
      
      public static function dealDayTaskData(da0:TaskData) : Boolean
      {
         var xx0:int = 0;
         var mapSave_arr0:Array = null;
         var map_s0:WorldMapSave = null;
         var worldMap_d0:WorldMapDefine = null;
         var s0:TaskSave = da0.save;
         var d0:TaskDefine = da0.def;
         var fatherD0:TaskFatherDefine = Gaming.defineGroup.task.getFatherDefine(d0.father);
         if(fatherD0.name == "active")
         {
            xx0 = 0;
         }
         if(!d0.tempLvB)
         {
            s0.lv = countTaskLv(da0);
         }
         var normaLevelId0:String = d0.levelId;
         if(d0.worldMapType == "random")
         {
            mapSave_arr0 = da0.playerData.worldMap.getWinArrExcludeTaskByType(d0.father,false,excludeMapArr);
            if(mapSave_arr0.length == 0)
            {
               return false;
            }
            map_s0 = mapSave_arr0[int(Math.random() * mapSave_arr0.length)];
            s0.map = map_s0.name;
            normaLevelId0 = map_s0.getLevelName();
         }
         else if(d0.worldMapType == "range")
         {
            map_s0 = da0.playerData.worldMap.getRandomMapExcludeTaskByType(d0.worldMapId.split(","),d0.father);
            if(!map_s0)
            {
               return false;
            }
            s0.map = map_s0.name;
            normaLevelId0 = map_s0.getLevelName();
         }
         else if(d0.worldMapType == "bossTask22")
         {
            normaLevelId0 = map_bossTask22(da0);
         }
         else if(d0.worldMapType == "armsEdit23")
         {
            normaLevelId0 = map_armsEdit23(da0);
         }
         else if(d0.worldMapType == "" && d0.levelId == "" && d0.worldMapId != "")
         {
            s0.map = d0.worldMapId;
            worldMap_d0 = d0.getWorldMapDefine();
            normaLevelId0 = worldMap_d0.getLevelName();
         }
         if(d0.fixedLevelUrl != "")
         {
            s0.lev = normaLevelId0;
         }
         excludeMapArr = null;
         return true;
      }
      
      private static function countTaskLv(da0:TaskData) : int
      {
         var heroLv0:int = 0;
         var armsDa0:ArmsData = null;
         var armsLv0:int = 0;
         var d0:TaskDefine = da0.def;
         var taskLv0:int = d0.lv;
         var fatherD0:TaskFatherDefine = Gaming.defineGroup.task.getFatherDefine(d0.father);
         if(taskLv0 == 0)
         {
            heroLv0 = da0.playerData.level;
            armsDa0 = da0.playerData.arms.getNowData_MaxDpsByType("");
            armsLv0 = armsDa0.getCountTaskLv();
            if(armsLv0 < heroLv0 - 10)
            {
               armsLv0 = heroLv0 - 10;
            }
            taskLv0 = armsLv0;
            if(taskLv0 < d0.unlockLv)
            {
               taskLv0 = d0.unlockLv;
            }
            if(taskLv0 > heroLv0)
            {
               taskLv0 = heroLv0;
            }
         }
         if(taskLv0 > PlayerBaseData.ENEMY_LEVEL)
         {
            taskLv0 = PlayerBaseData.ENEMY_LEVEL;
         }
         if(fatherD0.maxLvLimitB && d0.maxLvLimitB)
         {
            if(taskLv0 > getMaxTaskLv())
            {
               taskLv0 = getMaxTaskLv();
            }
         }
         return taskLv0;
      }
      
      public static function getRandomWorldMapSave() : WorldMapSave
      {
         var mapSave_arr0:Array = Gaming.PG.da.worldMap.getWinArrExcludeTaskByType("",true);
         if(mapSave_arr0.length == 0)
         {
            return null;
         }
         return mapSave_arr0[int(Math.random() * mapSave_arr0.length)];
      }
      
      private static function getRandomLv() : int
      {
         if(tempRandomLvArr.length == 0)
         {
            tempRandomLvArr = randomLvArr.concat([]);
         }
         var index0:int = Math.random() * tempRandomLvArr.length;
         var c_lv0:int = int(tempRandomLvArr[index0]);
         tempRandomLvArr.splice(index0,1);
         return c_lv0;
      }
      
      private static function map_save(mapD0:WorldMapDefine, da0:TaskData) : String
      {
         var map0:String = mapD0.name;
         var mapS0:WorldMapSave = da0.playerData.worldMap.saveGroup.getSave(map0);
         if(mapS0 == null)
         {
            map0 = "WoTu";
            mapS0 = da0.playerData.worldMap.saveGroup.getSave(map0);
         }
         da0.save.map = map0;
         var levelD0:LevelDefine = mapS0.getLevelDefine();
         if(levelD0.fixed.target != "")
         {
            return levelD0.fixed.target;
         }
         return levelD0.name;
      }
      
      private static function map_bossTask22(da0:TaskData) : String
      {
         var mapArr0:Array = ["ShuiSheng","BeiDou","BaoLun","DiXia","BaiSha","PrisonFirst","LvSen","FangZhouSecond"];
         var pd0:PlayerData = da0.playerData;
         var time0:StringDate = pd0.time.getReadTimeDate();
         var index0:int = time0.date;
         if(index0 > 15)
         {
            index0 = 0;
         }
         var map0:String = ArrayMethod.getElementLimit(mapArr0,index0) as String;
         var mapS0:WorldMapSave = pd0.worldMap.saveGroup.getSave(map0);
         if(mapS0 == null)
         {
            map0 = "WoTu";
            mapS0 = pd0.worldMap.saveGroup.getSave(map0);
         }
         da0.save.map = map0;
         var levelD0:LevelDefine = mapS0.getLevelDefine();
         if(levelD0.fixed.target != "")
         {
            return levelD0.fixed.target;
         }
         return levelD0.name;
      }
      
      private static function map_armsEdit23(da0:TaskData) : String
      {
         var readTime0:StringDate = da0.playerData.time.getReadTimeDate();
         var c0:int = readTime0.reductionOneStr("2023-6-26");
         var mapArr0:Array = Gaming.defineGroup.worldMap.getShowMapArr();
         var mapD0:WorldMapDefine = ArrayMethod.getElementLimit(mapArr0,c0);
         return map_save(mapD0,da0);
      }
   }
}


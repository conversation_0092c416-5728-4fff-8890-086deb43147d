package dataAll.pet.gene.creator
{
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import dataAll.pet.gene.GeneData;
   import dataAll.pet.gene.save.GeneSave;
   import dataAll.skill.HeroSkillReset;
   
   public class GeneDataMoveCtrl
   {
      public function GeneDataMoveCtrl()
      {
         super();
      }
      
      public static function moveData(petData0:PetData, geneData0:GeneData) : void
      {
         Gaming.PG.da.geneBag.removeData(geneData0);
         petData0.gene.save.obj = geneData0.save.obj;
         petData0.gene.save.itemsLevel = geneData0.save.itemsLevel;
         petData0.gene.save.addLevel = geneData0.save.addLevel;
         petData0.fleshProData();
      }
      
      public static function moveDataAll(petData0:PetData, geneData0:GeneData) : void
      {
         moveData(petData0,geneData0);
         petData0.gene.save.laterSkillArr = geneData0.save.laterSkillArr;
         petData0.gene.save.talentSkillArr = geneData0.save.talentSkillArr;
         var g0:GiftAddDefineGroup = HeroSkillReset.getReset(petData0.skill.dataArr);
         petData0.skill.resetAllSkill();
         GiftAddit.addByDefineGroup(g0,Gaming.PG.da);
      }
      
      public static function getMust(geneSave0:GeneSave) : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         var colorMul0:Number = getMustColorMul(geneSave0.color);
         d0.lv = geneSave0.getTrueLevel();
         d0.coin = Math.ceil(Gaming.defineGroup.normal.getPlayerCoinIncome(d0.lv) / 10 * colorMul0);
         var num0:int = Math.ceil(getGodStoneNum(geneSave0) * colorMul0);
         d0.inThingsDataByArr(["godStone;" + num0]);
         return d0;
      }
      
      private static function getGodStoneNum(geneSave0:GeneSave) : int
      {
         return Gaming.defineGroup.normal.getPropertyValue("skillStoneDrop",geneSave0.getGeneDropLevel()) / 2;
      }
      
      private static function getMustColorMul(color0:String) : Number
      {
         if(color0 == "red")
         {
            return 1.3;
         }
         if(color0 == "orange")
         {
            return 1;
         }
         if(color0 == "purple")
         {
            return 0.85;
         }
         if(color0 == "blue")
         {
            return 0.7;
         }
         return 0.5;
      }
   }
}


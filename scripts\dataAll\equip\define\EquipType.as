package dataAll.equip.define
{
   public class EquipType
   {
      public static const HEAD:String = "head";
      
      public static const COAT:String = "coat";
      
      public static const PANTS:String = "pants";
      
      public static const BELT:String = "belt";
      
      public static const FASHION:String = "fashion";
      
      public static const VEHICLE:String = "vehicle";
      
      public static const WEAPON:String = "weapon";
      
      public static const DEVICE:String = "device";
      
      public static const JEWELRY:String = "jewelry";
      
      public static const SHIELD:String = "shield";
      
      public static const TYPE_ARR:Array = [HEAD,COAT,PANTS,BELT,FASHION,VEHICLE,WEAPON,DEVICE,JEWELRY,SHIELD];
      
      public static const WEAR_ARR:Array = TYPE_ARR;
      
      public static const SHOW_ARR:Array = [FASHION,HEAD,COAT,PANTS,BELT];
      
      public static const NORMAL_ARR:Array = [HEAD,COAT,PANTS,BELT];
      
      public static const IMG_ARR:Array = NORMAL_ARR;
      
      public static const NORMAL_ARR5:Array = [COAT,PANTS,BELT];
      
      public static const numArr:Array = [DEVICE,WEAPON,JEWELRY,SHIELD];
      
      public static const name_1Arr:Array = [DEVICE,WEAPON,SHIELD];
      
      public static const advancedArr:Array = [DEVICE,WEAPON,SHIELD,JEWELRY,VEHICLE];
      
      public static const aloneDefineGroupArr:Array = [VEHICLE,DEVICE,WEAPON,JEWELRY,SHIELD];
      
      public static const noSwapInLevelArr:Array = [VEHICLE,DEVICE,WEAPON,FASHION,JEWELRY,SHIELD];
      
      public static const SUIT_PRIORITY_NAME:String = COAT;
      
      public static const SUIT_TYPE_ARR:Array = [HEAD,COAT,PANTS,BELT];
      
      public static const canResolveArr:Array = [VEHICLE,DEVICE,WEAPON,FASHION,JEWELRY,SHIELD];
      
      public static const haveLifeArr:Array = [FASHION,DEVICE,WEAPON];
      
      public static const TYPE_CN_ARR:Array = ["头盔","战衣","战裤","腰带","时装","载具","副手","装置","饰品","护盾"];
      
      public static const ID_ARR:Array = ["11","12","13","14","01","02","03","04","05","06"];
      
      public static const canDecomposePartsArr:Array = NORMAL_ARR;
      
      public static const haveSkillShowFashionArr:Array = ["goldFalcon"];
      
      public function EquipType()
      {
         super();
      }
      
      public static function init() : void
      {
      }
      
      public static function getUpgradePartNum(type0:String) : int
      {
         return 1;
      }
      
      public static function getDecomposePartNum(type0:String) : int
      {
         return 3;
      }
      
      public static function getID_byType(type0:String) : String
      {
         return ID_ARR[TYPE_ARR.indexOf(type0)];
      }
      
      public static function getTypeArrByHeroLv(lv0:int) : Array
      {
         if(lv0 < 5)
         {
            return NORMAL_ARR5;
         }
         return NORMAL_ARR;
      }
      
      public static function getCnName(type0:String) : String
      {
         return TYPE_CN_ARR[TYPE_ARR.indexOf(type0)];
      }
      
      public static function getSite(type0:String) : int
      {
         return WEAR_ARR.indexOf(type0);
      }
      
      public static function getTypeBySite(index0:int) : String
      {
         return WEAR_ARR[index0];
      }
      
      public static function getCoverCn(type0:String) : String
      {
         if(type0 == HEAD)
         {
            return "头部";
         }
         if(type0 == COAT)
         {
            return "上身";
         }
         if(type0 == BELT)
         {
            return "腰部";
         }
         if(type0 == PANTS)
         {
            return "下身";
         }
         if(type0 == "all")
         {
            return "全身";
         }
         return "全身";
      }
   }
}


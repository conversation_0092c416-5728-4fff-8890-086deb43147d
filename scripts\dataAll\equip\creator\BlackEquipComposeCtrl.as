package dataAll.equip.creator
{
   import UI.UIOrder;
   import UI.bag.ItemsGripBtnListCtrl;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._player.PlayerData;
   import dataAll.arms.creator.ArmsComposeCtrl;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.save.EquipSave;
   import dataAll.equip.suit.SuitCtreator;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.define.IO_ComposeItemsDefine;
   import dataAll.things.ThingsData;
   import dataAll.things.define.ThingsDefine;
   
   public class BlackEquipComposeCtrl
   {
      private static var tempTip:String = "";
      
      private static var tempDa:ThingsData = null;
      
      public function BlackEquipComposeCtrl()
      {
         super();
      }
      
      public static function getThingsTipAll(d0:ThingsDefine, pd0:PlayerData) : String
      {
         var id0:IO_ComposeItemsDefine = d0.getComposeItemsDefine();
         if(id0 is EquipDefine)
         {
            return getThingsTip(d0,pd0);
         }
         if(id0 is ArmsDefine)
         {
            return ArmsComposeCtrl.getThingsTip(d0,pd0);
         }
         return d0.description;
      }
      
      public static function getThingsTip(d0:ThingsDefine, pd0:PlayerData) : String
      {
         var f0:EquipFatherDefine = null;
         var num0:int = 0;
         var must0:int = 0;
         var str0:String = null;
         var equipD0:EquipDefine = Gaming.defineGroup.equip.getDefine(d0.name);
         if(equipD0 is EquipDefine)
         {
            f0 = Gaming.defineGroup.equip.getFatherDefine(equipD0.father);
            num0 = pd0.thingsBag.getThingsNum(d0.name);
            must0 = equipD0.getComposeMustBlack();
            str0 = "可合成装备：<yellow " + (equipD0.haveLevelB() ? equipD0.itemsLevel + "级" : "") + equipD0.cnName + "/>";
            str0 += "\n所需碎片：" + ComMethod.colorMustNum(num0,must0) + "个";
            str0 += "\n\n<i1>|<blue <b>" + equipD0.cnName + "属性：</b>/>";
            str0 += "\n" + Gaming.defineGroup.equipCreator.getBlackChipObjTip(equipD0);
            str0 += "\n" + SuitCtreator.getSuitTip(f0,equipD0.itemsLevel,f0.color);
            str0 += "\n<i1>|<blue <b>获得方式：</b>/>";
            if(equipD0.blackDropLevelArr.length > 0)
            {
               str0 += "\n消灭" + StringMethod.concatStringArr(equipD0.blackDropLevelArr,99) + "级普通关卡首领有几率掉落。";
            }
            if(equipD0.description != "")
            {
               str0 += "\n" + equipD0.description;
            }
            if(d0.description != "")
            {
               str0 += "\n" + d0.description;
            }
            return str0;
         }
         return d0.description;
      }
      
      public static function composeNumClick(da0:ThingsData) : void
      {
         var must0:Number = NaN;
         var max0:Number = NaN;
         var min0:Number = NaN;
         var d0:ThingsDefine = da0.save.getDefine();
         var id0:IO_ComposeItemsDefine = d0.getComposeItemsDefine();
         var dg0:ItemsDataGroup = getSameBagDg(d0);
         if(Boolean(id0) && Boolean(dg0))
         {
            must0 = Number(id0.getComposeMustBlack());
            if(must0 > 0)
            {
               max0 = getComposeMax(da0);
               if(max0 > 0)
               {
                  min0 = 1;
                  tempDa = da0;
                  Gaming.uiGroup.alertBox.showNumChoose("选择合成数量（" + min0 + "~" + max0 + "）",max0,max0,min0,1,yes_composeNumClick);
               }
               else
               {
                  Gaming.uiGroup.alertBox.showError("碎片数量不足，或者背包空位不足。");
               }
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("当前背包空位不足。");
            }
         }
      }
      
      private static function yes_composeNumClick(num0:Number) : void
      {
         var max0:Number = NaN;
         if(Boolean(tempDa))
         {
            max0 = getComposeMax(tempDa);
            num0 = NumberMethod.limitRange(num0,0,max0);
            if(num0 > 0)
            {
               composeAll(tempDa,num0);
            }
         }
      }
      
      private static function getSameBagDg(d0:ThingsDefine) : ItemsDataGroup
      {
         var id0:IO_ComposeItemsDefine = d0.getComposeItemsDefine();
         if(id0 is EquipDefine)
         {
            return Gaming.PG.da.equipBag;
         }
         if(id0 is ArmsDefine)
         {
            return Gaming.PG.da.armsBag;
         }
         return null;
      }
      
      private static function getComposeMax(da0:ThingsData) : Number
      {
         var must0:Number = NaN;
         var num0:Number = NaN;
         var max0:Number = NaN;
         var space0:Number = NaN;
         var d0:ThingsDefine = da0.save.getDefine();
         var id0:IO_ComposeItemsDefine = d0.getComposeItemsDefine();
         var dg0:ItemsDataGroup = getSameBagDg(d0);
         if(Boolean(id0) && Boolean(dg0))
         {
            must0 = Number(id0.getComposeMustBlack());
            if(must0 > 0)
            {
               num0 = da0.getNowNum();
               max0 = Math.floor(num0 / must0);
               space0 = dg0.getSpaceSiteNum();
               if(max0 > space0)
               {
                  max0 = space0;
               }
               return max0;
            }
         }
         return 0;
      }
      
      public static function composeAll(da0:ThingsData, num0:Number = 1) : void
      {
         var d0:ThingsDefine = da0.save.getDefine();
         var id0:IO_ComposeItemsDefine = d0.getComposeItemsDefine();
         if(id0 is EquipDefine)
         {
            compose(da0,num0);
         }
         else if(id0 is ArmsDefine)
         {
            ArmsComposeCtrl.compose(da0);
         }
      }
      
      public static function compose(da0:ThingsData, comNum0:Number = 1) : void
      {
         var d0:ThingsDefine = null;
         var pd0:PlayerData = null;
         var equipD0:EquipDefine = null;
         var num0:int = 0;
         var must0:int = 0;
         var proConstB0:Boolean = false;
         var i:int = 0;
         var equipS0:EquipSave = null;
         var loginB0:Boolean = Gaming.PG.loginData.isLoginByJS();
         if(!loginB0)
         {
            Gaming.uiGroup.alertBox.showError("您的账号已经退出登录，无法进行此操作。");
         }
         else
         {
            d0 = da0.save.getDefine();
            pd0 = da0.playerData;
            equipD0 = Gaming.defineGroup.equip.getDefine(d0.name);
            if(equipD0 is EquipDefine)
            {
               num0 = da0.save.nowNum;
               must0 = equipD0.getComposeMustBlack() * comNum0;
               if(num0 < must0)
               {
                  Gaming.uiGroup.alertBox.showError("碎片个数不足" + must0 + "个，无法合成" + equipD0.cnName + "。");
               }
               else if(pd0.equipBag.getSpaceSiteNum() < comNum0)
               {
                  Gaming.uiGroup.alertBox.showError("背包空位不足" + comNum0 + "，无法合成装备。");
               }
               else
               {
                  proConstB0 = equipD0.getComposeProConstB();
                  pd0.thingsBag.useThings(d0.name,must0);
                  for(i = 0; i < comNum0; i++)
                  {
                     equipS0 = Gaming.defineGroup.equipCreator.getBlackSave(equipD0.itemsLevel,equipD0.name,!proConstB0);
                     pd0.equipBag.addSave(equipS0);
                  }
                  ItemsGripBtnListCtrl.fleshAllBy(pd0.thingsBag);
                  tempTip = "成功合成" + comNum0 + "个 " + equipD0.cnName + "！";
                  if(proConstB0)
                  {
                     affterCompose();
                  }
                  else
                  {
                     UIOrder.save(true,true,true,affterCompose);
                  }
               }
            }
         }
      }
      
      private static function affterCompose(v:* = null) : void
      {
         Gaming.uiGroup.alertBox.showSuccess(tempTip);
      }
   }
}


package dataAll._app.setting.key
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import flash.ui.Keyboard;
   
   public class KeyActionDefine
   {
      public static var type_arr:Array = ["single","p1","p2"];
      
      public static const singleArr:Array = ["single"];
      
      public static const doubleArr:Array = ["p1","p2"];
      
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var pubB:Boolean = false;
      
      public var newB:Boolean = false;
      
      public var single:Array = [];
      
      public var p1:Array = [];
      
      public var p2:Array = [];
      
      public function KeyActionDefine()
      {
         super();
      }
      
      public static function getTypeArr(label0:String) : Array
      {
         return KeyActionDefine[label0 + "Arr"];
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.changeToKeyCode();
      }
      
      public function changeToKeyCode() : void
      {
         this.changeOneToKeyCode(this.single);
         this.changeOneToKeyCode(this.p1);
         this.changeOneToKeyCode(this.p2);
      }
      
      private function changeOneToKeyCode(arr0:Array) : void
      {
         var n:* = undefined;
         var str0:String = null;
         for(n in arr0)
         {
            str0 = arr0[n];
            arr0[n] = Keyboard[str0];
            if(str0 == "mouse")
            {
               arr0[n] = -1;
            }
            if(str0 == "")
            {
               arr0[n] = 0;
            }
         }
      }
      
      public function getColorCnName() : String
      {
         if(this.newB)
         {
            return ComMethod.color(this.cnName,"#FFFF00");
         }
         return this.cnName;
      }
   }
}


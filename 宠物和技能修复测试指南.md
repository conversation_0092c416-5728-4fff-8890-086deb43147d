# 宠物和技能功能修复测试指南

## 修复内容总结

### 1. 宠物基因体添加功能修复 ✅

**重要说明**：游戏中宠物是通过基因体孵化获得的，不能直接添加宠物！

**主要改进**：
- 🔄 **修正机制**：改为添加基因体而不是直接添加宠物
- 🧬 **多种添加方法**：基因背包 → GM命令 → 物品背包
- 📦 **智能背包管理**：自动扩展相应背包空间
- 🔍 **详细调试信息**：提供完整的执行过程反馈

**修复的文件**：
- `scripts/w_test/cheating/PetCheating.as`

### 2. 通用物品编辑功能 ✅

**重大升级**：创建了通用编辑控制器 `ItemsEditCtrl`，支持多种物品类型！

**支持的物品类型**：
- 🗡️ **武器 (ArmsData)**：等级编辑，支持满级/重置
- 🛡️ **装备 (EquipData)**：等级编辑，支持满级/重置
- 🚗 **载具 (VehicleData)**：等级编辑，支持满级/重置
- ⚡ **技能 (HeroSkillData)**：等级编辑，支持满级/重置
- 🃏 **魂卡 (BossCardData)**：星级编辑，支持满星/重置

**功能特点**：
- 🎯 **智能按钮定位**：编辑按钮显示在物品图标右侧，避免遮挡
- 🎨 **统一视觉风格**：橙色背景，白色文字，醒目易识别
- 🛡️ **全面错误处理**：数据验证和用户友好的错误提示
- 🔄 **自动界面刷新**：编辑后立即更新所有相关界面

**修复的文件**：
- `scripts/UI/bag/ItemsEditCtrl.as` (新增通用编辑控制器)
- `scripts/UI/skill/SkillWearUI.as` (集成编辑功能)
- `scripts/UI/bag/WearUI.as` (装备和武器编辑)
- `scripts/UI/bag/BagUI.as` (背包编辑功能)
- `scripts/UI/vehicle/VehicleUI.as` (载具编辑)
- `scripts/UI/edit/BosseditCardBoard.as` (魂卡编辑)

### 3. 新增调试功能 ✅

**新功能**：
- 添加了 `debugPetSystem` 方法用于诊断宠物系统状态
- 提供详细的系统组件检查
- 测试基因定义和创建器功能

## 测试步骤

### 测试宠物功能

#### 1. 基础测试
1. 打开游戏，按 `~` 键打开GM控制台
2. 点击"宠物"分类
3. 点击"🔍 调试宠物系统"按钮
4. 查看调试信息，确认系统组件状态

#### 2. 基因体添加测试
1. 在GM控制台的"宠物"分类中：
   - 点击"🧬 获取所有基因体" - 尝试添加所有可用基因体
   - 点击"🧬 安全添加基因体" - 使用更保守的方法添加基因体
   - 点击"🧬 简化添加基因体" - 只添加几个基础基因体
   - 点击"🧪 测试单个基因体" - 测试添加单个基因体

2. 观察输出信息：
   - ✅ 表示成功添加基因体
   - ❌ 表示失败
   - 🧬 显示添加的基因体数量
   - 💡 提示需要孵化才能获得宠物
   - 📋 调试信息显示详细的执行过程

3. 检查基因体背包：
   - 打开基因背包或物品背包
   - 确认新基因体是否正确显示
   - 尝试孵化基因体获得宠物

#### 3. 宠物功能验证
1. 尝试查看新添加的宠物信息
2. 测试宠物的各种功能（如果可用）
3. 确认宠物数据保存正确

### 测试技能编辑功能

#### 1. 准备工作
1. 确保角色已学习一些技能
2. 打开技能界面（快捷键或主界面按钮）
3. 切换到"装备"标签页

#### 2. 技能编辑测试
1. **显示编辑按钮**：
   - 点击任意已装备的技能图标
   - 应该在技能图标附近显示橙色的"编辑"按钮

2. **编辑功能测试**：
   - 点击"编辑"按钮
   - 应该弹出技能编辑对话框
   - 对话框显示当前技能信息和操作说明

3. **等级设置测试**：
   - 输入数字（如 `50`）设置具体等级
   - 输入 `max` 设置为满级
   - 输入 `reset` 重置为1级

4. **验证结果**：
   - 检查技能等级是否正确更改
   - 确认界面显示已更新
   - 验证成功消息是否显示

#### 3. 边界情况测试
1. 输入超过最大等级的数字
2. 输入无效字符
3. 测试不同类型的技能

## 常见问题排查

### 宠物添加失败
**症状**：点击宠物添加按钮后显示失败信息

**排查步骤**：
1. 使用"🔍 调试宠物系统"检查系统状态
2. 查看调试信息中的具体错误
3. 确认基因定义组是否正常加载
4. 检查宠物背包是否有足够空间

**可能原因**：
- 基因定义文件缺失或损坏
- 宠物背包空间不足
- 游戏数据初始化不完整

### 技能编辑按钮不显示
**症状**：点击技能图标后没有显示编辑按钮

**排查步骤**：
1. 确认点击的是已装备的技能（在装备栏中）
2. 确认技能升级界面没有打开
3. 检查技能是否有有效数据

**可能原因**：
- 技能数据无效
- UI层级问题
- 事件监听器冲突

### 技能等级修改无效
**症状**：编辑技能等级后没有变化

**排查步骤**：
1. 检查输入的等级是否在有效范围内
2. 确认技能定义是否支持等级修改
3. 查看错误消息获取详细信息

## 成功标准

### 宠物功能
- [ ] 调试系统显示所有组件正常
- [ ] 至少一种宠物添加方法成功
- [ ] 新宠物在背包中正确显示
- [ ] 宠物数据完整且可用

### 技能编辑功能
- [ ] 编辑按钮正确显示
- [ ] 编辑对话框正常弹出
- [ ] 等级修改功能正常工作
- [ ] 界面正确刷新显示新等级

## 注意事项

1. **备份存档**：测试前建议备份游戏存档
2. **分步测试**：建议按步骤逐一测试，便于定位问题
3. **错误记录**：如遇到问题，记录完整的错误信息
4. **重启测试**：某些修改可能需要重启游戏才能生效

## 技术细节

### 宠物添加流程
```
1. 检查系统组件 → debugPetSystem()
2. 扩展背包空间 → Gaming.PG.da.pet.addBagNum()
3. 获取宠物列表 → 多种方法备用
4. 创建基因数据 → 增强错误处理
5. 添加到背包 → Gaming.PG.da.pet.addByGeneData()
6. 刷新UI显示 → Gaming.uiGroup.petUI.fleshData()
```

### 技能编辑流程
```
1. 点击技能图标 → gripClick()
2. 显示编辑按钮 → showEditButton() (改进样式)
3. 点击编辑按钮 → editButtonClick()
4. 显示编辑对话框 → showSkillEditDialog()
5. 处理用户输入 → processSkillEdit() (增强验证)
6. 更新技能数据 → skillData.save.lv = newLevel
7. 刷新显示 → 多层级刷新机制
```

---

## 🆕 通用编辑功能扩展

### 新增功能概述
现在支持编辑以下物品类型：
- 🗡️ **武器** (ArmsData) - 等级编辑
- 🛡️ **装备** (EquipData) - 等级编辑
- 🚗 **载具** (VehicleData) - 等级编辑
- ⚡ **技能** (HeroSkillData) - 等级编辑
- 🃏 **魂卡** (BossCardData) - 星级编辑

### 通用编辑测试步骤

#### 1. 基础功能测试
1. 打开相应的界面（背包、角色、技能、载具、魂卡等）
2. 点击任意支持编辑的物品图标
3. 观察是否在物品图标右侧出现橙色"编辑"按钮
4. 点击编辑按钮，确认弹出编辑对话框

#### 2. 编辑选项测试
- **数字输入**：输入"10"设置为10级/10星
- **满级命令**：输入"max"设置为最高等级/星级
- **重置命令**：输入"reset"重置为1级/1星
- **错误输入**：输入"abc"测试错误处理

#### 3. 界面刷新验证
- 编辑后检查物品显示是否立即更新
- 确认相关界面（属性面板等）同步刷新
- 验证数据保存正确性

### 输入框改进
- ✅ 宽度从240像素增加到400像素
- ✅ 最大字符数从50增加到100
- ✅ 添加取消按钮
- ✅ 支持ESC键取消输入

### 测试清单
- [ ] 武器编辑功能正常
- [ ] 装备编辑功能正常
- [ ] 载具编辑功能正常
- [ ] 技能编辑功能正常
- [ ] 魂卡编辑功能正常
- [ ] 编辑按钮位置正确
- [ ] 输入框显示完整
- [ ] 错误处理正常
- [ ] 界面刷新正常

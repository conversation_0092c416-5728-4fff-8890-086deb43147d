package dataAll.must.define
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll.pro.NormalPropertyArrayDefineGroup;
   
   public class MustDefineGroup
   {
      private var skill:Object = {};
      
      private var girlSkill:Object = {};
      
      private var arms:Object = {};
      
      private var moreArms:Object = {};
      
      private var parts:Object = {};
      
      private var bag:Object = {};
      
      public var house:MustDefine = new MustDefine();
      
      public var addArenaNum:MustDefine = new MustDefine();
      
      public var buyTaskNum:MustDefine = new MustDefine();
      
      public var buyKingTaskNum:MustDefine = new MustDefine();
      
      public var buyExtraTaskNum:MustDefine = new MustDefine();
      
      public var downBossLevelNum:MustDefine = new MustDefine();
      
      public var loveGiftNum:MustDefine = new MustDefine();
      
      public var skillResetNum:MustDefine = new MustDefine();
      
      public var fillDailyNum:MustDefine = new MustDefine();
      
      public var fleshBlackMarket:MustDefine = new MustDefine();
      
      public var changeName:MustDefine = new MustDefine();
      
      private var wilderSweepObj:Object = {};
      
      private var moneyArr:Array = ["fleshBlackMarket","downBossLevelNum","skill","arms","moreArms","bag","addArenaNum","buyKingTaskNum","buyTaskNum","skillResetNum","fillDailyNum","buyExtraTaskNum"];
      
      private var thingsTipObj:Object = {};
      
      public function MustDefineGroup()
      {
         super();
      }
      
      public function init() : void
      {
         var d0:MustDefine = null;
         var ng0:NormalPropertyArrayDefineGroup = Gaming.defineGroup.normal;
         this.add(this.skill,3,"13;" + ComMethod.toInt(Number(ng0.getPlayerCoinIncome(13) * 0.3),2) + ";0");
         this.add(this.skill,4,"20;" + ComMethod.toInt(Number(ng0.getPlayerCoinIncome(20) * 0.7),3) + ";0");
         this.add(this.skill,5,"27;" + ComMethod.toInt(Number(ng0.getPlayerCoinIncome(27) * 0.7),3) + ";0");
         this.add(this.skill,6,"34;" + ComMethod.toInt(Number(ng0.getPlayerCoinIncome(34) * 0.7),3) + ";0");
         this.add(this.skill,7,"40;0;10;1225");
         this.add(this.skill,8,"48;0;50;1226");
         this.add(this.skill,9,"55;0;150;1579");
         this.add(this.skill,10,"75;0;10;1580");
         this.add(this.girlSkill,3,"27;" + ComMethod.toInt(Number(ng0.getPlayerCoinIncome(27) * 0.7),3) + ";0");
         this.add(this.girlSkill,4,"34;" + ComMethod.toInt(Number(ng0.getPlayerCoinIncome(34) * 0.7),3) + ";0");
         this.add(this.girlSkill,5,"40;0;10;1225");
         this.add(this.girlSkill,6,"48;0;50;1226");
         this.add(this.girlSkill,7,"55;0;150;1579");
         this.add(this.girlSkill,8,"75;0;250;1661");
         this.add(this.girlSkill,9,"75;0;250;1661");
         this.add(this.girlSkill,10,"暂未开放");
         this.add(this.arms,3,"通关北斗城");
         this.add(this.arms,4,"26;" + ComMethod.toInt(Number(ng0.getPlayerCoinIncome(26) * 0.8),4) + ";0");
         this.add(this.arms,5,"39;0;20;1227");
         this.add(this.arms,6,"50;0;80;1228");
         this.add(this.parts,11,"10;500000;0");
         this.add(this.parts,12,"10;0;30;4462");
         d0 = this.add(this.parts,13,"10;0;0");
         d0.inThingsDataByArr(["partsGridCard;1"]);
         this.add(this.parts,14,"10;0;30;4462");
         this.add(this.parts,15,"10;0;30;4462");
         this.add(this.moreArms,3,"13;" + ComMethod.toInt(Number(ng0.getPlayerCoinIncome(13) * 0.4),3) + ";0");
         this.add(this.moreArms,4,"26;" + ComMethod.toInt(Number(ng0.getPlayerCoinIncome(26) * 0.8),4) + ";0");
         this.add(this.moreArms,5,"39;0;16;3124");
         this.add(this.moreArms,6,"50;0;60;3125");
         this.add(this.bag,0,"0;0;5;1229");
         this.house.inDataByStr("0;0;5;1581","houseLock","仓库位置","",true);
         this.addArenaNum.inDataByStr("0;0;15;1241","addArenaNum","添加竞技场次数");
         this.buyTaskNum.inDataByStr("0;0;15;4509","buyTaskNum","普通任务次数");
         this.buyKingTaskNum.inDataByStr("0;0;5;1681","buyKingTaskNum","擒王任务次数");
         this.buyExtraTaskNum.inDataByStr("0;0;15;4510","buyExtraTaskNum","副本任务次数");
         this.downBossLevelNum.inDataByStr("0;0;8;1385","downBossLevelNum","降低擒王首领等级");
         this.loveGiftNum.inDataByStr("0;0;10;2080","loveGiftNum","赠送次数");
         this.skillResetNum.inDataByStr("0;0;20;1383","skillResetNum","技能重置");
         this.fillDailyNum.inDataByStr("0;0;9;9","fillDailyNum","补签");
         this.fleshBlackMarket.inDataByStr("0;0;15;3983","fleshBlackMarket","刷新神秘商店列表");
         this.changeName.inThingsDataByArr(["renameCard;1"]);
         this.add(this.wilderSweepObj,0,"0;0;10;2617","你确定要扫荡当前秘境副本？");
         this.add(this.wilderSweepObj,1,"0;0;12;2616","你确定要扫荡当前秘境副本？");
         this.add(this.wilderSweepObj,2,"0;0;15;2615","你确定要扫荡当前秘境副本？");
         this.add(this.wilderSweepObj,3,"0;0;20;2614","你确定要扫荡当前秘境副本？");
         this.thingsTipObj["partsGridCard"] = "零件券兑换";
      }
      
      public function getThingsTipBack(name0:String) : String
      {
         if(this.thingsTipObj.hasOwnProperty(name0))
         {
            return this.thingsTipObj[name0];
         }
         return "";
      }
      
      private function add(obj0:Object, trueSite0:int, str0:String, tipBoxString0:String = "") : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         var name0:String = "";
         var everB0:Boolean = true;
         if(obj0 == this.skill)
         {
            name0 = "skill" + trueSite0;
         }
         else if(obj0 == this.parts)
         {
            name0 = "parts" + trueSite0;
         }
         else if(obj0 == this.girlSkill)
         {
            name0 = "girlSkill" + trueSite0;
         }
         else if(obj0 == this.arms)
         {
            name0 = "arms" + trueSite0;
         }
         else if(obj0 == this.moreArms)
         {
            name0 = "moreArms" + trueSite0;
         }
         else if(obj0 == this.bag)
         {
            name0 = "bag" + trueSite0;
         }
         else if(obj0 == this.wilderSweepObj)
         {
            everB0 = false;
            name0 = "wilderSweep" + trueSite0;
         }
         d0.inDataByStr(str0,name0,name0,tipBoxString0,everB0);
         obj0[trueSite0] = d0;
         return d0;
      }
      
      public function getSkill(trueSite0:int, sex0:String) : MustDefine
      {
         return this.skill[trueSite0];
      }
      
      public function getSkillGoldGap(trueSite0:int) : Number
      {
         var a0:MustDefine = null;
         var b0:MustDefine = null;
         if(this.skill.hasOwnProperty(trueSite0))
         {
            a0 = this.skill[trueSite0];
            b0 = this.girlSkill[trueSite0];
            return b0.money - a0.money;
         }
         return 0;
      }
      
      public function getArms(trueSite0:int, moreB:Boolean = false) : MustDefine
      {
         return moreB ? this.moreArms[trueSite0] : this.arms[trueSite0];
      }
      
      public function getParts(trueSite0:int) : MustDefine
      {
         return this.parts[trueSite0];
      }
      
      public function getBag() : MustDefine
      {
         return this.bag[0];
      }
      
      public function getBagNum(num0:int) : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         d0.inThingsDataByArr(["bagGridCard;" + num0]);
         return d0;
      }
      
      public function getHouseNum(num0:int) : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         d0.inThingsDataByArr(["houseGridCard;" + num0]);
         return d0;
      }
      
      public function getWilderSweep(diff0:String) : MustDefine
      {
         return this.wilderSweepObj[int(diff0)];
      }
      
      public function getGoodsDefineByPropsId(id0:String) : GoodsDefine
      {
         var n:* = undefined;
         var name0:String = null;
         var obj0:Object = null;
         var md2:MustDefine = null;
         var md0:MustDefine = null;
         if(id0 == "1483")
         {
            id0 = "1487";
         }
         for(n in this.moneyArr)
         {
            name0 = this.moneyArr[n];
            if(!(this[name0] is MustDefine))
            {
               obj0 = this[name0];
               for(n in obj0)
               {
                  md2 = obj0[n];
                  if(md2.propId == id0)
                  {
                     return this.getGoodsDefine(md2,name0 + "_" + n);
                  }
               }
            }
            else
            {
               md0 = this[name0];
               if(md0.propId == id0)
               {
                  return this.getGoodsDefine(md0,name0);
               }
            }
         }
         return null;
      }
      
      private function getGoodsDefine(md0:MustDefine, cnName0:String) : GoodsDefine
      {
         var d0:GoodsDefine = new GoodsDefine();
         d0.cnName = cnName0;
         d0.price = md0.money;
         return d0;
      }
   }
}


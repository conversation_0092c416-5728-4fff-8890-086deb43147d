package dataAll._app.union.battle
{
   import UI.test.SaveTestBox;
   import com.sounto.cf.StringCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.ObjectMethod;
   import dataAll._app.union.info.MemberInfo;
   import dataAll._app.union.info.MemberListInfo;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class UBattleAgent
   {
      private static const stringCF:StringCF = new StringCF();
      
      private var mapObj:Object = {};
      
      private var _attackTB:Boolean = false;
      
      private var _giftTB:Boolean = false;
      
      private var _canGiftB:Boolean = false;
      
      public function UBattleAgent()
      {
         super();
      }
      
      public static function get START_TIME() : String
      {
         return stringCF.getAttribute("START_TIME") as String;
      }
      
      public static function set START_TIME(str0:String) : void
      {
         stringCF.setAttribute("START_TIME",str0);
      }
      
      public static function staticInit() : void
      {
         START_TIME = "2022-7-30";
      }
      
      public static function getStartTimeDate() : StringDate
      {
         return new StringDate(START_TIME);
      }
      
      public static function getPhase(timeStr0:String) : int
      {
         var now0:StringDate = new StringDate(timeStr0);
         return getPhaseByTimeDate(now0);
      }
      
      private static function getPhaseByTimeDate(now0:StringDate) : int
      {
         var start0:StringDate = getStartTimeDate();
         var c0:int = now0.reductionOne(start0);
         return int(c0 / 7) + 1;
      }
      
      public static function test() : void
      {
         var str0:String = null;
         var da0:StringDate = null;
         var phase0:int = 0;
         for(var i:int = 0; i < 30; i++)
         {
            str0 = "2022-8-" + (i + 1) + " 23:00:00";
            da0 = new StringDate(str0);
            phase0 = getPhaseByTimeDate(da0);
            INIT.tempTrace(str0 + "  ----  " + phase0);
         }
      }
      
      private static function isAttackTimeB(st0:StringDate) : Boolean
      {
         var day0:int = st0.getDateClass().day;
         var hour0:int = st0.hours;
         if(day0 == 6 && hour0 >= 8)
         {
            return true;
         }
         if(day0 == 0 && hour0 < 21)
         {
            return true;
         }
         return false;
      }
      
      private static function isGiftTimeB(st0:StringDate) : Boolean
      {
         var day0:int = st0.getDateClass().day;
         if(day0 >= 1 && day0 <= 5)
         {
            return true;
         }
         return false;
      }
      
      private static function isAttackDay(st0:StringDate) : Boolean
      {
         var day0:int = st0.getDateClass().day;
         if(day0 == 6 || day0 == 0)
         {
            return true;
         }
         return false;
      }
      
      public static function getScore(levelTime0:Number) : Number
      {
         if(nullLevelTime(levelTime0))
         {
            return 4;
         }
         var s0:Number = Math.round((100 - levelTime0) / 2);
         var max0:Number = getScoreOneMax();
         if(s0 > max0)
         {
            s0 = max0;
         }
         else if(s0 < 4)
         {
            s0 = 4;
         }
         return s0;
      }
      
      public static function getScoreOneMax() : Number
      {
         return 48;
      }
      
      public static function getLifeMul(score0:Number) : Number
      {
         var v0:Number = score0 * 1.4 / 100 + 0.05;
         return NumberMethod.toFixed(v0,2);
      }
      
      public static function getDpsMul(score0:Number) : Number
      {
         var v0:Number = score0 * 0.008 + 0.05;
         return NumberMethod.toFixed(v0,2);
      }
      
      public static function countUnionDpsMul(score0:Number) : Number
      {
         var v0:Number = score0 * 0.05 / 100;
         if(v0 > 0.46)
         {
            v0 = 0.46;
         }
         return NumberMethod.toFixed(v0,2);
      }
      
      public static function nullLevelTime(time0:Number) : Boolean
      {
         if(time0 < 2)
         {
            return true;
         }
         return false;
      }
      
      private static function countUnionGiftMul(score0:Number, attackMapNum0:int) : Number
      {
         var v0:int = Math.floor(score0 * 0.035);
         if(v0 > 33)
         {
            v0 = 33;
         }
         if(attackMapNum0 > 20)
         {
            attackMapNum0 = 20;
         }
         var min0:int = attackMapNum0 * 0.5;
         if(v0 < min0)
         {
            v0 = min0;
         }
         return v0;
      }
      
      private static function getBaseGift() : GiftAddDefineGroup
      {
         var gift0:GiftAddDefineGroup = new GiftAddDefineGroup();
         gift0.addGiftByStr("things;strengthenDrug;4.82142857142857");
         gift0.addGiftByStr("things;converStone;0.535714285714286");
         gift0.addGiftByStr("things;godStone;0.535714285714286");
         gift0.addGiftByStr("things;deviceChest;0.25");
         gift0.addGiftByStr("things;weaponChest;0.25");
         gift0.addGiftByStr("things;normalChest;0.178571428571429");
         gift0.addGiftByStr("things;magicChest;0.178571428571429");
         gift0.addGiftByStr("things;allBlackEquipCash;0.285714285714286");
         gift0.addGiftByStr("things;allBlackCash;0.178571428571429");
         gift0.addGiftByStr("things;armsGemChest;0.142857142857143");
         gift0.addGiftByStr("parts;huntParts_1;0.1");
         gift0.addGiftByStr("parts;acidicParts_1;0.1");
         gift0.addGiftByStr("things;sweepingCard;0.0178571428571429");
         return gift0;
      }
      
      public function getAgent(name0:String) : UBattleMapAgent
      {
         return this.mapObj[name0];
      }
      
      public function getObj() : Object
      {
         return this.mapObj;
      }
      
      private function clearData() : void
      {
         var a0:UBattleMapAgent = null;
         this._attackTB = false;
         this._giftTB = false;
         this._canGiftB = false;
         for each(a0 in this.mapObj)
         {
            a0.clearData();
         }
      }
      
      public function haveDataB() : Boolean
      {
         return ObjectMethod.getObjElementNum(this.mapObj) > 0;
      }
      
      public function inData(info0:MemberListInfo, severTime0:String, meInfo0:MemberInfo) : void
      {
         var i0:MemberInfo = null;
         var attackB0:Boolean = false;
         var attackTime0:String = null;
         var attackDate0:StringDate = null;
         var phase0:int = 0;
         var isAttackDayB0:Boolean = false;
         var a0:UBattleMapAgent = null;
         this.addMap();
         this.clearData();
         var nowPhase0:int = getPhase(severTime0);
         var iarr0:Array = info0.arr;
         var inMeB0:Boolean = false;
         var index0:int = 0;
         for each(i0 in iarr0)
         {
            if(inMeB0 == false && meInfo0.samePan(i0))
            {
               meInfo0.inBattleExtra(i0);
               inMeB0 = true;
               i0 = meInfo0;
            }
            attackB0 = false;
            i0.setBattleRank(0);
            i0.setAttackB(false);
            attackTime0 = i0.extraObj.bt;
            if(attackTime0 != "")
            {
               attackDate0 = new StringDate(attackTime0);
               phase0 = getPhaseByTimeDate(attackDate0);
               isAttackDayB0 = isAttackDay(attackDate0);
               if(phase0 == nowPhase0 && isAttackDayB0)
               {
                  i0.setAttackB(true);
                  attackB0 = true;
                  a0 = this.getAgent(i0.extraObj.mp);
                  if(Boolean(a0))
                  {
                     a0.addMemberInfo(i0);
                  }
               }
            }
            index0++;
            SaveTestBox.addText((attackB0 ? "@" : " ") + index0 + "：" + i0.getBattleTrace(meInfo0));
         }
         this.sortByLevelTime();
         this.fleshTimeState(severTime0);
      }
      
      private function addMap() : void
      {
         var defArr0:Array = null;
         var d0:UnionBattleMapDefine = null;
         var a0:UBattleMapAgent = null;
         if(ObjectMethod.getObjElementNum(this.mapObj) == 0)
         {
            defArr0 = Gaming.defineGroup.union.battle.mapArr;
            for each(d0 in defArr0)
            {
               a0 = new UBattleMapAgent();
               a0.init(d0);
               this.mapObj[d0.name] = a0;
            }
         }
      }
      
      private function sortByLevelTime() : void
      {
         var a0:UBattleMapAgent = null;
         for each(a0 in this.mapObj)
         {
            a0.sortByLevelTime();
         }
      }
      
      public function fleshTimeState(severTime0:String) : void
      {
         var readTime0:StringDate = null;
         var readDay0:int = 0;
         var st0:StringDate = new StringDate(severTime0);
         this._attackTB = isAttackTimeB(st0);
         this._giftTB = isGiftTimeB(st0);
         this._canGiftB = true;
         if(this._giftTB == false)
         {
            readTime0 = Gaming.PG.da.time.getReadTimeDate();
            readDay0 = readTime0.getDateClass().day;
            if(readDay0 >= 1 && readDay0 <= 5)
            {
               this._canGiftB = false;
            }
         }
      }
      
      public function get attackTB() : Boolean
      {
         return this._attackTB;
      }
      
      public function get giftTB() : Boolean
      {
         return this._giftTB;
      }
      
      public function get canGiftB() : Boolean
      {
         return this._canGiftB;
      }
      
      public function getAttackMapNum() : int
      {
         var a0:UBattleMapAgent = null;
         var num0:int = 0;
         for each(a0 in this.mapObj)
         {
            if(a0.getMemberNum() > 0)
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function getUnionScore() : int
      {
         var a0:UBattleMapAgent = null;
         var top0:MemberInfo = null;
         var s0:Number = NaN;
         var all0:Number = 0;
         for each(a0 in this.mapObj)
         {
            top0 = a0.getTopOneNull();
            if(Boolean(top0))
            {
               s0 = top0.getBattleScore();
               all0 += s0;
            }
         }
         return all0;
      }
      
      public function getAllTime() : Number
      {
         var a0:UBattleMapAgent = null;
         var top0:MemberInfo = null;
         var s0:Number = NaN;
         var time0:Number = 0;
         for each(a0 in this.mapObj)
         {
            top0 = a0.getTopOneNull();
            if(Boolean(top0))
            {
               s0 = top0.getBatTime();
               if(s0 > 0)
               {
                  time0 += s0;
               }
            }
         }
         return NumberMethod.toFixed(time0,2);
      }
      
      public function getAllMapNum() : int
      {
         var a0:UBattleMapAgent = null;
         var top0:MemberInfo = null;
         var s0:Number = NaN;
         var num0:Number = 0;
         for each(a0 in this.mapObj)
         {
            top0 = a0.getTopOneNull();
            if(Boolean(top0))
            {
               s0 = top0.getBatTime();
               if(s0 > 0)
               {
                  num0 += 1;
               }
            }
         }
         return num0;
      }
      
      public function getUnionDpsMul() : Number
      {
         var score0:int = this.getUnionScore();
         return countUnionDpsMul(score0);
      }
      
      public function getUnionGift() : GiftAddDefineGroup
      {
         return null;
      }
      
      public function getWeekGift() : GiftAddDefineGroup
      {
         var score0:int = this.getUnionScore();
         var attackMapNum0:int = this.getAttackMapNum();
         var mul0:Number = countUnionGiftMul(score0,attackMapNum0);
         var g0:GiftAddDefineGroup = getBaseGift();
         g0.addNumMulClear(mul0);
         g0.addNumMulClear(7);
         if(score0 >= 960)
         {
            g0.addGiftByStr("things;battleMedel;1");
            g0.addGiftByStr("head;battle4;1");
         }
         else if(score0 >= 900)
         {
            g0.addGiftByStr("head;battle4;1");
         }
         else if(score0 >= 850)
         {
            g0.addGiftByStr("head;battle3;1");
         }
         else if(score0 >= 800)
         {
            g0.addGiftByStr("head;battle2;1");
         }
         else if(score0 >= 750)
         {
            g0.addGiftByStr("head;battle1;1");
         }
         return g0;
      }
      
      public function getPlayerNum() : int
      {
         var a0:UBattleMapAgent = null;
         var all0:Number = 0;
         for each(a0 in this.mapObj)
         {
            all0 += a0.getMemberNum();
         }
         return all0;
      }
   }
}


package dataAll.skill.define.otherObj
{
   public class SkillChangeHurt
   {
      public static const lifePerLess:String = "lifePerLess";
      
      public static const player:String = "player";
      
      public static const lifePerMore:String = "lifePerMore";
      
      public static const onlyAttack:String = "onlyAttack";
      
      public static const onlyWeapon:String = "onlyWeapon";
      
      public static const accurate:String = "accurate";
      
      public static const hyperopia:String = "hyperopia";
      
      public static const myopia:String = "myopia";
      
      public static const backWeak:String = "backWeak";
      
      public static const atry:String = "atry";
      
      public static const despise:String = "despise";
      
      public static const killCharm:String = "killCharm";
      
      public static const defenceBounceAndImploding:String = "defenceBounceAndImploding";
      
      public static const likeMissle:String = "likeMissle";
      
      public static const followBullet:String = "followBullet";
      
      public static const enemyType:String = "enemyType";
      
      public static const cmldef:String = "cmldef";
      
      public static const vehicle:String = "vehicle";
      
      public static const stationaryBoss:String = "stationaryBoss";
      
      public function SkillChangeHurt()
      {
         super();
      }
   }
}


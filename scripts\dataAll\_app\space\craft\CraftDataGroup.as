package dataAll._app.space.craft
{
   import dataAll._base.OneDataGroup;
   import dataAll._base.OneSaveGroup;
   
   public class CraftDataGroup extends OneDataGroup
   {
      private var sG:CraftSaveGroup = null;
      
      private var nowData:CraftData = null;
      
      public function CraftDataGroup()
      {
         super();
         dataClass = CraftData;
      }
      
      override public function inData_bySave(sg0:OneSaveGroup) : void
      {
         this.sG = sg0 as CraftSaveGroup;
         super.inData_bySave(sg0);
         if(arr.length == 0)
         {
            this.addCraft("SilverShip","silverScreen");
         }
         this.setNowData(this.sG.now);
      }
      
      public function newWeek() : void
      {
         var da0:CraftData = null;
         for each(da0 in arr)
         {
            da0.newWeek();
         }
      }
      
      public function overGamingClear() : void
      {
         var da0:CraftData = null;
         for each(da0 in arr)
         {
            da0.overGamingClear();
         }
      }
      
      public function getUIArr() : Array
      {
         return arr;
      }
      
      public function addCraft(name0:String, firstSkill0:String = "") : CraftData
      {
         var s0:CraftSave = null;
         var da0:CraftData = null;
         var d0:CraftDefine = Gaming.defineGroup.craft.getDefine(name0);
         if(Boolean(d0))
         {
            s0 = new CraftSave();
            s0.n = d0.name;
            da0 = addSave(s0) as CraftData;
            if(firstSkill0 != "")
            {
               da0.setPoint(firstSkill0,1);
            }
            return da0;
         }
         return null;
      }
      
      public function getNowData() : CraftData
      {
         return this.nowData;
      }
      
      public function setNowData(name0:String) : CraftData
      {
         var da0:CraftData = null;
         if(name0 != "")
         {
            da0 = this.getData(name0);
            if(Boolean(da0))
            {
               this.nowData = da0;
               this.sG.now = name0;
               return da0;
            }
         }
         if(arr.length > 0)
         {
            this.nowData = arr[0];
            this.sG.now = this.nowData.name;
         }
         else
         {
            this.nowData = null;
            this.sG.now = "";
         }
         return null;
      }
      
      public function getData(name0:String) : CraftData
      {
         var da0:CraftData = null;
         for each(da0 in arr)
         {
            if(da0.name == name0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getCraftMaxLv() : int
      {
         var da0:CraftData = null;
         var max0:int = 0;
         for each(da0 in arr)
         {
            if(da0.level > max0)
            {
               max0 = da0.level;
            }
         }
         return max0;
      }
   }
}


package dataAll._app.food
{
   import com.sounto.cf.NiuBiCF;
   import dataAll._base.NormalDefine;
   import dataAll.gift.define.IO_GiftDefine;
   
   public class FoodRawDefine extends NormalDefine implements IO_GiftDefine
   {
      public static var pro_arr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var iconUrl:String = "";
      
      public var skill:String = "";
      
      public function FoodRawDefine()
      {
         super();
      }
      
      public function get dropPro() : Number
      {
         return this.CF.getAttribute("dropPro");
      }
      
      public function set dropPro(v0:Number) : void
      {
         this.CF.setAttribute("dropPro",v0);
      }
      
      override public function inData_byXML(xml0:XML, father0:String = "") : void
      {
         super.inData_byXML(xml0,father0);
         if(this.iconUrl == "")
         {
            this.iconUrl = "FoodUI/" + name;
         }
         if(this.skill == "")
         {
            this.skill = name + "FoodSkill";
         }
      }
      
      override protected function getBaseClassProArr() : Array
      {
         return pro_arr;
      }
      
      override protected function getFinalProArr() : Array
      {
         return pro_arr;
      }
      
      public function getGiftCn() : String
      {
         return cnName;
      }
      
      public function getGiftTip() : String
      {
         return "";
      }
      
      public function getGiftIconUrl() : String
      {
         return this.iconUrl;
      }
   }
}


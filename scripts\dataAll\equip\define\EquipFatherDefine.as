package dataAll.equip.define
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.items.creator.ItemsRefiningCtrl;
   import dataAll.pro.PropertyArrayDefine;
   
   public class EquipFatherDefine
   {
      public static const skillAddMoveCnArr:Array = [];
      
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var index:int = 0;
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var suitPro:Array = [];
      
      public var color:String = "";
      
      public var rareDropType:String = RareDropType.NONE;
      
      public var rareDropLv:int = 0;
      
      public var evoB:Boolean = false;
      
      public var resolveB:Boolean = false;
      
      public var composeProConstB:Boolean = false;
      
      public var suitObj:Object = {};
      
      public var suitArray:Array = [];
      
      public var haveSuitB:Boolean = false;
      
      public var partObj:Object = {};
      
      public var noConverB:Boolean = false;
      
      public function EquipFatherDefine()
      {
         super();
         this.chipNum = 0;
      }
      
      public function get chipNum() : Number
      {
         return this.CF.getAttribute("chipNum");
      }
      
      public function set chipNum(v0:Number) : void
      {
         this.CF.setAttribute("chipNum",v0);
      }
      
      public function inData_byXML(xml0:XML, index0:int) : void
      {
         var n:* = undefined;
         var str0:String = null;
         var d0:EquipSuitPropertyMul = null;
         this.index = index0;
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.evoB = Boolean(int(xml0.@evoB));
         this.chipNum = int(xml0.@chipNum);
         if(this.chipNum == 0)
         {
            this.chipNum = ItemsRefiningCtrl.getBlackChipNum();
         }
         for(n in this.suitPro)
         {
            str0 = this.suitPro[n];
            d0 = new EquipSuitPropertyMul();
            d0.inData_byStr(str0,n);
            this.suitArray.push(d0);
            this.suitObj[d0.name] = d0;
            this.haveSuitB = true;
         }
         if(this.canSkillAddMoveB())
         {
            skillAddMoveCnArr.push(this.cnName);
         }
      }
      
      public function canSkillAddMoveB() : Boolean
      {
         if(this.evoB && EquipColor.moreBlackB(this.color))
         {
            return true;
         }
         return false;
      }
      
      public function getConverB() : Boolean
      {
         return !this.noConverB && this.color == "";
      }
      
      public function getTitleText() : String
      {
         return this.cnName + "套装";
      }
      
      public function addPart(d0:EquipDefine) : void
      {
         this.partObj[d0.type] = d0;
      }
      
      public function getPart(type0:String) : EquipDefine
      {
         return this.partObj[type0];
      }
      
      public function getSortId() : String
      {
         return TextWay.toNum(String(999 - this.index),3);
      }
      
      public function getSuitProDefineArray() : Array
      {
         var mul0:EquipSuitPropertyMul = null;
         var d0:PropertyArrayDefine = null;
         var arr0:Array = [];
         for each(mul0 in this.suitArray)
         {
            d0 = Gaming.defineGroup.suitProperty.getDefine(mul0.name);
            arr0.push(d0);
         }
         return arr0;
      }
      
      public function getSuitProNameArr() : Array
      {
         var mul0:EquipSuitPropertyMul = null;
         var arr0:Array = [];
         for each(mul0 in this.suitArray)
         {
            arr0.push(mul0.name);
         }
         return arr0;
      }
      
      public function getProCnNameArr() : Array
      {
         var mul0:EquipSuitPropertyMul = null;
         var d0:PropertyArrayDefine = null;
         var arr0:Array = [];
         for each(mul0 in this.suitArray)
         {
            d0 = Gaming.defineGroup.suitProperty.getDefine(mul0.name);
            arr0.push(d0.cnName);
         }
         return arr0;
      }
   }
}


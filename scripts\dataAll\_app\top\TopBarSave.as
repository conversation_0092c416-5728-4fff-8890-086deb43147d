package dataAll._app.top
{
   import com.common.text.TextWay;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.top.define.TopBarDefineGroup;
   
   public class TopBarSave
   {
      public static var pro_arr:Array = [];
      
      public var index:int = 0;
      
      private var _uid:String = "";
      
      private var _uname:String = "";
      
      public var score:Number = 0;
      
      public var rank:Number = 0;
      
      public var extra:String = "";
      
      public var topDefineName:String = "";
      
      public function TopBarSave()
      {
         super();
         this.uid = "";
         this.uname = "";
      }
      
      public function set uid(str0:String) : void
      {
         this._uid = TextWay.toCode32(str0);
      }
      
      public function get uid() : String
      {
         return TextWay.getText32(this._uid);
      }
      
      public function set uname(str0:String) : void
      {
         this._uname = TextWay.toCode32(str0);
      }
      
      public function get uname() : String
      {
         return TextWay.getText32(this._uname);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         if(obj0.hasOwnProperty("uId"))
         {
            this.uid = obj0["uId"];
         }
      }
      
      public function getPCGId() : String
      {
         return this.uid + "_" + this.index;
      }
      
      public function getTopBarData() : TopBarData
      {
         var da0:TopBarData = new TopBarData();
         var d0:TopBarDefineGroup = Gaming.defineGroup.top.getDefine(this.topDefineName);
         da0.inData_byObjNoFlesh(this,d0);
         return da0;
      }
   }
}


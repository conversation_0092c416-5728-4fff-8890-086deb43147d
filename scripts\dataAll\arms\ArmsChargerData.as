package dataAll.arms
{
   import com.sounto.utils.ObjectMethod;
   import dataAll.arms.define.ArmsChargerDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.equip.EquipPropertyData;
   import dataAll.level.modeDiy.ModeDiyDefine;
   
   public class ArmsChargerData
   {
      protected var V:Number = Math.random() / 5 + 0.01;
      
      public var def:ArmsChargerDefine = null;
      
      private var _now:Number = 0;
      
      private var _max:Number = 0;
      
      public function ArmsChargerData()
      {
         super();
      }
      
      public function set now(v0:Number) : void
      {
         this._now = v0 / this.V;
      }
      
      public function get now() : Number
      {
         return Math.round(this._now * this.V);
      }
      
      private function set max(v0:Number) : void
      {
         this._max = v0 / this.V;
      }
      
      private function get max() : Number
      {
         return Math.round(this._max * this.V);
      }
      
      public function init(d0:ArmsChargerDefine) : void
      {
         this.def = d0;
         this.max = d0.baseCharger;
      }
      
      public function flesh_byEquip(mergeData0:EquipPropertyData) : void
      {
         if(!this.def)
         {
            INIT.showError("定义def为null");
         }
         var pro0:String = this.def.name;
         var value0:Number = ObjectMethod.getNumberEleIfHave(mergeData0,"charger_" + pro0);
         var mul0:Number = ObjectMethod.getNumberEleIfHave(mergeData0,"chargerMul_" + pro0);
         this.max = this.def.baseCharger * (1 + mul0 + mergeData0.chargerMul) + value0 + mergeData0.charger * ArmsType.getCapacityMul(this.def.name);
      }
      
      public function getMax() : int
      {
         var v0:int = 0;
         var diyD0:ModeDiyDefine = Gaming.LG.getModelDiyDefineNull();
         if(Boolean(diyD0))
         {
            if(diyD0.baseChargerMul >= 0)
            {
               return diyD0.getCharger(this);
            }
         }
         return this.max;
      }
      
      public function setMaxMul(mul0:Number) : void
      {
         this.max = Math.floor(mul0 * this.max);
      }
      
      public function fillAllData() : void
      {
         this.now = this.getMax();
      }
      
      public function addChargerNum(value0:Number, mulB0:Boolean = false, addType0:String = "add") : *
      {
         var max0:int = this.getMax();
         if(mulB0)
         {
            if(addType0 == "add")
            {
               this.now += Math.ceil(max0 * value0);
            }
            else if(addType0 == "set")
            {
               this.now = Math.ceil(max0 * value0);
            }
         }
         else if(addType0 == "add")
         {
            this.now += value0;
         }
         else if(addType0 == "set")
         {
            this.now = value0;
         }
         if(this.now >= max0)
         {
            this.now = max0;
         }
      }
   }
}


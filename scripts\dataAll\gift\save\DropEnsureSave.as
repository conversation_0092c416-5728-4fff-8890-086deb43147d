package dataAll.gift.save
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class DropEnsureSave
   {
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public function DropEnsureSave()
      {
         super();
         this.allTrigger = 0;
         this.trigger = 0;
         this.dropNum = 0;
      }
      
      public function get allTrigger() : Number
      {
         return this.CF.getAttribute("allTrigger");
      }
      
      public function set allTrigger(v0:Number) : void
      {
         this.CF.setAttribute("allTrigger",v0);
      }
      
      public function get trigger() : Number
      {
         return this.CF.getAttribute("trigger");
      }
      
      public function set trigger(v0:Number) : void
      {
         this.CF.setAttribute("trigger",v0);
      }
      
      public function get dropNum() : Number
      {
         return this.CF.getAttribute("dropNum");
      }
      
      public function set dropNum(v0:Number) : void
      {
         this.CF.setAttribute("dropNum",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function getDropIndex(dg0:GiftAddDefineGroup) : int
      {
         var nowPro0:Number = NaN;
         var mustNum0:int = 0;
         var triggerNum0:Number = NaN;
         var index0:int = 0;
         ++this.allTrigger;
         ++this.trigger;
         var definePro0:Number = dg0.getProByIndex(0);
         if(definePro0 > 0)
         {
            nowPro0 = definePro0;
            mustNum0 = 1 / definePro0;
            triggerNum0 = this.trigger;
            if(triggerNum0 > mustNum0)
            {
               if(triggerNum0 < mustNum0 * 2)
               {
                  nowPro0 = definePro0 * 4;
               }
               else
               {
                  nowPro0 = 0.1;
               }
               dg0 = dg0.clone();
               dg0.setProByIndex(0,nowPro0);
            }
            trace("概率：" + dg0.getProByIndex(0));
            index0 = dg0.getRandomIndex();
            if(index0 == 0)
            {
               ++this.dropNum;
               this.trigger = 0;
            }
            return index0;
         }
         return dg0.getRandomIndex();
      }
   }
}


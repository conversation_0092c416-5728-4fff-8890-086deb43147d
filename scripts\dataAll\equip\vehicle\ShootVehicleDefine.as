package dataAll.equip.vehicle
{
   import com.sounto.utils.ClassProperty;
   
   public class ShootVehicleDefine extends VehicleDefine
   {
      public static var pro_arr:Array = [];
      
      public static var mePro_arr:Array = [];
      
      public var main:VehicleBulletDefine = new VehicleBulletDefine();
      
      public var sub:VehicleBulletDefine = new VehicleBulletDefine();
      
      public var mainFrontB:Boolean = false;
      
      public var firstArmsIsSubB:Boolean = false;
      
      public function ShootVehicleDefine()
      {
         super();
      }
      
      override public function inData_byXML(xml0:XML, father0:String) : void
      {
         super.inData_byXML(xml0,father0);
         ClassProperty.inData_byXML(this,xml0,mePro_arr);
         this.main.inData_byXML(xml0.main[0],"main");
         this.sub.inData_byXML(xml0.sub[0],"sub");
         if(this.main.label == "")
         {
            this.main.label = name + "_main";
         }
         if(this.sub.label == "")
         {
            this.sub.label = name + "_sub";
         }
      }
      
      public function getNextBullet(nowName0:String) : VehicleBulletDefine
      {
         if(nowName0 != this.main.label || this.sub.hideB)
         {
            return this.main;
         }
         return this.sub;
      }
      
      public function getBulletByType(type0:String) : VehicleBulletDefine
      {
         if(type0 == this.main.type)
         {
            return this.main;
         }
         return this.sub;
      }
      
      override public function getBullletNum(type0:String) : int
      {
         if(type0 == "main")
         {
            return this.main.getBulletDefine().bulletNum;
         }
         if(type0 == "sub")
         {
            return this.sub.getBulletDefine().bulletNum;
         }
         if(type0 == "attack")
         {
            return attackBulletNum;
         }
         return 1;
      }
      
      public function getBulletTypeByLabel(name0:String) : String
      {
         if(this.main.label == name0)
         {
            return "main";
         }
         return "sub";
      }
      
      override public function getMulByLabel(name0:String) : Number
      {
         var bd0:VehicleBulletDefine = null;
         var type0:String = name0.substr(0,name0.length - 3);
         if(this.hasOwnProperty(name0))
         {
            return this[name0];
         }
         if(this.hasOwnProperty(type0))
         {
            if(this[type0] is VehicleBulletDefine)
            {
               bd0 = this[type0];
               return bd0.dpsMul;
            }
         }
         return 0;
      }
      
      override public function fitlerProCn(cn0:String) : String
      {
         return cn0.replace("普通","碾压");
      }
      
      override public function getPreBulletNameArr() : Array
      {
         var arr0:Array = [];
         arr0 = arr0.concat(this.main.getPreBulletNameArr());
         return arr0.concat(this.sub.getPreBulletNameArr());
      }
      
      override public function getSoundUrlArr() : Array
      {
         var arr0:Array = [];
         arr0 = arr0.concat(this.main.getSoundUrlArr());
         return arr0.concat(this.sub.getSoundUrlArr());
      }
   }
}

